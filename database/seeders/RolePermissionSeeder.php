<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Permission;
use App\Models\Role;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Permissions
        $permissions = [
            // User Management
            ['name' => 'View Users', 'slug' => 'users.view', 'module' => 'users', 'description' => 'View user list and details'],
            ['name' => 'Create Users', 'slug' => 'users.create', 'module' => 'users', 'description' => 'Create new users'],
            ['name' => 'Edit Users', 'slug' => 'users.edit', 'module' => 'users', 'description' => 'Edit existing users'],
            ['name' => 'Delete Users', 'slug' => 'users.delete', 'module' => 'users', 'description' => 'Delete users'],
            ['name' => 'Manage User Roles', 'slug' => 'users.roles', 'module' => 'users', 'description' => 'Assign roles to users'],

            // Company Management
            ['name' => 'View Companies', 'slug' => 'companies.view', 'module' => 'companies', 'description' => 'View company list and details'],
            ['name' => 'Create Companies', 'slug' => 'companies.create', 'module' => 'companies', 'description' => 'Create new companies'],
            ['name' => 'Edit Companies', 'slug' => 'companies.edit', 'module' => 'companies', 'description' => 'Edit existing companies'],
            ['name' => 'Delete Companies', 'slug' => 'companies.delete', 'module' => 'companies', 'description' => 'Delete companies'],
            ['name' => 'Switch Companies', 'slug' => 'companies.switch', 'module' => 'companies', 'description' => 'Switch between companies'],

            // Customer Management
            ['name' => 'View Customers', 'slug' => 'customers.view', 'module' => 'customers', 'description' => 'View customer list and details'],
            ['name' => 'Create Customers', 'slug' => 'customers.create', 'module' => 'customers', 'description' => 'Create new customers'],
            ['name' => 'Edit Customers', 'slug' => 'customers.edit', 'module' => 'customers', 'description' => 'Edit existing customers'],
            ['name' => 'Delete Customers', 'slug' => 'customers.delete', 'module' => 'customers', 'description' => 'Delete customers'],

            // Product Management
            ['name' => 'View Products', 'slug' => 'products.view', 'module' => 'products', 'description' => 'View product list and details'],
            ['name' => 'Create Products', 'slug' => 'products.create', 'module' => 'products', 'description' => 'Create new products'],
            ['name' => 'Edit Products', 'slug' => 'products.edit', 'module' => 'products', 'description' => 'Edit existing products'],
            ['name' => 'Delete Products', 'slug' => 'products.delete', 'module' => 'products', 'description' => 'Delete products'],

            // Category Management
            ['name' => 'View Categories', 'slug' => 'categories.view', 'module' => 'categories', 'description' => 'View category list and details'],
            ['name' => 'Create Categories', 'slug' => 'categories.create', 'module' => 'categories', 'description' => 'Create new categories'],
            ['name' => 'Edit Categories', 'slug' => 'categories.edit', 'module' => 'categories', 'description' => 'Edit existing categories'],
            ['name' => 'Delete Categories', 'slug' => 'categories.delete', 'module' => 'categories', 'description' => 'Delete categories'],

            // Sales Management
            ['name' => 'View Sales', 'slug' => 'sales.view', 'module' => 'sales', 'description' => 'View sales entries and details'],
            ['name' => 'Create Sales', 'slug' => 'sales.create', 'module' => 'sales', 'description' => 'Create new sales entries'],
            ['name' => 'Edit Sales', 'slug' => 'sales.edit', 'module' => 'sales', 'description' => 'Edit existing sales entries'],
            ['name' => 'Delete Sales', 'slug' => 'sales.delete', 'module' => 'sales', 'description' => 'Delete sales entries'],
            ['name' => 'Approve Sales', 'slug' => 'sales.approve', 'module' => 'sales', 'description' => 'Approve sales entries'],

            // Transport Management
            ['name' => 'View Transport', 'slug' => 'transport.view', 'module' => 'transport', 'description' => 'View transport entries and details'],
            ['name' => 'Create Transport', 'slug' => 'transport.create', 'module' => 'transport', 'description' => 'Create new transport entries'],
            ['name' => 'Edit Transport', 'slug' => 'transport.edit', 'module' => 'transport', 'description' => 'Edit existing transport entries'],
            ['name' => 'Delete Transport', 'slug' => 'transport.delete', 'module' => 'transport', 'description' => 'Delete transport entries'],

            // Reports
            ['name' => 'View Reports', 'slug' => 'reports.view', 'module' => 'reports', 'description' => 'View all reports'],
            ['name' => 'Export Reports', 'slug' => 'reports.export', 'module' => 'reports', 'description' => 'Export reports to various formats'],
            ['name' => 'Advanced Reports', 'slug' => 'reports.advanced', 'module' => 'reports', 'description' => 'Access advanced reporting features'],

            // Role & Permission Management
            ['name' => 'View Roles', 'slug' => 'roles.view', 'module' => 'roles', 'description' => 'View role list and details'],
            ['name' => 'Create Roles', 'slug' => 'roles.create', 'module' => 'roles', 'description' => 'Create new roles'],
            ['name' => 'Edit Roles', 'slug' => 'roles.edit', 'module' => 'roles', 'description' => 'Edit existing roles'],
            ['name' => 'Delete Roles', 'slug' => 'roles.delete', 'module' => 'roles', 'description' => 'Delete roles'],

            ['name' => 'View Permissions', 'slug' => 'permissions.view', 'module' => 'permissions', 'description' => 'View permission list and details'],
            ['name' => 'Create Permissions', 'slug' => 'permissions.create', 'module' => 'permissions', 'description' => 'Create new permissions'],
            ['name' => 'Edit Permissions', 'slug' => 'permissions.edit', 'module' => 'permissions', 'description' => 'Edit existing permissions'],
            ['name' => 'Delete Permissions', 'slug' => 'permissions.delete', 'module' => 'permissions', 'description' => 'Delete permissions'],

            // Settings
            ['name' => 'View Settings', 'slug' => 'settings.view', 'module' => 'settings', 'description' => 'View application settings'],
            ['name' => 'Edit Settings', 'slug' => 'settings.edit', 'module' => 'settings', 'description' => 'Edit application settings'],

            // Dashboard
            ['name' => 'View Dashboard', 'slug' => 'dashboard.view', 'module' => 'dashboard', 'description' => 'Access dashboard'],
            ['name' => 'View Analytics', 'slug' => 'dashboard.analytics', 'module' => 'dashboard', 'description' => 'View advanced analytics'],
            ['name' => 'View User Management', 'slug' => 'dashboard.user-management', 'module' => 'dashboard', 'description' => 'Access user management dashboard'],

            // Master Data Management
            ['name' => 'View States', 'slug' => 'states.view', 'module' => 'masters', 'description' => 'View state list and details'],
            ['name' => 'Create States', 'slug' => 'states.create', 'module' => 'masters', 'description' => 'Create new states'],
            ['name' => 'Edit States', 'slug' => 'states.edit', 'module' => 'masters', 'description' => 'Edit existing states'],
            ['name' => 'Delete States', 'slug' => 'states.delete', 'module' => 'masters', 'description' => 'Delete states'],

            ['name' => 'View Cities', 'slug' => 'cities.view', 'module' => 'masters', 'description' => 'View city list and details'],
            ['name' => 'Create Cities', 'slug' => 'cities.create', 'module' => 'masters', 'description' => 'Create new cities'],
            ['name' => 'Edit Cities', 'slug' => 'cities.edit', 'module' => 'masters', 'description' => 'Edit existing cities'],
            ['name' => 'Delete Cities', 'slug' => 'cities.delete', 'module' => 'masters', 'description' => 'Delete cities'],

            // Profile Management
            ['name' => 'View Profile', 'slug' => 'profile.view', 'module' => 'users', 'description' => 'View own profile'],
            ['name' => 'Edit Profile', 'slug' => 'profile.edit', 'module' => 'users', 'description' => 'Edit own profile'],

            // Export Operations
            ['name' => 'Export Users', 'slug' => 'users.export', 'module' => 'users', 'description' => 'Export user data'],

            // Bulk Operations
            ['name' => 'Bulk Actions Users', 'slug' => 'users.bulk', 'module' => 'users', 'description' => 'Perform bulk actions on users'],

            // Status Toggle Operations
            ['name' => 'Toggle User Status', 'slug' => 'users.toggle-status', 'module' => 'users', 'description' => 'Toggle user active/inactive status'],
            ['name' => 'Toggle Customer Status', 'slug' => 'customers.toggle-status', 'module' => 'customers', 'description' => 'Toggle customer active/inactive status'],
            ['name' => 'Toggle Product Status', 'slug' => 'products.toggle-status', 'module' => 'products', 'description' => 'Toggle product active/inactive status'],
            ['name' => 'Toggle Category Status', 'slug' => 'categories.toggle-status', 'module' => 'categories', 'description' => 'Toggle category active/inactive status'],
            ['name' => 'Toggle Role Status', 'slug' => 'roles.toggle-status', 'module' => 'roles', 'description' => 'Toggle role active/inactive status'],
            ['name' => 'Toggle Permission Status', 'slug' => 'permissions.toggle-status', 'module' => 'permissions', 'description' => 'Toggle permission active/inactive status'],

            // Additional Sales Operations
            ['name' => 'Print Sales', 'slug' => 'sales.print', 'module' => 'sales', 'description' => 'Print sales entries'],
            ['name' => 'Download Sales PDF', 'slug' => 'sales.pdf', 'module' => 'sales', 'description' => 'Download sales entries as PDF'],

            // Additional Transport Operations
            ['name' => 'Print Transport', 'slug' => 'transport.print', 'module' => 'transport', 'description' => 'Print transport entries'],
            ['name' => 'Download Transport PDF', 'slug' => 'transport.pdf', 'module' => 'transport', 'description' => 'Download transport entries as PDF'],

            // Permission Bulk Operations
            ['name' => 'Bulk Create Permissions', 'slug' => 'permissions.bulk-create', 'module' => 'permissions', 'description' => 'Create multiple permissions at once'],

            // Menu Access Permissions
            ['name' => 'Access Masters Menu', 'slug' => 'menu.masters', 'module' => 'menu', 'description' => 'Access to Masters menu in sidebar'],
            ['name' => 'Access Sales Menu', 'slug' => 'menu.sales', 'module' => 'menu', 'description' => 'Access to Sales menu in sidebar'],
            ['name' => 'Access Transport Menu', 'slug' => 'menu.transport', 'module' => 'menu', 'description' => 'Access to Transport menu in sidebar'],
            ['name' => 'Access Reports Menu', 'slug' => 'menu.reports', 'module' => 'menu', 'description' => 'Access to Reports menu in sidebar'],
            ['name' => 'Access Administration Menu', 'slug' => 'menu.administration', 'module' => 'menu', 'description' => 'Access to Administration menu in sidebar'],
            ['name' => 'Access Settings Menu', 'slug' => 'menu.settings', 'module' => 'menu', 'description' => 'Access to Settings menu in sidebar'],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['slug' => $permission['slug']],
                $permission
            );
        }

        // Create Roles
        $roles = [
            [
                'name' => 'Super Admin',
                'slug' => 'super-admin',
                'description' => 'Full system access with all permissions',
                'status' => 'active'
            ],
            [
                'name' => 'Admin',
                'slug' => 'admin',
                'description' => 'Administrative access with most permissions',
                'status' => 'active'
            ],
            [
                'name' => 'Manager',
                'slug' => 'manager',
                'description' => 'Management level access with limited permissions',
                'status' => 'active'
            ],
            [
                'name' => 'User',
                'slug' => 'user',
                'description' => 'Basic user access with minimal permissions',
                'status' => 'active'
            ],
        ];

        foreach ($roles as $roleData) {
            $role = Role::firstOrCreate(
                ['slug' => $roleData['slug']],
                $roleData
            );

            // Assign permissions to roles
            $this->assignPermissionsToRole($role);
        }
    }

    private function assignPermissionsToRole($role)
    {
        $allPermissions = Permission::all();

        switch ($role->slug) {
            case 'super-admin':
                // Super Admin gets all permissions
                $role->permissions()->sync($allPermissions->pluck('id'));
                break;

            case 'admin':
                // Admin gets most permissions except role/permission management
                $adminPermissions = $allPermissions->reject(function ($permission) {
                    return in_array($permission->module, ['roles', 'permissions']) ||
                           in_array($permission->slug, ['companies.delete', 'users.delete']);
                });
                $role->permissions()->sync($adminPermissions->pluck('id'));
                break;

            case 'manager':
                // Manager gets business operation permissions
                $managerPermissions = $allPermissions->filter(function ($permission) {
                    return in_array($permission->module, [
                        'dashboard', 'customers', 'products', 'categories',
                        'sales', 'transport', 'reports', 'masters', 'menu'
                    ]) && !str_contains($permission->slug, '.delete') &&
                    !in_array($permission->slug, ['menu.administration', 'menu.settings']);
                });
                $role->permissions()->sync($managerPermissions->pluck('id'));
                break;

            case 'user':
                // User gets basic view and create permissions
                $userPermissions = $allPermissions->filter(function ($permission) {
                    return in_array($permission->slug, [
                        'dashboard.view',
                        'customers.view', 'customers.create',
                        'products.view',
                        'sales.view', 'sales.create', 'sales.print',
                        'transport.view', 'transport.create', 'transport.print',
                        'reports.view',
                        'profile.view', 'profile.edit',
                        'states.view', 'cities.view',
                        'menu.masters', 'menu.sales', 'menu.transport', 'menu.reports'
                    ]);
                });
                $role->permissions()->sync($userPermissions->pluck('id'));
                break;
        }
    }
}
