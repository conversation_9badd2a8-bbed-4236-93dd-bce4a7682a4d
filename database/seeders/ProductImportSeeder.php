<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Product;
use App\Models\Category;

class ProductImportSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get or create the category (assuming category_id 1 exists)
        $category = Category::find(1);
        if (!$category) {
            $category = Category::create([
                'name' => 'Steel Sheets',
                'description' => 'Various types of steel sheets and profiles',
                'status' => 'active'
            ]);
        }

        $products = [
            [
                'name' => 'COLOURED PROFILE SHEET 0.40 MM',
                'category_id' => $category->id,
                'unit' => 'MT',
                'hsn_code' => '7210',
                'gst_percentage' => 18.00,
                'rate' => 0.00,
                'price' => 0.00,
            ],
            [
                'name' => 'COLOURED PROFILE SHEET 0.50 MM',
                'category_id' => $category->id,
                'unit' => 'MT',
                'hsn_code' => '7210',
                'gst_percentage' => 18.00,
                'rate' => 0.00,
                'price' => 0.00,
            ],
            [
                'name' => 'CR SHEET 0.30 MM',
                'category_id' => $category->id,
                'unit' => 'MT',
                'hsn_code' => '7209',
                'gst_percentage' => 18.00,
                'rate' => 0.00,
                'price' => 0.00,
            ],
            [
                'name' => 'CR SHEET 0.35 MM',
                'category_id' => $category->id,
                'unit' => 'MT',
                'hsn_code' => '7209',
                'gst_percentage' => 18.00,
                'rate' => 0.00,
                'price' => 0.00,
            ],
            [
                'name' => 'CR SHEET 0.40 MM',
                'category_id' => $category->id,
                'unit' => 'MT',
                'hsn_code' => '7209',
                'gst_percentage' => 18.00,
                'rate' => 0.00,
                'price' => 0.00,
            ],
            [
                'name' => 'CR SHEET 0.50 MM',
                'category_id' => $category->id,
                'unit' => 'MT',
                'hsn_code' => '7209',
                'gst_percentage' => 18.00,
                'rate' => 0.00,
                'price' => 0.00,
            ],
            [
                'name' => 'CR SHEET 0.60 MM',
                'category_id' => $category->id,
                'unit' => 'MT',
                'hsn_code' => '7209',
                'gst_percentage' => 18.00,
                'rate' => 0.00,
                'price' => 0.00,
            ],
            [
                'name' => 'CR SHEET 0.80 MM',
                'category_id' => $category->id,
                'unit' => 'MT',
                'hsn_code' => '7209',
                'gst_percentage' => 18.00,
                'rate' => 0.00,
                'price' => 0.00,
            ],
            [
                'name' => 'CR SHEET 1.00 MM',
                'category_id' => $category->id,
                'unit' => 'MT',
                'hsn_code' => '7209',
                'gst_percentage' => 18.00,
                'rate' => 0.00,
                'price' => 0.00,
            ],
            [
                'name' => 'CR SHEET 1.20 MM',
                'category_id' => $category->id,
                'unit' => 'MT',
                'hsn_code' => '7209',
                'gst_percentage' => 18.00,
                'rate' => 0.00,
                'price' => 0.00,
            ],
            [
                'name' => 'CR SHEET 1.40 MM',
                'category_id' => $category->id,
                'unit' => 'MT',
                'hsn_code' => '7209',
                'gst_percentage' => 18.00,
                'rate' => 0.00,
                'price' => 0.00,
            ],
            [
                'name' => 'CR SHEET 1.50 MM',
                'category_id' => $category->id,
                'unit' => 'MT',
                'hsn_code' => '7209',
                'gst_percentage' => 18.00,
                'rate' => 0.00,
                'price' => 0.00,
            ],
            [
                'name' => 'CR SHEET 1.60 MM',
                'category_id' => $category->id,
                'unit' => 'MT',
                'hsn_code' => '7209',
                'gst_percentage' => 18.00,
                'rate' => 0.00,
                'price' => 0.00,
            ],
            [
                'name' => 'CR SHEET 1.80 MM',
                'category_id' => $category->id,
                'unit' => 'MT',
                'hsn_code' => '7209',
                'gst_percentage' => 18.00,
                'rate' => 0.00,
                'price' => 0.00,
            ],
            [
                'name' => 'CR SHEET 2.00 MM',
                'category_id' => $category->id,
                'unit' => 'MT',
                'hsn_code' => '7209',
                'gst_percentage' => 18.00,
                'rate' => 0.00,
                'price' => 0.00,
            ],
        ];

        $this->command->info('Starting product import...');
        $imported = 0;
        $skipped = 0;

        foreach ($products as $index => $productData) {
            $productData['stock_quantity'] = 0;
            $productData['status'] = 'active';
            $productData['description'] = 'Imported steel sheet product';

            // Check if product already exists
            $existingProduct = Product::where('name', $productData['name'])->first();
            
            if ($existingProduct) {
                $this->command->warn("Product '{$productData['name']}' already exists. Skipping...");
                $skipped++;
                continue;
            }

            try {
                Product::create($productData);
                $this->command->info("✓ Imported: {$productData['name']}");
                $imported++;
            } catch (\Exception $e) {
                $this->command->error("✗ Failed to import: {$productData['name']} - {$e->getMessage()}");
            }
        }

        $this->command->info("\n=== Import Summary ===");
        $this->command->info("Imported: {$imported} products");
        $this->command->info("Skipped: {$skipped} products");
        $this->command->info("Total processed: " . ($imported + $skipped));
    }


}
