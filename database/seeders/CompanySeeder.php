<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Company;
use App\Models\User;

class CompanySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample companies
        $company1 = Company::updateOrCreate(
            ['code' => 'JMD001'],
            [
                'name' => 'JMD Traders',
                'email' => '<EMAIL>',
                'phone' => '+91 9876543210',
                'address' => '123 Business Street',
                'gst_number' => 'GST123456789',
                'status' => 'active'
            ]
        );

        $company2 = Company::updateOrCreate(
            ['code' => 'ABC001'],
            [
                'name' => 'ABC Enterprises',
                'email' => '<EMAIL>',
                'phone' => '+91 9876543211',
                'address' => '456 Commerce Road',
                'gst_number' => 'GST987654321',
                'status' => 'active'
            ]
        );

        // Get the first user (admin) and assign to both companies
        $adminUser = User::first();
        $adminRole = \App\Models\Role::where('slug', 'admin')->first();

        if ($adminUser && $adminRole) {
            // Assign admin to both companies (sync to avoid duplicates)
            if (!$company1->users()->where('user_id', $adminUser->id)->exists()) {
                $company1->users()->attach($adminUser->id, [
                    'role' => 'admin',
                    'role_id' => $adminRole->id,
                    'status' => 'active'
                ]);
            }

            if (!$company2->users()->where('user_id', $adminUser->id)->exists()) {
                $company2->users()->attach($adminUser->id, [
                    'role' => 'admin',
                    'role_id' => $adminRole->id,
                    'status' => 'active'
                ]);
            }

            // Set the first company as current company for admin
            $adminUser->update(['current_company_id' => $company1->id]);
        }

        // Create additional users for testing
        $managerRole = \App\Models\Role::where('slug', 'manager')->first();
        $userRole = \App\Models\Role::where('slug', 'user')->first();

        $manager = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Manager User',
                'password' => bcrypt('password'),
                'status' => 'active',
                'current_company_id' => $company1->id
            ]
        );

        if ($managerRole && !$company1->users()->where('user_id', $manager->id)->exists()) {
            $company1->users()->attach($manager->id, [
                'role' => 'manager',
                'role_id' => $managerRole->id,
                'status' => 'active'
            ]);
        }

        $user = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Regular User',
                'password' => bcrypt('password'),
                'status' => 'active',
                'current_company_id' => $company2->id
            ]
        );

        if ($userRole && !$company2->users()->where('user_id', $user->id)->exists()) {
            $company2->users()->attach($user->id, [
                'role' => 'user',
                'role_id' => $userRole->id,
                'status' => 'active'
            ]);
        }
    }
}
