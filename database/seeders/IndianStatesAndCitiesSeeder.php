<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Artisan;

class IndianStatesAndCitiesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🇮🇳 Seeding Indian States and Cities...');
        
        // Call the import command
        Artisan::call('import:indian-states-cities', [
            '--force' => true
        ]);
        
        $output = Artisan::output();
        $this->command->info($output);
        
        $this->command->info('✅ Indian States and Cities seeding completed!');
    }
}
