<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Role;
use App\Models\Company;

class UserRoleAssignmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🚀 Starting User Role Assignment...');

        // Ensure roles exist
        $this->ensureRolesExist();

        // Get all users and roles
        $users = User::all();
        $roles = Role::all()->keyBy('slug');
        $companies = Company::all();

        if ($users->isEmpty()) {
            $this->command->warn('⚠️  No users found to assign roles');
            return;
        }

        $this->command->info("📊 Found {$users->count()} users to process");

        foreach ($users as $user) {
            $this->assignRolesToUser($user, $roles, $companies);
        }

        $this->command->info('✅ User role assignment completed!');
    }

    /**
     * Ensure required roles exist
     */
    private function ensureRolesExist()
    {
        $requiredRoles = ['super-admin', 'admin', 'manager', 'user'];
        $existingRoles = Role::whereIn('slug', $requiredRoles)->pluck('slug')->toArray();
        $missingRoles = array_diff($requiredRoles, $existingRoles);

        if (!empty($missingRoles)) {
            $this->command->error('❌ Missing required roles: ' . implode(', ', $missingRoles));
            $this->command->error('Please run: php artisan db:seed --class=RolePermissionSeeder');
            throw new \Exception('Required roles not found');
        }
    }

    /**
     * Assign roles to a specific user
     */
    private function assignRolesToUser($user, $roles, $companies)
    {
        $this->command->line("👤 Processing: {$user->name} ({$user->email})");

        // 1. Assign global role if not set
        $this->assignGlobalRole($user, $roles);

        // 2. Assign company roles
        $this->assignCompanyRoles($user, $roles, $companies);

        // 3. Set current company if not set
        $this->setCurrentCompany($user, $companies);
    }

    /**
     * Assign global role to user
     */
    private function assignGlobalRole($user, $roles)
    {
        if ($user->role_id) {
            $this->command->comment("   ℹ️  Already has global role");
            return;
        }

        $globalRole = $this->determineGlobalRole($user, $roles);
        
        $user->update(['role_id' => $globalRole->id]);
        
        $this->command->info("   ✅ Assigned global role: {$globalRole->name}");
    }

    /**
     * Determine appropriate global role for user
     */
    private function determineGlobalRole($user, $roles)
    {
        // Check email patterns for admin roles
        $adminPatterns = [
            'admin@', 'administrator@', 'superadmin@', 'super-admin@',
            'root@', 'system@', 'owner@'
        ];

        $email = strtolower($user->email);
        
        foreach ($adminPatterns as $pattern) {
            if (str_contains($email, $pattern)) {
                return $roles['super-admin'];
            }
        }

        // Check legacy role field
        if (isset($user->role)) {
            switch (strtolower($user->role)) {
                case 'super-admin':
                case 'superadmin':
                    return $roles['super-admin'];
                case 'admin':
                case 'administrator':
                    return $roles['admin'];
                case 'manager':
                    return $roles['manager'];
            }
        }

        // Check name patterns
        $name = strtolower($user->name);
        if (str_contains($name, 'admin') || str_contains($name, 'administrator')) {
            return $roles['admin'];
        }

        if (str_contains($name, 'manager')) {
            return $roles['manager'];
        }

        // Default to user role
        return $roles['user'];
    }

    /**
     * Assign company roles to user
     */
    private function assignCompanyRoles($user, $roles, $companies)
    {
        if ($companies->isEmpty()) {
            $this->command->comment("   ℹ️  No companies found for assignment");
            return;
        }

        foreach ($companies as $company) {
            $this->assignUserToCompany($user, $company, $roles);
        }
    }

    /**
     * Assign user to specific company
     */
    private function assignUserToCompany($user, $company, $roles)
    {
        // Check if user is already assigned to this company
        $existingAssignment = $user->companies()->where('companies.id', $company->id)->first();
        
        if ($existingAssignment) {
            $this->command->comment("   ℹ️  Already assigned to: {$company->name}");
            return;
        }

        // Determine company role based on global role
        $companyRole = $this->determineCompanyRole($user, $roles);

        // Assign user to company
        $user->companies()->attach($company->id, [
            'role' => $companyRole->slug,
            'role_id' => $companyRole->id,
            'status' => 'active',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        $this->command->info("   ✅ Assigned to company: {$company->name} as {$companyRole->name}");
    }

    /**
     * Determine company role based on user's global role
     */
    private function determineCompanyRole($user, $roles)
    {
        $globalRole = $user->userRole ?? $roles['user'];

        // Map global roles to company roles
        switch ($globalRole->slug) {
            case 'super-admin':
                return $roles['admin']; // Super admin becomes admin in companies
            case 'admin':
                return $roles['admin'];
            case 'manager':
                return $roles['manager'];
            default:
                return $roles['user'];
        }
    }

    /**
     * Set current company for user if not set
     */
    private function setCurrentCompany($user, $companies)
    {
        if ($user->current_company_id || $companies->isEmpty()) {
            return;
        }

        // Get user's first company assignment
        $userCompany = $user->companies()->first();
        
        if ($userCompany) {
            $user->update(['current_company_id' => $userCompany->id]);
            $this->command->info("   ✅ Set current company: {$userCompany->name}");
        } else {
            // Assign to first available company
            $firstCompany = $companies->first();
            $defaultRole = Role::where('slug', 'user')->first();
            
            $user->companies()->attach($firstCompany->id, [
                'role' => $defaultRole->slug,
                'role_id' => $defaultRole->id,
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            
            $user->update(['current_company_id' => $firstCompany->id]);
            $this->command->info("   ✅ Assigned and set current company: {$firstCompany->name}");
        }
    }
}
