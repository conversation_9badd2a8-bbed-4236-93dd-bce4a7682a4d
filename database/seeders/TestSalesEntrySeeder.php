<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SalesEntry;
use App\Models\SalesEntryItem;
use App\Models\Customer;
use App\Models\User;
use App\Models\Company;
use App\Models\Product;

class TestSalesEntrySeeder extends Seeder
{
    public function run()
    {
        // Check if we already have sales entries
        if (SalesEntry::count() > 0) {
            echo "Sales entries already exist.\n";
            return;
        }

        // Get required data
        $company = Company::first();
        $customers = Customer::take(3)->get();
        $user = User::first();
        $products = Product::take(5)->get();

        if (!$company || $customers->isEmpty() || !$user || $products->isEmpty()) {
            echo "Required data not found. Please run other seeders first.\n";
            return;
        }

        // Create sample sales entries with items
        $salesEntriesData = [
            [
                'entry' => [
                    'company_id' => $company->id,
                    'customer_id' => $customers[0]->id,
                    'user_id' => $user->id,
                    'invoice_number' => 'INV-2025-001',
                    'quotation_number' => 'QT-2025-001',
                    'invoice_date' => now(),
                    'quotation_date' => now(),
                    'payment_terms' => '30 days',
                    'party_name' => $customers[0]->name,
                    'mobile' => $customers[0]->phone,
                    'email' => $customers[0]->email,
                    'status' => 'approved',
                    'notes' => 'Sample sales entry with multiple items',
                    'valid_until' => now()->addDays(30),
                ],
                'items' => [
                    ['product_id' => $products[0]->id, 'quantity' => 2, 'rate' => $products[0]->rate],
                    ['product_id' => $products[1]->id, 'quantity' => 5, 'rate' => $products[1]->rate],
                ]
            ],
            [
                'entry' => [
                    'company_id' => $company->id,
                    'customer_id' => $customers[1]->id,
                    'user_id' => $user->id,
                    'invoice_number' => 'INV-2025-002',
                    'quotation_number' => 'QT-2025-002',
                    'invoice_date' => now()->subDays(5),
                    'quotation_date' => now()->subDays(5),
                    'payment_terms' => '15 days',
                    'party_name' => $customers[1]->name,
                    'mobile' => $customers[1]->phone,
                    'email' => $customers[1]->email,
                    'status' => 'pending',
                    'notes' => 'Pending approval from customer',
                    'valid_until' => now()->addDays(25),
                ],
                'items' => [
                    ['product_id' => $products[2]->id, 'quantity' => 3, 'rate' => $products[2]->rate],
                    ['product_id' => $products[3]->id, 'quantity' => 1, 'rate' => $products[3]->rate],
                ]
            ],
            [
                'entry' => [
                    'company_id' => $company->id,
                    'customer_id' => $customers[2]->id,
                    'user_id' => $user->id,
                    'invoice_number' => 'INV-2025-003',
                    'quotation_number' => 'QT-2025-003',
                    'invoice_date' => now()->subDays(10),
                    'quotation_date' => now()->subDays(10),
                    'payment_terms' => '45 days',
                    'party_name' => $customers[2]->name,
                    'mobile' => $customers[2]->phone,
                    'email' => $customers[2]->email,
                    'status' => 'approved',
                    'notes' => 'Large order with software licenses',
                    'valid_until' => now()->addDays(35),
                ],
                'items' => [
                    ['product_id' => $products[4]->id, 'quantity' => 10, 'rate' => $products[4]->rate],
                ]
            ]
        ];

        foreach ($salesEntriesData as $data) {
            // Calculate totals
            $subtotal = 0;
            foreach ($data['items'] as $item) {
                $subtotal += $item['quantity'] * $item['rate'];
            }

            $cgst = $subtotal * 0.09; // 9% CGST
            $sgst = $subtotal * 0.09; // 9% SGST
            $grandTotal = $subtotal + $cgst + $sgst;

            // Create sales entry
            $salesEntry = SalesEntry::create(array_merge($data['entry'], [
                'subtotal' => $subtotal,
                'total_cgst' => $cgst,
                'total_sgst' => $sgst,
                'grand_total' => $grandTotal,
                'net_amount' => $grandTotal,
            ]));

            // Create sales entry items
            foreach ($data['items'] as $itemData) {
                $product = $products->find($itemData['product_id']);
                $lineTotal = $itemData['quantity'] * $itemData['rate'];
                $cgstAmount = $lineTotal * 0.09; // 9% CGST
                $sgstAmount = $lineTotal * 0.09; // 9% SGST

                SalesEntryItem::create([
                    'sales_entry_id' => $salesEntry->id,
                    'product_id' => $itemData['product_id'],
                    'quantity' => $itemData['quantity'],
                    'unit_price' => $itemData['rate'],
                    'rate' => $itemData['rate'],
                    'basic_rate' => $itemData['rate'],
                    'total' => $lineTotal,
                    'line_total' => $lineTotal,
                    'taxable_value' => $lineTotal,
                    'hsn_code' => $product->hsn_code ?? '',
                    'unit' => $product->unit ?? 'Piece',
                    'packages' => $itemData['quantity'],
                    'gst_percent' => $product->gst_percentage ?? 18.00,
                    'cgst_percent' => 9.00,
                    'cgst_amount' => $cgstAmount,
                    'sgst_percent' => 9.00,
                    'sgst_amount' => $sgstAmount,
                    'description' => $product->description ?? $product->name,
                    'discount' => 0.00,
                ]);
            }
        }

        echo "Created " . count($salesEntriesData) . " test sales entries with items.\n";
    }
}
