<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Category;
use App\Models\State;
use App\Models\City;
use App\Models\Product;
use App\Models\Customer;

class DatabaseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        $adminUser = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'password' => Hash::make('admin123'),
                'status' => 'active',
                'email_verified_at' => now(),
            ]
        );

        // Create sample categories
        $categories = [
            ['name' => 'Electronics', 'description' => 'Electronic products and components'],
            ['name' => 'Hardware', 'description' => 'Hardware tools and equipment'],
            ['name' => 'Software', 'description' => 'Software products and licenses'],
        ];

        foreach ($categories as $category) {
            Category::updateOrCreate(['name' => $category['name']], $category);
        }

        // Create sample states
        $states = [
            ['name' => 'Gujarat', 'code' => 'GJ'],
            ['name' => 'Maharashtra', 'code' => 'MH'],
            ['name' => 'Rajasthan', 'code' => 'RJ'],
        ];

        foreach ($states as $state) {
            State::updateOrCreate(['name' => $state['name']], $state);
        }

        // Create sample cities
        $cities = [
            ['name' => 'Ahmedabad', 'state_id' => 1],
            ['name' => 'Surat', 'state_id' => 1],
            ['name' => 'Mumbai', 'state_id' => 2],
            ['name' => 'Pune', 'state_id' => 2],
            ['name' => 'Jaipur', 'state_id' => 3],
        ];

        foreach ($cities as $city) {
            City::updateOrCreate(['name' => $city['name'], 'state_id' => $city['state_id']], $city);
        }

        // Run role and permission seeder
        $this->call(RolePermissionSeeder::class);

        // Run company seeder
        $this->call(CompanySeeder::class);

        // Assign super-admin role to the admin user in the default company
        $this->assignSuperAdminRole($adminUser);

        // Seed products
        $this->seedProducts();

        // Seed customers
        $this->seedCustomers();

        // Run additional seeders
        $this->call([
            IndianStatesAndCitiesSeeder::class,
            WatermarkSeeder::class,
            TestSalesEntrySeeder::class,
            TransportEntrySeeder::class,
        ]);
    }

    /**
     * Assign super-admin role to the admin user in the default company
     */
    private function assignSuperAdminRole(User $adminUser): void
    {
        // Get the super-admin role
        $superAdminRole = \App\Models\Role::where('slug', 'super-admin')->first();

        // Get the default company (first company created)
        $defaultCompany = \App\Models\Company::first();

        if ($superAdminRole && $defaultCompany) {
            // Check if the user is already assigned to this company
            $existingAssignment = DB::table('company_users')
                ->where('user_id', $adminUser->id)
                ->where('company_id', $defaultCompany->id)
                ->first();

            if ($existingAssignment) {
                // Update existing assignment with super-admin role
                DB::table('company_users')
                    ->where('user_id', $adminUser->id)
                    ->where('company_id', $defaultCompany->id)
                    ->update([
                        'role_id' => $superAdminRole->id,
                        'role' => 'super-admin',
                        'status' => 'active',
                        'is_default' => true,
                        'updated_at' => now(),
                    ]);
            } else {
                // Create new assignment
                DB::table('company_users')->insert([
                    'user_id' => $adminUser->id,
                    'company_id' => $defaultCompany->id,
                    'role_id' => $superAdminRole->id,
                    'role' => 'super-admin',
                    'status' => 'active',
                    'is_default' => true,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            // Set the default company for the user
            $adminUser->update(['current_company_id' => $defaultCompany->id]);
        }
    }

    /**
     * Seed sample products
     */
    private function seedProducts(): void
    {
        $products = [
            [
                'name' => 'Laptop Computer',
                'category_id' => 1, // Electronics
                'hsn_code' => '8471',
                'gst_percentage' => 18.00,
                'unit' => 'Piece',
                'rate' => 45000.00,
                'price' => 50000.00,
                'stock_quantity' => 25,
                'description' => 'High-performance laptop for business use',
                'status' => 'active'
            ],
            [
                'name' => 'Wireless Mouse',
                'category_id' => 1, // Electronics
                'hsn_code' => '8471',
                'gst_percentage' => 18.00,
                'unit' => 'Piece',
                'rate' => 800.00,
                'price' => 1000.00,
                'stock_quantity' => 100,
                'description' => 'Ergonomic wireless mouse',
                'status' => 'active'
            ],
            [
                'name' => 'Mechanical Keyboard',
                'category_id' => 1, // Electronics
                'hsn_code' => '8471',
                'gst_percentage' => 18.00,
                'unit' => 'Piece',
                'rate' => 2500.00,
                'price' => 3000.00,
                'stock_quantity' => 50,
                'description' => 'RGB mechanical gaming keyboard',
                'status' => 'active'
            ],
            [
                'name' => 'Screwdriver Set',
                'category_id' => 2, // Hardware
                'hsn_code' => '8205',
                'gst_percentage' => 18.00,
                'unit' => 'Set',
                'rate' => 500.00,
                'price' => 650.00,
                'stock_quantity' => 75,
                'description' => 'Professional screwdriver set with multiple bits',
                'status' => 'active'
            ],
            [
                'name' => 'Power Drill',
                'category_id' => 2, // Hardware
                'hsn_code' => '8467',
                'gst_percentage' => 18.00,
                'unit' => 'Piece',
                'rate' => 3500.00,
                'price' => 4200.00,
                'stock_quantity' => 20,
                'description' => 'Cordless power drill with battery',
                'status' => 'active'
            ],
            [
                'name' => 'Antivirus Software',
                'category_id' => 3, // Software
                'hsn_code' => '9985',
                'gst_percentage' => 18.00,
                'unit' => 'License',
                'rate' => 1500.00,
                'price' => 2000.00,
                'stock_quantity' => 200,
                'description' => '1-year antivirus software license',
                'status' => 'active'
            ],
            [
                'name' => 'Office Suite',
                'category_id' => 3, // Software
                'hsn_code' => '9985',
                'gst_percentage' => 18.00,
                'unit' => 'License',
                'rate' => 8000.00,
                'price' => 10000.00,
                'stock_quantity' => 50,
                'description' => 'Complete office productivity suite',
                'status' => 'active'
            ]
        ];

        foreach ($products as $productData) {
            Product::updateOrCreate(
                ['name' => $productData['name']],
                $productData
            );
        }
    }

    /**
     * Seed sample customers
     */
    private function seedCustomers(): void
    {
        $defaultCompany = \App\Models\Company::first();

        if (!$defaultCompany) {
            return;
        }

        $customers = [
            [
                'company_id' => $defaultCompany->id,
                'name' => 'Tech Solutions Pvt Ltd',
                'email' => '<EMAIL>',
                'phone' => '**********',
                'address' => '123 Tech Park, Sector 5',
                'state_id' => 1, // Gujarat
                'city_id' => 1, // Ahmedabad
                'pincode' => '380001',
                'gst_number' => '24AAAAA0000A1Z5',
                'status' => 'active'
            ],
            [
                'company_id' => $defaultCompany->id,
                'name' => 'Digital Innovations Ltd',
                'email' => '<EMAIL>',
                'phone' => '9876543211',
                'address' => '456 Business Center, Ring Road',
                'state_id' => 1, // Gujarat
                'city_id' => 2, // Surat
                'pincode' => '395001',
                'gst_number' => '24BBBBB0000B1Z5',
                'status' => 'active'
            ],
            [
                'company_id' => $defaultCompany->id,
                'name' => 'Mumbai Enterprises',
                'email' => '<EMAIL>',
                'phone' => '9876543212',
                'address' => '789 Commercial Complex, Andheri',
                'state_id' => 2, // Maharashtra
                'city_id' => 3, // Mumbai
                'pincode' => '400001',
                'gst_number' => '27CCCCC0000C1Z5',
                'status' => 'active'
            ],
            [
                'company_id' => $defaultCompany->id,
                'name' => 'Pune Software Solutions',
                'email' => '<EMAIL>',
                'phone' => '9876543213',
                'address' => '321 IT Park, Hinjewadi',
                'state_id' => 2, // Maharashtra
                'city_id' => 4, // Pune
                'pincode' => '411057',
                'gst_number' => '27DDDDD0000D1Z5',
                'status' => 'active'
            ],
            [
                'company_id' => $defaultCompany->id,
                'name' => 'Rajasthan Trading Co',
                'email' => '<EMAIL>',
                'phone' => '9876543214',
                'address' => '654 Market Street, Civil Lines',
                'state_id' => 3, // Rajasthan
                'city_id' => 5, // Jaipur
                'pincode' => '302001',
                'gst_number' => '08EEEEE0000E1Z5',
                'status' => 'active'
            ]
        ];

        foreach ($customers as $customerData) {
            Customer::updateOrCreate(
                ['email' => $customerData['email']],
                $customerData
            );
        }
    }
}
