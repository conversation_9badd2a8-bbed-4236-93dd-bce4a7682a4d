<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\TransportEntry;
use App\Models\SalesEntry;
use App\Models\Customer;
use App\Models\User;
use App\Models\Company;

class TransportEntrySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if we already have transport entries
        if (TransportEntry::count() > 0) {
            echo "Transport entries already exist.\n";
            return;
        }

        // Get required data
        $company = Company::first();
        $salesEntries = SalesEntry::take(3)->get();
        $customers = Customer::take(3)->get();
        $user = User::first();

        if (!$company || $salesEntries->isEmpty() || $customers->isEmpty() || !$user) {
            echo "Required data not found. Please run other seeders first.\n";
            return;
        }

        // Create sample transport entries
        $transportEntries = [
            [
                'sales_entry_id' => $salesEntries[0]->id ?? null,
                'user_id' => $user->id,
                'customer_id' => $customers[0]->id,
                'company_id' => $company->id,
                'transport_number' => 'TRN-2025-001',
                'transport_date' => now(),
                'due_days' => 30,
                'due_date' => now()->addDays(30),
                'freight_cond' => 'Paid',
                'way_bill_no' => 'WB001',
                'material_direct_delivered_from' => 'Warehouse A',
                'lr_no' => 'LR001',
                'goods_insured_by' => 'ABC Insurance',
                'eway_bill_no' => 'EWB001',
                'doc_through' => 'Courier',
                'policy_no' => 'POL001',
                'eway_bill_date' => now(),
                'driver_name' => 'Rajesh Kumar',
                'driver_mob_no' => '9876543210',
                'dl_no' => 'DL001',
                'from_city' => 'Ahmedabad',
                'from_location' => 'Warehouse A, Sector 5',
                'destination' => 'Mumbai',
                'to_city' => 'Mumbai',
                'to_location' => 'Customer Site, Andheri',
                'delivery_1' => 'Standard Delivery',
                'prepared_by' => $user->name,
                'remarks' => 'Handle with care - electronic items',
                'gross_wt' => '50.5',
                'net_wt' => '45.0',
                'packages' => 5,
                'status' => 'scheduled'
            ],
            [
                'sales_entry_id' => $salesEntries[1]->id ?? null,
                'user_id' => $user->id,
                'customer_id' => $customers[1]->id,
                'company_id' => $company->id,
                'transport_number' => 'TRN-2025-002',
                'transport_date' => now()->subDays(3),
                'due_days' => 15,
                'due_date' => now()->addDays(12),
                'freight_cond' => 'To Pay',
                'way_bill_no' => 'WB002',
                'material_direct_delivered_from' => 'Warehouse B',
                'lr_no' => 'LR002',
                'goods_insured_by' => 'XYZ Insurance',
                'eway_bill_no' => 'EWB002',
                'doc_through' => 'Hand Delivery',
                'policy_no' => 'POL002',
                'eway_bill_date' => now()->subDays(3),
                'driver_name' => 'Suresh Patel',
                'driver_mob_no' => '9876543211',
                'dl_no' => 'DL002',
                'from_city' => 'Surat',
                'from_location' => 'Warehouse B, Industrial Area',
                'destination' => 'Pune',
                'to_city' => 'Pune',
                'to_location' => 'IT Park, Hinjewadi',
                'delivery_1' => 'Express Delivery',
                'prepared_by' => $user->name,
                'remarks' => 'Urgent delivery required',
                'gross_wt' => '25.0',
                'net_wt' => '22.5',
                'packages' => 3,
                'status' => 'in_transit'
            ],
            [
                'sales_entry_id' => $salesEntries[2]->id ?? null,
                'user_id' => $user->id,
                'customer_id' => $customers[2]->id,
                'company_id' => $company->id,
                'transport_number' => 'TRN-2025-003',
                'transport_date' => now()->subDays(7),
                'due_days' => 45,
                'due_date' => now()->addDays(38),
                'freight_cond' => 'Paid',
                'way_bill_no' => 'WB003',
                'material_direct_delivered_from' => 'Main Warehouse',
                'lr_no' => 'LR003',
                'goods_insured_by' => 'PQR Insurance',
                'eway_bill_no' => 'EWB003',
                'doc_through' => 'Email',
                'policy_no' => 'POL003',
                'eway_bill_date' => now()->subDays(7),
                'driver_name' => 'Amit Singh',
                'driver_mob_no' => '**********',
                'dl_no' => 'DL003',
                'from_city' => 'Mumbai',
                'from_location' => 'Main Warehouse, Thane',
                'destination' => 'Jaipur',
                'to_city' => 'Jaipur',
                'to_location' => 'Market Street, Civil Lines',
                'delivery_1' => 'Standard Delivery',
                'prepared_by' => $user->name,
                'remarks' => 'Bulk software licenses delivery',
                'gross_wt' => '5.0',
                'net_wt' => '4.5',
                'packages' => 1,
                'status' => 'delivered'
            ]
        ];

        foreach ($transportEntries as $entryData) {
            TransportEntry::create($entryData);
        }

        echo "Created " . count($transportEntries) . " test transport entries.\n";
    }
}
