<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Company;
use App\Models\Customer;
use App\Models\Product;
use App\Models\User;
use App\Models\SalesEntry;
use App\Models\SalesEntryItem;
use App\Models\TransportEntry;
use Carbon\Carbon;

class SalesTransportDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Starting to seed sales and transport data...');

        // Get companies
        $companies = Company::all();
        
        if ($companies->count() < 2) {
            $this->command->error('Need at least 2 companies. Please create companies first.');
            return;
        }

        // Get users
        $users = User::all();
        if ($users->isEmpty()) {
            $this->command->error('No users found. Please create users first.');
            return;
        }

        // Get products
        $products = Product::all();
        if ($products->isEmpty()) {
            $this->command->error('No products found. Please create products first.');
            return;
        }

        foreach ($companies as $company) {
            $this->command->info("Creating data for company: {$company->name}");
            
            // Get customers for this company
            $customers = Customer::where('company_id', $company->id)->get();
            
            if ($customers->isEmpty()) {
                $this->command->info("Creating customers for {$company->name}");
                $customers = $this->createCustomersForCompany($company);
            }

            // Create 10 sales entries for this company
            $this->createSalesEntries($company, $customers, $users, $products);
            
            // Create 10 transport entries for this company
            $this->createTransportEntries($company, $customers, $users);
        }

        $this->command->info('Sales and transport data seeding completed!');
    }

    private function createCustomersForCompany($company)
    {
        // Get available states and cities
        $states = \App\Models\State::all();
        $cities = \App\Models\City::all();

        if ($states->isEmpty() || $cities->isEmpty()) {
            $this->command->error('No states or cities found. Please seed states and cities first.');
            return collect();
        }

        $customerData = [
            [
                'name' => 'Steel Industries Ltd',
                'email' => '<EMAIL>',
                'phone' => '9876543210',
                'address' => '123 Industrial Area, Sector 15',
                'gst_number' => '22ABCDE1234F1Z5',
                'state_id' => $states->random()->id,
            ],
            [
                'name' => 'Iron Works Corporation',
                'email' => '<EMAIL>',
                'phone' => '9876543211',
                'address' => '456 Manufacturing Hub, Phase 2',
                'gst_number' => '22FGHIJ5678K2Y6',
                'state_id' => $states->random()->id,
            ],
            [
                'name' => 'Metal Trading Co',
                'email' => '<EMAIL>',
                'phone' => '9876543212',
                'address' => '789 Commerce Street, Block A',
                'gst_number' => '22LMNOP9012L3X7',
                'state_id' => $states->random()->id,
            ],
            [
                'name' => 'Construction Materials Ltd',
                'email' => '<EMAIL>',
                'phone' => '9876543213',
                'address' => '321 Builder Colony, Zone 3',
                'gst_number' => '22QRSTU3456M4W8',
                'state_id' => $states->random()->id,
            ],
            [
                'name' => 'Engineering Solutions Pvt Ltd',
                'email' => '<EMAIL>',
                'phone' => '9876543214',
                'address' => '654 Tech Park, IT City',
                'gst_number' => '22VWXYZ7890N5V9',
                'state_id' => $states->random()->id,
            ],
        ];

        $customers = collect();
        foreach ($customerData as $data) {
            // Get a city from the selected state
            $stateId = $data['state_id'];
            $stateCities = $cities->where('state_id', $stateId);
            $cityId = $stateCities->isNotEmpty() ? $stateCities->random()->id : $cities->random()->id;

            $customer = Customer::create([
                'company_id' => $company->id,
                'name' => $data['name'],
                'email' => $data['email'],
                'phone' => $data['phone'],
                'address' => $data['address'],
                'state_id' => $stateId,
                'city_id' => $cityId,
                'pincode' => rand(100000, 999999),
                'gst_number' => $data['gst_number'],
                'status' => 'active',
            ]);
            $customers->push($customer);
        }

        return $customers;
    }

    private function createSalesEntries($company, $customers, $users, $products)
    {
        $this->command->info("Creating 10 sales entries for {$company->name}");

        $statuses = ['pending', 'approved', 'draft', 'rejected'];

        // Get the highest existing quotation number for this company
        $lastEntry = SalesEntry::where('company_id', $company->id)
            ->where('quotation_number', 'like', $company->code . '-QUO-%')
            ->orderBy('quotation_number', 'desc')
            ->first();

        $startNumber = 1;
        if ($lastEntry) {
            $lastNumber = (int) substr($lastEntry->quotation_number, -4);
            $startNumber = $lastNumber + 1;
        }

        for ($i = 0; $i < 10; $i++) {
            $customer = $customers->random();
            $user = $users->random();

            $quotationDate = Carbon::now()->subDays(rand(1, 30));
            $validUntil = $quotationDate->copy()->addDays(rand(15, 45));

            $quotationNumber = $company->code . '-QUO-' . str_pad($startNumber + $i, 4, '0', STR_PAD_LEFT);
            $invoiceNumber = $company->code . '-INV-' . str_pad($startNumber + $i, 4, '0', STR_PAD_LEFT);

            $salesEntry = SalesEntry::create([
                'company_id' => $company->id,
                'customer_id' => $customer->id,
                'user_id' => $user->id,
                'quotation_number' => $quotationNumber,
                'quotation_date' => $quotationDate,
                'valid_until' => $validUntil,
                'invoice_number' => $invoiceNumber,
                'invoice_date' => $quotationDate,
                'payment_terms' => collect(['30 Days', '45 Days', '60 Days', 'Advance', 'COD'])->random(),
                'party_name' => $customer->name,
                'mobile' => $customer->phone,
                'email' => $customer->email,
                'subtotal' => 0, // Will be calculated after items
                'total_cgst' => 0,
                'total_sgst' => 0,
                'total_igst' => 0,
                'grand_total' => 0,
                'ins_pmt' => rand(0, 1000),
                'insurance' => rand(0, 2000),
                'frt_advance' => rand(0, 1500),
                'tcs_percent' => rand(0, 1) ? 0.1 : 0,
                'tcs_amount' => 0, // Will be calculated
                'net_amount' => 0, // Will be calculated
                'notes' => 'Sample quotation for ' . $customer->name,
                'status' => $statuses[array_rand($statuses)],
            ]);

            // Create 2-5 items for each sales entry
            $this->createSalesEntryItems($salesEntry, $products);
            
            // Recalculate totals
            $this->recalculateSalesEntryTotals($salesEntry);
        }
    }

    private function createSalesEntryItems($salesEntry, $products)
    {
        $itemCount = rand(2, 5);
        $subtotal = 0;
        
        for ($j = 1; $j <= $itemCount; $j++) {
            $product = $products->random();
            $quantity = rand(1, 100);
            $rate = rand(100, 5000);
            $total = $quantity * $rate;
            $subtotal += $total;
            
            SalesEntryItem::create([
                'sales_entry_id' => $salesEntry->id,
                'product_id' => $product->id,
                'description' => $product->name,
                'hsn_code' => $product->hsn_code ?? '7208',
                'quantity' => $quantity,
                'packages' => $quantity,
                'unit' => collect(['MT', 'KG', 'PCS', 'TON'])->random(),
                'rate' => $rate,
                'unit_price' => $rate,
                'basic_rate' => $rate * 0.9,
                'loading' => rand(0, 50),
                'total' => $total,
                'line_total' => $total,
                'taxable_value' => $total,
                'gst_percent' => 18,
                'cgst_percent' => 9,
                'sgst_percent' => 9,
                'igst_percent' => 0,
                'cgst_amount' => $total * 0.09,
                'sgst_amount' => $total * 0.09,
                'igst_amount' => 0,
                'discount' => 0,
            ]);
        }
        
        return $subtotal;
    }

    private function recalculateSalesEntryTotals($salesEntry)
    {
        $items = $salesEntry->items;
        $subtotal = $items->sum('total');
        $totalCgst = $items->sum('cgst_amount');
        $totalSgst = $items->sum('sgst_amount');
        $totalIgst = $items->sum('igst_amount');
        $grandTotal = $subtotal + $totalCgst + $totalSgst + $totalIgst;
        
        $tcsAmount = $grandTotal * ($salesEntry->tcs_percent / 100);
        $netAmount = $grandTotal + $salesEntry->ins_pmt + $salesEntry->insurance + $salesEntry->frt_advance + $tcsAmount;
        
        $salesEntry->update([
            'subtotal' => $subtotal,
            'total_cgst' => $totalCgst,
            'total_sgst' => $totalSgst,
            'total_igst' => $totalIgst,
            'grand_total' => $grandTotal,
            'tcs_amount' => $tcsAmount,
            'net_amount' => $netAmount,
            'total_amount' => $netAmount, // Legacy field
        ]);
    }

    private function createTransportEntries($company, $customers, $users)
    {
        $this->command->info("Creating 10 transport entries for {$company->name}");

        $statuses = ['scheduled', 'in_transit', 'delivered', 'cancelled'];
        $cities = ['Mumbai', 'Delhi', 'Bangalore', 'Chennai', 'Kolkata', 'Pune', 'Hyderabad', 'Ahmedabad'];
        $vehicleNumbers = ['MH12AB1234', 'DL01CD5678', 'KA03EF9012', 'TN09GH3456', 'WB07IJ7890'];

        // Get the highest existing transport number for this company
        $lastEntry = TransportEntry::where('company_id', $company->id)
            ->where('transport_number', 'like', $company->code . '-TRN-%')
            ->orderBy('transport_number', 'desc')
            ->first();

        $startNumber = 1;
        if ($lastEntry) {
            $lastNumber = (int) substr($lastEntry->transport_number, -4);
            $startNumber = $lastNumber + 1;
        }

        for ($i = 0; $i < 10; $i++) {
            $customer = $customers->random();
            $user = $users->random();

            $transportDate = Carbon::now()->subDays(rand(1, 20));
            $dueDate = $transportDate->copy()->addDays(rand(3, 15));
            $estimatedDelivery = $transportDate->copy()->addDays(rand(2, 10));

            $fromCity = $cities[array_rand($cities)];
            $toCity = $cities[array_rand($cities)];
            while ($toCity === $fromCity) {
                $toCity = $cities[array_rand($cities)];
            }

            $transportCost = rand(5000, 50000);
            $weight = rand(1000, 10000);
            $packages = rand(10, 100);

            $transportNumber = $company->code . '-TRN-' . str_pad($startNumber + $i, 4, '0', STR_PAD_LEFT);

            $transportEntry = TransportEntry::create([
                'company_id' => $company->id,
                'customer_id' => $customer->id,
                'user_id' => $user->id,
                'transport_number' => $transportNumber,
                'transport_date' => $transportDate,
                'due_days' => rand(3, 15),
                'due_date' => $dueDate,
                'freight_cond' => collect(['To Pay', 'Paid', 'TBB'])->random(),
                'way_bill_no' => 'WB' . rand(100000, 999999),
                'material_direct_delivered_from' => $fromCity . ' Warehouse',
                'lr_no' => 'LR' . rand(100000, 999999),
                'goods_insured_by' => collect(['Self', 'Transporter', 'Customer'])->random(),
                'eway_bill_no' => 'EWB' . rand(100000000000, 999999999999),
                'doc_through' => collect(['Email', 'Courier', 'Hand Delivery'])->random(),
                'policy_no' => 'POL' . rand(100000, 999999),
                'eway_bill_date' => $transportDate,
                'driver_name' => collect(['Rajesh Kumar', 'Suresh Sharma', 'Ramesh Patel', 'Mahesh Singh', 'Dinesh Yadav'])->random(),
                'lc_no' => 'LC' . rand(100000, 999999),
                'our_offer_no' => $company->code . '-OFF-' . rand(1000, 9999),
                'driver_mob_no' => '98765' . rand(10000, 99999),
                'ac_of' => $customer->name,
                'cash_address' => $customer->address,
                'dl_no' => 'DL' . rand(1000000000000000, 9999999999999999),
                'from_city' => $fromCity,
                'from_location' => $fromCity . ' Industrial Area',
                'export_inv_no' => 'EXP' . rand(100000, 999999),
                'destination' => $toCity,
                'to_city' => $toCity,
                'to_location' => $toCity . ' Delivery Point',
                'export_inv_date' => $transportDate,
                'road_permit_no' => 'RP' . rand(100000, 999999),
                'delivery_1' => $toCity . ' Main Office',
                'prepared_by' => $user->name,
                'delivery_2' => $toCity . ' Warehouse',
                'remarks' => 'Transport from ' . $fromCity . ' to ' . $toCity,
                'gross_wt' => $weight + rand(100, 500),
                'tare_wt' => rand(100, 500),
                'net_wt' => $weight,
                'weight' => $weight,
                'packages' => $packages,
                'goods_description' => collect(['Iron Rods', 'Steel Sheets', 'Metal Pipes', 'Construction Materials', 'Industrial Equipment'])->random(),
                'vehicle_number' => $vehicleNumbers[array_rand($vehicleNumbers)],
                'driver_phone' => '98765' . rand(10000, 99999),
                'pickup_address' => $fromCity . ' Industrial Area, Sector 15',
                'delivery_address' => $toCity . ' Delivery Point, Zone 3',
                'estimated_delivery' => $estimatedDelivery,
                'actual_delivery' => rand(0, 1) ? $estimatedDelivery->copy()->addDays(rand(-2, 3)) : null,
                'transport_cost' => $transportCost,
                'total' => $transportCost,
                'total_amount' => $transportCost,
                'ins_pmt' => rand(0, 500),
                'insurance' => rand(0, 1000),
                'frt_advance' => rand(0, 2000),
                'grand_total' => $transportCost,
                'tcs_percent' => rand(0, 1) ? 0.1 : 0,
                'tcs_amount' => $transportCost * (rand(0, 1) ? 0.001 : 0),
                'net_amount' => $transportCost + rand(0, 1000),
                'notes' => 'Transport service from ' . $fromCity . ' to ' . $toCity . ' for ' . $customer->name,
                'status' => $statuses[array_rand($statuses)],
            ]);

            // Recalculate transport totals
            $this->recalculateTransportEntryTotals($transportEntry);
        }
    }

    private function recalculateTransportEntryTotals($transportEntry)
    {
        $grandTotal = $transportEntry->transport_cost + $transportEntry->ins_pmt + $transportEntry->insurance;
        $tcsAmount = $grandTotal * ($transportEntry->tcs_percent / 100);
        $netAmount = $grandTotal + $transportEntry->frt_advance + $tcsAmount;

        $transportEntry->update([
            'grand_total' => $grandTotal,
            'tcs_amount' => $tcsAmount,
            'net_amount' => $netAmount,
            'total_amount' => $netAmount,
        ]);
    }
}
