<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update the role enum to include 'super-admin'
        DB::statement("ALTER TABLE company_users MODIFY COLUMN role ENUM('admin', 'manager', 'user', 'super-admin') DEFAULT 'user'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert back to original enum values
        // First, update any super-admin roles to admin
        DB::statement("UPDATE company_users SET role = 'admin' WHERE role = 'super-admin'");

        // Then modify the enum back to original values
        DB::statement("ALTER TABLE company_users MODIFY COLUMN role ENUM('admin', 'manager', 'user') DEFAULT 'user'");
    }
};
