<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // Drop the index first
            $table->dropIndex(['sku']);
            // Drop the unique constraint
            $table->dropUnique(['sku']);
            // Drop the column
            $table->dropColumn('sku');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // Add the column back
            $table->string('sku')->unique()->after('category_id');
            // Add the index back
            $table->index(['sku']);
        });
    }
};
