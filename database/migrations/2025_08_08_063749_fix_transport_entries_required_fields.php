<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('transport_entries', function (Blueprint $table) {
            // Make originally required fields nullable since we're using new field structure
            $table->string('driver_phone', 15)->nullable()->change();
            $table->text('pickup_address')->nullable()->change();
            $table->text('delivery_address')->nullable()->change();
            $table->date('estimated_delivery')->nullable()->change();
            $table->string('vehicle_number', 20)->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('transport_entries', function (Blueprint $table) {
            // Revert back to required fields
            $table->string('driver_phone', 15)->nullable(false)->change();
            $table->text('pickup_address')->nullable(false)->change();
            $table->text('delivery_address')->nullable(false)->change();
            $table->date('estimated_delivery')->nullable(false)->change();
            $table->string('vehicle_number', 20)->nullable(false)->change();
        });
    }
};
