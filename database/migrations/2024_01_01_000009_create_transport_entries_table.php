<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transport_entries', function (Blueprint $table) {
            $table->id();
            $table->foreignId('sales_entry_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->date('transport_date');
            $table->string('vehicle_number', 20);
            $table->string('driver_name');
            $table->string('driver_phone', 15);
            $table->text('pickup_address');
            $table->text('delivery_address');
            $table->date('estimated_delivery');
            $table->date('actual_delivery')->nullable();
            $table->decimal('transport_cost', 10, 2)->default(0);
            $table->text('notes')->nullable();
            $table->enum('status', ['scheduled', 'in_transit', 'delivered', 'cancelled'])->default('scheduled');
            $table->timestamps();
            $table->softDeletes();
            
            $table->index(['status']);
            $table->index(['transport_date']);
            $table->index(['estimated_delivery']);
            $table->index(['sales_entry_id']);
            $table->index(['user_id']);
            $table->index(['vehicle_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transport_entries');
    }
};
