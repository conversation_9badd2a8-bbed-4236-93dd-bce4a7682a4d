<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['role']);
            $table->dropIndex(['role', 'status']);

            // Drop foreign key constraint for role_id
            $table->dropForeign(['role_id']);

            // Drop the columns
            $table->dropColumn(['role', 'role_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add back the columns
            $table->enum('role', ['admin', 'manager', 'user'])->default('user')->after('phone');
            $table->foreignId('role_id')->nullable()->after('role')->constrained()->onDelete('set null');

            // Add back the indexes
            $table->index(['role']);
            $table->index(['role', 'status']);
        });
    }
};
