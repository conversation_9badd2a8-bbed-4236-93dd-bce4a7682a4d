<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('company_users', function (Blueprint $table) {
            // Add role_id column if it doesn't exist
            if (!Schema::hasColumn('company_users', 'role_id')) {
                $table->foreignId('role_id')->nullable()->after('role')->constrained('roles')->onDelete('set null');
            }
            
            // Add indexes for better performance
            $table->index(['company_id', 'role_id']);
            $table->index(['user_id', 'company_id', 'status']);
        });

        // Update existing records to have proper role_id
        $this->updateExistingRecords();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('company_users', function (Blueprint $table) {
            $table->dropIndex(['company_id', 'role_id']);
            $table->dropIndex(['user_id', 'company_id', 'status']);
            
            if (Schema::hasColumn('company_users', 'role_id')) {
                $table->dropForeign(['role_id']);
                $table->dropColumn('role_id');
            }
        });
    }

    /**
     * Update existing records with proper role_id
     */
    private function updateExistingRecords()
    {
        // Get role mappings
        $roles = DB::table('roles')->get()->keyBy('slug');
        
        // Update company_users records
        $companyUsers = DB::table('company_users')->get();
        
        foreach ($companyUsers as $companyUser) {
            $roleSlug = $companyUser->role;
            $roleId = null;
            
            // Map string roles to role IDs
            if (isset($roles[$roleSlug])) {
                $roleId = $roles[$roleSlug]->id;
            } else {
                // Try to find a matching role
                switch ($roleSlug) {
                    case 'admin':
                    case 'administrator':
                        $roleId = $roles['admin']->id ?? $roles['super-admin']->id ?? null;
                        break;
                    case 'manager':
                        $roleId = $roles['manager']->id ?? null;
                        break;
                    case 'user':
                    default:
                        $roleId = $roles['user']->id ?? null;
                        break;
                }
            }
            
            if ($roleId) {
                DB::table('company_users')
                    ->where('id', $companyUser->id)
                    ->update(['role_id' => $roleId]);
            }
        }
    }
};
