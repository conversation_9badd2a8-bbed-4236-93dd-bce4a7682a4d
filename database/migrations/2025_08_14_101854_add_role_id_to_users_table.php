<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Role;
use App\Models\User;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add role_id column to users table
        Schema::table('users', function (Blueprint $table) {
            $table->foreignId('role_id')->nullable()->after('phone')->constrained()->onDelete('set null');
        });

        // Migrate existing role data
        $this->migrateExistingRoles();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['role_id']);
            $table->dropColumn('role_id');
        });
    }

    /**
     * Migrate existing string roles to role_id references
     */
    private function migrateExistingRoles(): void
    {
        // Get role mappings
        $roleMap = [
            'admin' => Role::where('slug', 'admin')->first()?->id,
            'manager' => Role::where('slug', 'manager')->first()?->id,
            'user' => Role::where('slug', 'user')->first()?->id,
        ];

        // Update users with role_id based on their current role string
        foreach ($roleMap as $roleString => $roleId) {
            if ($roleId) {
                User::where('role', $roleString)->update(['role_id' => $roleId]);
            }
        }

        // Special case: Make <EMAIL> a super admin
        $superAdminRole = Role::where('slug', 'super-admin')->first();
        if ($superAdminRole) {
            User::where('email', '<EMAIL>')->update(['role_id' => $superAdminRole->id]);
        }
    }
};
