<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('roles', function (Blueprint $table) {
            $table->dropIndex(['level', 'status']); // Drop the index first
            $table->dropColumn('level');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('roles', function (Blueprint $table) {
            $table->integer('level')->default(4)->comment('Role hierarchy: 1=Super Admin, 2=Admin, 3=Manager, 4=User, etc.');
            $table->index(['level', 'status']);
        });
    }
};
