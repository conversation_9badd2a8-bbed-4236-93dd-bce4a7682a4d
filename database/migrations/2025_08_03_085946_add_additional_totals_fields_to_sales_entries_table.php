<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sales_entries', function (Blueprint $table) {
            $table->decimal('ins_pmt', 10, 2)->default(0)->after('grand_total');
            $table->decimal('insurance', 10, 2)->default(0)->after('ins_pmt');
            $table->decimal('frt_advance', 10, 2)->default(0)->after('insurance');
            $table->decimal('tcs_percent', 5, 2)->default(0)->after('frt_advance');
            $table->decimal('tcs_amount', 10, 2)->default(0)->after('tcs_percent');
            $table->decimal('net_amount', 10, 2)->default(0)->after('tcs_amount');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sales_entries', function (Blueprint $table) {
            $table->dropColumn([
                'ins_pmt',
                'insurance',
                'frt_advance',
                'tcs_percent',
                'tcs_amount',
                'net_amount'
            ]);
        });
    }
};
