<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('transport_entries', function (Blueprint $table) {
            // Add missing fields for transport sharing functionality
            if (!Schema::hasColumn('transport_entries', 'customer_id')) {
                $table->foreignId('customer_id')->nullable()->after('company_id')->constrained()->onDelete('cascade');
            }
            if (!Schema::hasColumn('transport_entries', 'transport_number')) {
                $table->string('transport_number')->nullable()->after('customer_id');
            }
            if (!Schema::hasColumn('transport_entries', 'from_location')) {
                $table->string('from_location')->nullable()->after('from_city');
            }
            if (!Schema::hasColumn('transport_entries', 'to_location')) {
                $table->string('to_location')->nullable()->after('to_city');
            }
            if (!Schema::hasColumn('transport_entries', 'weight')) {
                $table->decimal('weight', 10, 2)->nullable()->after('net_wt');
            }
            if (!Schema::hasColumn('transport_entries', 'packages')) {
                $table->integer('packages')->nullable()->after('weight');
            }
            if (!Schema::hasColumn('transport_entries', 'goods_description')) {
                $table->text('goods_description')->nullable()->after('packages');
            }
            if (!Schema::hasColumn('transport_entries', 'total_amount')) {
                $table->decimal('total_amount', 10, 2)->nullable()->after('total');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('transport_entries', function (Blueprint $table) {
            // Drop the added columns
            $columns = ['customer_id', 'transport_number', 'from_location', 'to_location', 'weight', 'packages', 'goods_description', 'total_amount'];

            foreach ($columns as $column) {
                if (Schema::hasColumn('transport_entries', $column)) {
                    if ($column === 'customer_id') {
                        $table->dropForeign(['customer_id']);
                    }
                    $table->dropColumn($column);
                }
            }
        });
    }
};
