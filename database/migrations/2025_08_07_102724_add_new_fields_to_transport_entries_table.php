<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('transport_entries', function (Blueprint $table) {
            // Add new fields for comprehensive transport management
            $table->integer('due_days')->nullable()->after('transport_date');
            $table->date('due_date')->nullable()->after('due_days');
            $table->string('freight_cond')->nullable()->after('due_date');
            $table->string('way_bill_no')->nullable()->after('freight_cond');
            $table->string('material_direct_delivered_from')->nullable()->after('way_bill_no');
            $table->string('lr_no')->nullable()->after('material_direct_delivered_from');
            $table->string('goods_insured_by')->nullable()->after('lr_no');
            $table->string('eway_bill_no')->nullable()->after('goods_insured_by');
            $table->string('doc_through')->nullable()->after('eway_bill_no');
            $table->string('policy_no')->nullable()->after('doc_through');
            $table->date('eway_bill_date')->nullable()->after('policy_no');
            $table->string('lc_no')->nullable()->after('eway_bill_date');
            $table->string('our_offer_no')->nullable()->after('lc_no');
            $table->string('driver_mob_no')->nullable()->after('our_offer_no');
            $table->string('ac_of')->nullable()->after('driver_mob_no');
            $table->text('cash_address')->nullable()->after('ac_of');
            $table->string('dl_no')->nullable()->after('cash_address');
            $table->string('from_city')->nullable()->after('dl_no');
            $table->string('export_inv_no')->nullable()->after('from_city');
            $table->string('destination')->nullable()->after('export_inv_no');
            $table->string('to_city')->nullable()->after('destination');
            $table->date('export_inv_date')->nullable()->after('to_city');
            $table->string('road_permit_no')->nullable()->after('export_inv_date');
            $table->string('delivery_1')->nullable()->after('road_permit_no');
            $table->string('prepared_by')->nullable()->after('delivery_1');
            $table->string('delivery_2')->nullable()->after('prepared_by');
            $table->text('remarks')->nullable()->after('delivery_2');
            $table->decimal('gross_wt', 10, 2)->nullable()->after('remarks');
            $table->decimal('tare_wt', 10, 2)->nullable()->after('gross_wt');
            $table->decimal('net_wt', 10, 2)->nullable()->after('tare_wt');
            
            // Financial fields
            $table->decimal('total', 10, 2)->nullable()->after('net_wt');
            $table->decimal('ins_pmt', 10, 2)->nullable()->after('total');
            $table->decimal('insurance', 10, 2)->nullable()->after('ins_pmt');
            $table->decimal('frt_advance', 10, 2)->nullable()->after('insurance');
            $table->decimal('grand_total', 10, 2)->nullable()->after('frt_advance');
            $table->decimal('tcs_percent', 5, 2)->nullable()->after('grand_total');
            $table->decimal('tcs_amount', 10, 2)->nullable()->after('tcs_percent');
            $table->decimal('net_amount', 10, 2)->nullable()->after('tcs_amount');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('transport_entries', function (Blueprint $table) {
            $table->dropColumn([
                'due_days', 'due_date', 'freight_cond', 'way_bill_no', 'material_direct_delivered_from',
                'lr_no', 'goods_insured_by', 'eway_bill_no', 'doc_through', 'policy_no', 'eway_bill_date',
                'lc_no', 'our_offer_no', 'driver_mob_no', 'ac_of', 'cash_address', 'dl_no', 'from_city',
                'export_inv_no', 'destination', 'to_city', 'export_inv_date', 'road_permit_no', 'delivery_1',
                'prepared_by', 'delivery_2', 'remarks', 'gross_wt', 'tare_wt', 'net_wt', 'total', 'ins_pmt',
                'insurance', 'frt_advance', 'grand_total', 'tcs_percent', 'tcs_amount', 'net_amount'
            ]);
        });
    }
};
