<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add new fields to sales_entries table
        Schema::table('sales_entries', function (Blueprint $table) {
            $table->string('invoice_number')->nullable()->after('user_id');
            $table->date('invoice_date')->nullable()->after('invoice_number');
            $table->string('payment_terms')->nullable()->after('invoice_date');
            $table->string('party_name')->nullable()->after('payment_terms');
            $table->string('mobile', 15)->nullable()->after('party_name');
            $table->string('email')->nullable()->after('mobile');
            $table->decimal('total_cgst', 12, 2)->default(0)->after('subtotal');
            $table->decimal('total_sgst', 12, 2)->default(0)->after('total_cgst');
            $table->decimal('total_igst', 12, 2)->default(0)->after('total_sgst');
            $table->decimal('grand_total', 12, 2)->default(0)->after('total_igst');

            // Add indexes for new fields
            $table->index(['invoice_number']);
            $table->index(['invoice_date']);
            $table->index(['mobile']);
        });

        // Add new fields to sales_entry_items table
        Schema::table('sales_entry_items', function (Blueprint $table) {
            $table->string('hsn_code')->nullable()->after('product_id');
            $table->decimal('gst_percent', 5, 2)->default(0)->after('hsn_code');
            $table->integer('packages')->default(1)->after('gst_percent');
            $table->string('unit')->nullable()->after('packages');
            $table->decimal('rate', 10, 2)->default(0)->after('unit');
            $table->decimal('loading', 10, 2)->default(0)->after('rate');
            $table->decimal('basic_rate', 10, 2)->default(0)->after('loading');
            $table->decimal('total', 12, 2)->default(0)->after('basic_rate');
            $table->decimal('taxable_value', 12, 2)->default(0)->after('total');
            $table->text('description')->nullable()->after('taxable_value');
            $table->decimal('cgst_percent', 5, 2)->default(0)->after('description');
            $table->decimal('cgst_amount', 12, 2)->default(0)->after('cgst_percent');
            $table->decimal('sgst_percent', 5, 2)->default(0)->after('cgst_amount');
            $table->decimal('sgst_amount', 12, 2)->default(0)->after('sgst_percent');
            $table->decimal('igst_percent', 5, 2)->default(0)->after('sgst_amount');
            $table->decimal('igst_amount', 12, 2)->default(0)->after('igst_percent');

            // Add indexes for new fields
            $table->index(['hsn_code']);
            $table->index(['gst_percent']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove new fields from sales_entries table
        Schema::table('sales_entries', function (Blueprint $table) {
            $table->dropIndex(['invoice_number']);
            $table->dropIndex(['invoice_date']);
            $table->dropIndex(['mobile']);

            $table->dropColumn([
                'invoice_number',
                'invoice_date',
                'payment_terms',
                'party_name',
                'mobile',
                'email',
                'total_cgst',
                'total_sgst',
                'total_igst',
                'grand_total'
            ]);
        });

        // Remove new fields from sales_entry_items table
        Schema::table('sales_entry_items', function (Blueprint $table) {
            $table->dropIndex(['hsn_code']);
            $table->dropIndex(['gst_percent']);

            $table->dropColumn([
                'hsn_code',
                'gst_percent',
                'packages',
                'unit',
                'rate',
                'loading',
                'basic_rate',
                'total',
                'taxable_value',
                'description',
                'cgst_percent',
                'cgst_amount',
                'sgst_percent',
                'sgst_amount',
                'igst_percent',
                'igst_amount'
            ]);
        });
    }
};
