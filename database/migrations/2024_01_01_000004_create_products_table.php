<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->foreignId('category_id')->constrained()->onDelete('cascade');
            $table->string('sku')->unique();
            $table->string('hsn_code')->nullable();
            $table->decimal('gst_percentage', 5, 2)->default(0);
            $table->string('unit')->default('PCS');
            $table->decimal('rate', 10, 2);
            $table->decimal('price', 10, 2);
            $table->decimal('gauge_diff', 10, 2)->nullable();
            $table->integer('stock_quantity')->default(0);
            $table->text('description')->nullable();
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['status']);
            $table->index(['name']);
            $table->index(['sku']);
            $table->index(['hsn_code']);
            $table->index(['category_id', 'status']);
            $table->index(['stock_quantity']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
