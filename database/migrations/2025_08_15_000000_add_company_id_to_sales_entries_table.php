<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sales_entries', function (Blueprint $table) {
            // Check if company_id column doesn't exist before adding
            if (!Schema::hasColumn('sales_entries', 'company_id')) {
                $table->foreignId('company_id')->nullable()->after('id')->constrained()->onDelete('cascade');
            }

            // Add indexes if they don't exist (<PERSON><PERSON> will handle duplicates gracefully)
            try {
                $table->index(['company_id', 'status']);
            } catch (\Exception $e) {
                // Index might already exist
            }

            try {
                $table->index(['company_id', 'quotation_date']);
            } catch (\Exception $e) {
                // Index might already exist
            }

            try {
                $table->index(['company_id', 'customer_id']);
            } catch (\Exception $e) {
                // Index might already exist
            }

            // Add index for quotation number generation
            try {
                $table->index(['company_id', 'created_at']);
            } catch (\Exception $e) {
                // Index might already exist
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sales_entries', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['company_id', 'status']);
            $table->dropIndex(['company_id', 'quotation_date']);
            $table->dropIndex(['company_id', 'customer_id']);
            
            // Drop foreign key constraint and column
            $table->dropForeign(['company_id']);
            $table->dropColumn('company_id');
        });
    }
};
