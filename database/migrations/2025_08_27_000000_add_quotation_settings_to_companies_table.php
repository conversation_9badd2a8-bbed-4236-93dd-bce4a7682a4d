<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('companies', function (Blueprint $table) {
            $table->string('quotation_prefix', 20)->nullable()->after('code')->comment('Quotation number prefix (e.g., JMD/QT)');
            $table->integer('quotation_start_number')->default(2007)->after('quotation_prefix')->comment('Starting number for quotation sequence');
            $table->integer('current_quotation_number')->default(2007)->after('quotation_start_number')->comment('Current quotation number counter');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('companies', function (Blueprint $table) {
            $table->dropColumn([
                'quotation_prefix',
                'quotation_start_number',
                'current_quotation_number'
            ]);
        });
    }
};
