<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sales_entries', function (Blueprint $table) {
            if (!Schema::hasColumn('sales_entries', 'share_token')) {
                $table->string('share_token', 64)->nullable()->unique()->after('id');
            }
            if (!Schema::hasColumn('sales_entries', 'share_expires_at')) {
                $table->timestamp('share_expires_at')->nullable()->after('share_token');
            }
            if (!Schema::hasColumn('sales_entries', 'share_enabled')) {
                $table->boolean('share_enabled')->default(false)->after('share_expires_at');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sales_entries', function (Blueprint $table) {
            $table->dropColumn(['share_token', 'share_expires_at', 'share_enabled']);
        });
    }
};
