<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('transport_entries', function (Blueprint $table) {
            // First drop the foreign key constraint
            $table->dropForeign(['sales_entry_id']);
            
            // Modify the column to be nullable
            $table->unsignedBigInteger('sales_entry_id')->nullable()->change();
            
            // Re-add the foreign key constraint as nullable
            $table->foreign('sales_entry_id')->references('id')->on('sales_entries')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('transport_entries', function (Blueprint $table) {
            // Drop the nullable foreign key
            $table->dropForeign(['sales_entry_id']);
            
            // Revert to non-nullable
            $table->foreignId('sales_entry_id')->constrained()->onDelete('cascade')->change();
        });
    }
};
