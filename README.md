# JMD Traders Dashboard - Laravel Application

A comprehensive Laravel-based quotation management system for JMD Traders, featuring modern UI, robust backend architecture, and complete business workflow management.

## 🚀 Features

### Core Functionality
- **Dashboard**: Real-time business metrics, recent quotations, quick actions, and system status monitoring
- **Master Data Management**: Categories, Products, Customers, Users, States, and Cities
- **Sales Entry**: Complete quotation creation, management, and approval workflow
- **Transport Entry**: Logistics and transportation management with delivery tracking
- **Reports System**: Daily, monthly, quotation, sales, and customer reports with analytics
- **User Authentication**: Secure login/logout with session management

### Technical Features
- **Laravel 10.10**: Modern PHP framework with MVC architecture
- **Blade Templates**: Responsive UI with Bootstrap 5.3.0 and Font Awesome icons
- **Eloquent ORM**: Database relationships, scopes, and business logic
- **RESTful API**: Standardized CRUD operations with JSON responses
- **Middleware Protection**: Authentication and authorization
- **Form Validation**: Server-side validation with error handling
- **Soft Deletes**: Data integrity and recovery capabilities

## 📁 Project Structure

```
jmd-traders-dashboard/
├── app/
│   ├── Http/
│   │   ├── Controllers/
│   │   │   ├── Auth/
│   │   │   │   └── LoginController.php
│   │   │   ├── DashboardController.php
│   │   │   ├── MasterController.php
│   │   │   ├── EntryController.php
│   │   │   └── ReportController.php
│   │   └── Middleware/
│   │       └── Authenticate.php
│   └── Models/
│       ├── User.php
│       ├── Category.php
│       ├── Product.php
│       ├── Customer.php
│       ├── State.php
│       ├── City.php
│       ├── SalesEntry.php
│       ├── SalesEntryItem.php
│       └── TransportEntry.php
├── database/
│   └── migrations/
│       ├── 2024_01_01_000001_create_categories_table.php
│       ├── 2024_01_01_000002_create_products_table.php
│       ├── 2024_01_01_000003_create_customers_table.php
│       ├── 2024_01_01_000004_create_states_table.php
│       ├── 2024_01_01_000005_create_cities_table.php
│       ├── 2024_01_01_000006_create_users_table.php
│       ├── 2024_01_01_000007_create_sales_entries_table.php
│       ├── 2024_01_01_000008_create_sales_entry_items_table.php
│       └── 2024_01_01_000009_create_transport_entries_table.php
├── resources/
│   └── views/
│       ├── layouts/
│       │   └── app.blade.php
│       ├── auth/
│       │   └── login.blade.php
│       ├── dashboard/
│       │   └── index.blade.php
│       ├── masters/
│       │   └── category/
│       │       └── index.blade.php
│       ├── entries/
│       │   ├── sales/
│       │   │   ├── index.blade.php
│       │   │   └── create.blade.php
│       │   └── transport/
│       │       └── index.blade.php
│       └── reports/
│           ├── daily.blade.php
│           └── monthly.blade.php
├── routes/
│   └── web.php
├── config/
│   └── auth.php
├── composer.json
├── .env.example
└── README.md
```

## 🛠️ Installation & Setup

### Prerequisites
- PHP 8.1 or higher
- Composer
- MySQL 8.0 or higher
- Node.js & NPM (for asset compilation)

### Installation Steps

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd jmd-traders-dashboard
   ```

2. **Install PHP dependencies**
   ```bash
   composer install
   ```

3. **Environment setup**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

4. **Configure database**
   Edit `.env` file with your database credentials:
   ```env
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=jmd_traders
   DB_USERNAME=your_username
   DB_PASSWORD=your_password
   ```

5. **Run database migrations**
   ```bash
   php artisan migrate
   ```

6. **Seed database (optional)**
   ```bash
   php artisan db:seed
   ```

7. **Start development server**
   ```bash
   php artisan serve
   ```

8. **Access the application**
   Open your browser and navigate to `http://localhost:8000`

## 🗄️ Database Schema

### Core Tables
- **users**: User authentication and profile management
- **categories**: Product categorization
- **products**: Product catalog with pricing
- **customers**: Customer information and contact details
- **states**: State master data
- **cities**: City master data with state relationships
- **sales_entries**: Quotation headers with customer and totals
- **sales_entry_items**: Quotation line items with products and quantities
- **transport_entries**: Transportation and logistics management

### Key Relationships
- Products belong to Categories
- Cities belong to States
- Customers belong to Cities/States
- Sales Entries belong to Customers and Users
- Sales Entry Items belong to Sales Entries and Products
- Transport Entries belong to Sales Entries

## 🎯 Usage Guide

### Dashboard
- View real-time business metrics
- Monitor recent quotations and their status
- Access quick actions for common tasks
- Check system status and notifications

### Master Data Management
Navigate to Masters menu to manage:
- **Categories**: Product categorization
- **Products**: Product catalog with SKU, pricing, and descriptions
- **Customers**: Customer database with contact information
- **Users**: System user management
- **States & Cities**: Geographic master data

### Sales Entry (Quotations)
1. Navigate to Sales Entry
2. Click "New Quotation" to create
3. Select customer and add products
4. Configure quantities, pricing, and discounts
5. Review totals and submit for approval

### Transport Entry
1. Navigate to Transport Entry
2. Create transport entries for approved quotations
3. Assign vehicles and drivers
4. Track delivery status and updates
5. Mark deliveries as completed

### Reports
Access comprehensive reports:
- **Daily Reports**: Day-wise business activity
- **Monthly Reports**: Monthly trends and analytics
- **Quotation Reports**: Quotation-specific analysis
- **Sales Reports**: Sales performance metrics
- **Customer Reports**: Customer activity and preferences

## 🔧 Configuration

### Authentication
- Default authentication uses email/password
- Session-based authentication with remember me option
- Middleware protection for all dashboard routes
- User status validation (active users only)

### Security Features
- CSRF protection on all forms
- Input validation and sanitization
- SQL injection prevention via Eloquent ORM
- XSS protection through Blade templating

## 🚀 Deployment

### Production Setup
1. Set `APP_ENV=production` in `.env`
2. Set `APP_DEBUG=false` in `.env`
3. Configure production database
4. Run `composer install --optimize-autoloader --no-dev`
5. Run `php artisan config:cache`
6. Run `php artisan route:cache`
7. Run `php artisan view:cache`
8. Set up proper file permissions
9. Configure web server (Apache/Nginx)

### Environment Variables
Key environment variables to configure:
```env
APP_NAME="JMD Traders Dashboard"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com

DB_CONNECTION=mysql
DB_HOST=your-db-host
DB_DATABASE=jmd_traders
DB_USERNAME=your-db-user
DB_PASSWORD=your-db-password

MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-email
MAIL_PASSWORD=your-password
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/new-feature`)
3. Commit your changes (`git commit -am 'Add new feature'`)
4. Push to the branch (`git push origin feature/new-feature`)
5. Create a Pull Request

## 📝 License

This project is proprietary software developed for JMD Traders. All rights reserved.

## 📞 Support

For technical support or questions, please contact:
- Email: <EMAIL>
- Phone: +91-XXXXXXXXXX

---

**JMD Traders Dashboard** - Efficient Quotation Management System
Version 1.0.0 | Built with Laravel 10.10
