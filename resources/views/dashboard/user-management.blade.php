@extends('layouts.app')

@section('title', 'User Management')

@push('styles')
<style>
    :root {
        --primary: #4e73df;
        --primary-dark: #224abe;
        --success: #1cc88a;
        --info: #36b9cc;
        --warning: #f6c23e;
        --danger: #e74a3b;
        --secondary: #858796;
        --light: #f8f9fc;
        --dark: #5a5c69;
    }

    .user-management-container {
        background: var(--light);
        min-height: 100vh;
        padding: 20px 0;
    }

    .page-header {
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
        color: white;
        padding: 30px;
        margin-bottom: 30px;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(78, 115, 223, 0.2);
    }

    .page-title {
        font-size: 28px;
        font-weight: 700;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .page-subtitle {
        font-size: 16px;
        opacity: 0.9;
        margin-top: 10px;
    }

    .stats-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        border-left: 4px solid;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100px;
        height: 100px;
        opacity: 0.1;
        border-radius: 50%;
        transform: translate(30px, -30px);
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    .stat-total {
        border-left-color: var(--primary);
    }
    .stat-total::before {
        background: var(--primary);
    }

    .stat-admins {
        border-left-color: var(--danger);
    }
    .stat-admins::before {
        background: var(--danger);
    }

    .stat-managers {
        border-left-color: var(--warning);
    }
    .stat-managers::before {
        background: var(--warning);
    }

    .stat-employees {
        border-left-color: var(--success);
    }
    .stat-employees::before {
        background: var(--success);
    }

    .stat-number {
        font-size: 36px;
        font-weight: 700;
        margin: 0;
        position: relative;
        z-index: 1;
    }

    .stat-label {
        font-size: 14px;
        color: var(--secondary);
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-top: 8px;
        position: relative;
        z-index: 1;
    }

    .content-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        overflow: hidden;
    }

    .card-header {
        background: linear-gradient(135deg, var(--light) 0%, #e3e6f0 100%);
        padding: 20px 30px;
        border-bottom: 1px solid #e3e6f0;
        display: flex;
        justify-content: between;
        align-items: center;
        flex-wrap: wrap;
        gap: 15px;
    }

    .card-title {
        font-size: 20px;
        font-weight: 700;
        color: var(--dark);
        margin: 0;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .card-actions {
        display: flex;
        gap: 10px;
        align-items: center;
        flex-wrap: wrap;
    }

    .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;
        cursor: pointer;
        font-size: 14px;
    }

    .btn-primary {
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
        color: white;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(78, 115, 223, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-success {
        background: linear-gradient(135deg, var(--success) 0%, #17a673 100%);
        color: white;
    }

    .btn-success:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(28, 200, 138, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-warning {
        background: linear-gradient(135deg, var(--warning) 0%, #dda20a 100%);
        color: white;
    }

    .btn-warning:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(246, 194, 62, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-danger {
        background: linear-gradient(135deg, var(--danger) 0%, #c92432 100%);
        color: white;
    }

    .btn-danger:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(231, 74, 59, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-sm {
        padding: 6px 12px;
        font-size: 12px;
    }

    .search-container {
        position: relative;
        flex: 1;
        max-width: 300px;
    }

    .search-input {
        width: 100%;
        padding: 10px 40px 10px 15px;
        border: 2px solid #e3e6f0;
        border-radius: 25px;
        background: white;
        transition: all 0.3s ease;
    }

    .search-input:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgba(78, 115, 223, 0.1);
    }

    .search-icon {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: var(--secondary);
    }

    .table-container {
        padding: 0;
        overflow-x: auto;
    }

    .users-table {
        width: 100%;
        border-collapse: collapse;
        margin: 0;
    }

    .users-table th {
        background: linear-gradient(135deg, var(--light) 0%, #e3e6f0 100%);
        color: var(--dark);
        font-weight: 700;
        padding: 18px 20px;
        text-align: left;
        border-bottom: 2px solid #e3e6f0;
        font-size: 12px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .users-table td {
        padding: 18px 20px;
        border-bottom: 1px solid #f8f9fc;
        vertical-align: middle;
    }

    .users-table tr:hover {
        background: var(--light);
    }

    .user-avatar {
        width: 45px;
        height: 45px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 700;
        font-size: 16px;
        margin-right: 15px;
    }

    .user-info {
        display: flex;
        align-items: center;
    }

    .user-details h6 {
        margin: 0;
        font-weight: 600;
        color: var(--dark);
        font-size: 15px;
    }

    .user-details small {
        color: var(--secondary);
        font-size: 13px;
    }

    .role-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 11px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .role-admin {
        background: rgba(231, 74, 59, 0.1);
        color: var(--danger);
        border: 1px solid rgba(231, 74, 59, 0.2);
    }

    .role-manager {
        background: rgba(246, 194, 62, 0.1);
        color: var(--warning);
        border: 1px solid rgba(246, 194, 62, 0.2);
    }

    .role-employee {
        background: rgba(28, 200, 138, 0.1);
        color: var(--success);
        border: 1px solid rgba(28, 200, 138, 0.2);
    }

    .status-active {
        color: var(--success);
        font-weight: 600;
    }

    .status-inactive {
        color: var(--secondary);
        font-weight: 600;
    }

    .action-buttons {
        display: flex;
        gap: 5px;
        align-items: center;
    }

    .empty-state {
        text-align: center;
        padding: 60px 30px;
        color: var(--secondary);
    }

    .empty-icon {
        font-size: 48px;
        margin-bottom: 20px;
        opacity: 0.5;
    }

    .filter-container {
        display: flex;
        gap: 15px;
        margin-bottom: 20px;
        flex-wrap: wrap;
        align-items: center;
    }

    .filter-select {
        padding: 8px 15px;
        border: 2px solid #e3e6f0;
        border-radius: 8px;
        background: white;
        font-size: 14px;
        transition: all 0.3s ease;
    }

    .filter-select:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgba(78, 115, 223, 0.1);
    }

    @media (max-width: 768px) {
        .stats-container {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .card-header {
            flex-direction: column;
            align-items: stretch;
        }
        
        .card-actions {
            justify-content: center;
        }
        
        .users-table {
            font-size: 14px;
        }
        
        .users-table th,
        .users-table td {
            padding: 12px 8px;
        }
        
        .user-avatar {
            width: 35px;
            height: 35px;
            font-size: 14px;
        }
    }

    @media (max-width: 480px) {
        .stats-container {
            grid-template-columns: 1fr;
        }
        
        .action-buttons {
            flex-direction: column;
            gap: 3px;
        }
        
        .btn-sm {
            padding: 4px 8px;
            font-size: 11px;
        }
    }
</style>
@endpush

@section('content')
<div class="user-management-container">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-users"></i>
                User Management
            </h1>
            <p class="page-subtitle">Manage system users, roles, and permissions</p>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-container">
            <div class="stat-card stat-total">
                <h3 class="stat-number">{{ $users->count() }}</h3>
                <p class="stat-label">Total Users</p>
            </div>
            <div class="stat-card stat-admins">
                <h3 class="stat-number">{{ $users->where('role', 'admin')->count() }}</h3>
                <p class="stat-label">Administrators</p>
            </div>
            <div class="stat-card stat-managers">
                <h3 class="stat-number">{{ $users->where('role', 'manager')->count() }}</h3>
                <p class="stat-label">Managers</p>
            </div>
            <div class="stat-card stat-employees">
                <h3 class="stat-number">{{ $users->where('role', 'employee')->count() }}</h3>
                <p class="stat-label">Employees</p>
            </div>
        </div>

        <!-- Users Table -->
        <div class="content-card">
            <div class="card-header">
                <h2 class="card-title">
                    <i class="fas fa-list"></i>
                    All Users
                </h2>
                <div class="card-actions">
                    <div class="search-container">
                        <input type="text" class="search-input" placeholder="Search users..." id="userSearch">
                        <i class="fas fa-search search-icon"></i>
                    </div>
                    <select class="filter-select" id="roleFilter">
                        <option value="">All Roles</option>
                        <option value="admin">Admin</option>
                        <option value="manager">Manager</option>
                        <option value="employee">Employee</option>
                    </select>
                    <a href="{{ route('masters.users.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        Add New User
                    </a>
                </div>
            </div>

            <div class="table-container">
                @if($users->count() > 0)
                <table class="users-table">
                    <thead>
                        <tr>
                            <th>User</th>
                            <th>Role</th>
                            <th>Email</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="usersTableBody">
                        @foreach($users as $user)
                        <tr data-role="{{ $user->role }}" data-name="{{ strtolower($user->name) }}" data-email="{{ strtolower($user->email) }}">
                            <td>
                                <div class="user-info">
                                    <div class="user-avatar">
                                        {{ strtoupper(substr($user->name, 0, 1)) }}
                                    </div>
                                    <div class="user-details">
                                        <h6>{{ $user->name }}</h6>
                                        <small>ID: {{ $user->id }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="role-badge role-{{ $user->role }}">
                                    {{ ucfirst($user->role) }}
                                </span>
                            </td>
                            <td>{{ $user->email }}</td>
                            <td>
                                <span class="status-active">
                                    <i class="fas fa-circle" style="font-size: 8px; margin-right: 5px;"></i>
                                    Active
                                </span>
                            </td>
                            <td>{{ $user->created_at->format('M d, Y') }}</td>
                            <td>
                                <div class="action-buttons">
                                    <a href="{{ route('masters.users.show', $user) }}" class="btn btn-sm btn-primary" title="View">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('masters.users.edit', $user) }}" class="btn btn-sm btn-warning" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    @if($user->id !== auth()->id())
                                    <form method="POST" action="{{ route('masters.users.destroy', $user) }}" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this user?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
                @else
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h4>No Users Found</h4>
                    <p>Start by adding your first user to the system.</p>
                    <a href="{{ route('masters.users.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        Add First User
                    </a>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('userSearch');
    const roleFilter = document.getElementById('roleFilter');
    const tableBody = document.getElementById('usersTableBody');
    const tableRows = tableBody ? tableBody.querySelectorAll('tr') : [];

    function filterUsers() {
        const searchTerm = searchInput.value.toLowerCase();
        const selectedRole = roleFilter.value;

        tableRows.forEach(row => {
            const name = row.dataset.name || '';
            const email = row.dataset.email || '';
            const role = row.dataset.role || '';

            const matchesSearch = name.includes(searchTerm) || email.includes(searchTerm);
            const matchesRole = !selectedRole || role === selectedRole;

            if (matchesSearch && matchesRole) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }

    if (searchInput) {
        searchInput.addEventListener('input', filterUsers);
    }

    if (roleFilter) {
        roleFilter.addEventListener('change', filterUsers);
    }
});
</script>
@endpush
