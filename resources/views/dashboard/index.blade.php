@extends('layouts.app')

@section('title', 'Dashboard - JMD Traders')
@section('page-title', 'Dashboard')

@section('content')
@hascompany
<!-- Welcome Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-gradient-primary text-white shadow">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <h2 class="text-white mb-0">Welcome back, {{ auth()->user()->name }}!</h2>
                        <p class="text-white-50 mb-0">Here's what's happening with your business today.</p>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-3x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Statistics Cards -->
<div class="row">
    @permission('sales.view')
    <!-- Total Quotations -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2 hover-shadow">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Quotations
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" data-stat="total_quotations">{{ number_format($stats['total_quotations']) }}</div>
                        <div class="text-xs text-muted mt-1">
                            <i class="fas fa-calendar"></i> All time
                        </div>
                    </div>
                    <div class="col-auto">
                        <div class="icon-circle bg-primary">
                            <i class="fas fa-file-invoice text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Revenue -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2 hover-shadow">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Monthly Revenue
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" data-stat="monthly_revenue">₹{{ number_format($stats['monthly_revenue'], 0) }}</div>
                        <div class="text-xs mt-1">
                            @if($stats['revenue_growth'] >= 0)
                                <span class="text-success"><i class="fas fa-arrow-up"></i> {{ number_format($stats['revenue_growth'], 1) }}%</span>
                            @else
                                <span class="text-danger"><i class="fas fa-arrow-down"></i> {{ number_format(abs($stats['revenue_growth']), 1) }}%</span>
                            @endif
                            <span class="text-muted">vs last month</span>
                        </div>
                    </div>
                    <div class="col-auto">
                        <div class="icon-circle bg-success">
                            <i class="fas fa-rupee-sign text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endpermission

    @permission('customers.view')
    <!-- Total Customers -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2 hover-shadow">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Total Customers
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" data-stat="total_customers">{{ number_format($stats['total_customers']) }}</div>
                        <div class="text-xs text-muted mt-1">
                            <i class="fas fa-users"></i> Active accounts
                        </div>
                    </div>
                    <div class="col-auto">
                        <div class="icon-circle bg-info">
                            <i class="fas fa-users text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endpermission

    <!-- Pending Quotations -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2 hover-shadow">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Pending Quotations
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" data-stat="pending_quotations">{{ number_format($stats['pending_quotations']) }}</div>
                        <div class="text-xs text-muted mt-1">
                            <i class="fas fa-clock"></i> Awaiting approval
                        </div>
                    </div>
                    <div class="col-auto">
                        <div class="icon-circle bg-warning">
                            <i class="fas fa-clock text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Analytics Section -->
<div class="row">
    <!-- Sales Trend Chart -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Sales Trend (Last 12 Months)</h6>
                <div class="dropdown no-arrow">
                    <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown">
                        <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right shadow">
                        <a class="dropdown-item" href="{{ route('reports.monthly') }}">View Detailed Report</a>
                        <a class="dropdown-item" href="{{ route('reports.sales') }}">Sales Analytics</a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="salesTrendChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Distribution Pie Chart -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Quotation Status Distribution</h6>
            </div>
            <div class="card-body">
                <div class="chart-pie pt-4 pb-2">
                    <canvas id="statusPieChart"></canvas>
                </div>
                <div class="mt-4 text-center small">
                    <span class="mr-2">
                        <i class="fas fa-circle text-primary"></i> Pending
                    </span>
                    <span class="mr-2">
                        <i class="fas fa-circle text-success"></i> Approved
                    </span>
                    <span class="mr-2">
                        <i class="fas fa-circle text-info"></i> Draft
                    </span>
                    <span class="mr-2">
                        <i class="fas fa-circle text-warning"></i> Rejected
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities and Quick Actions -->
<div class="row">
    <!-- Recent Quotations -->
    @permission('sales.view')
    <div class="col-lg-8 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-file-invoice mr-1"></i> Recent Quotations
                </h6>
                <a href="{{ route('entries.sales') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-eye mr-1"></i> View All
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" width="100%" cellspacing="0">
                        <thead class="thead-light">
                            <tr>
                                <th>Quotation #</th>
                                <th>Customer</th>
                                <th>Company</th>
                                <th>Date</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($recent_quotations as $quotation)
                            <tr>
                                <td>
                                    <strong class="text-primary">{{ $quotation->quotation_number }}</strong>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-light rounded-circle mr-2 d-flex align-items-center justify-content-center">
                                            <i class="fas fa-user text-muted"></i>
                                        </div>
                                        <div>
                                            <div class="font-weight-bold">{{ $quotation->customer->name ?? 'N/A' }}</div>
                                            <small class="text-muted">{{ $quotation->customer->phone ?? '' }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge badge-outline-primary">{{ $quotation->company->code ?? 'N/A' }}</span>
                                </td>
                                <td>
                                    <div>{{ $quotation->quotation_date ? $quotation->quotation_date->format('d M Y') : 'N/A' }}</div>
                                    <small class="text-muted">{{ $quotation->created_at->diffForHumans() }}</small>
                                </td>
                                <td>
                                    <strong class="text-success">₹{{ number_format($quotation->calculated_net_amount, 0) }}</strong>
                                </td>
                                <td>
                                    @php
                                        $statusColors = [
                                            'pending' => 'warning',
                                            'approved' => 'success',
                                            'rejected' => 'danger',
                                            'draft' => 'secondary'
                                        ];
                                        $statusColor = $statusColors[$quotation->status] ?? 'secondary';
                                    @endphp
                                    <span class="badge badge-{{ $statusColor }}">
                                        {{ ucfirst($quotation->status) }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('entries.sales.show', $quotation->id) }}" class="btn btn-outline-primary btn-sm" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('entries.sales.print', $quotation->id) }}" class="btn btn-outline-secondary btn-sm" title="Print" target="_blank">
                                            <i class="fas fa-print"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-inbox fa-2x mb-2"></i>
                                        <p>No quotations found</p>
                                    </div>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    @endpermission

    <!-- Enhanced Quick Actions & Top Customers -->
    <div class="col-lg-4 mb-4">
        <!-- Quick Actions -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-bolt mr-1"></i> Quick Actions
                </h6>
            </div>
            <div class="card-body">
                @hascompany
                    <div class="d-grid gap-2">
                        @permission('sales.create')
                            <a href="{{ route('entries.sales.create') }}" class="btn btn-primary btn-block mb-2">
                                <i class="fas fa-plus mr-2"></i> New Quotation
                            </a>
                        @endpermission

                        @permission('transport.create')
                            <a href="{{ route('entries.transport.create') }}" class="btn btn-info btn-block mb-2">
                                <i class="fas fa-truck mr-2"></i> New Transport
                            </a>
                        @endpermission

                        @permission('customers.create')
                            <a href="{{ route('customers.create') }}" class="btn btn-success btn-block mb-2">
                                <i class="fas fa-user-plus mr-2"></i> Add Customer
                            </a>
                        @endpermission

                        @permission('products.create')
                            <a href="{{ route('products.create') }}" class="btn btn-warning btn-block mb-2">
                                <i class="fas fa-box mr-2"></i> Add Product
                            </a>
                        @endpermission

                        @permission('reports.view')
                            <a href="{{ route('reports.daily') }}" class="btn btn-secondary btn-block">
                                <i class="fas fa-chart-bar mr-2"></i> View Reports
                            </a>
                        @endpermission
                    </div>
                @else
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>No Company Access</strong><br>
                        You don't have access to any company or no company is selected. Please contact your administrator.
                    </div>
                @endhascompany
            </div>
        </div>

        <!-- Top Customers -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-crown mr-1"></i> Top Customers
                </h6>
            </div>
            <div class="card-body">
                @forelse($topCustomers as $customer)
                    <div class="d-flex align-items-center mb-3">
                        <div class="avatar-sm bg-primary rounded-circle mr-3 d-flex align-items-center justify-content-center">
                            <span class="text-white font-weight-bold">{{ substr($customer->name, 0, 1) }}</span>
                        </div>
                        <div class="flex-grow-1">
                            <div class="font-weight-bold">{{ $customer->name }}</div>
                            <small class="text-muted">₹{{ number_format($customer->total_revenue ?? 0, 0) }} revenue</small>
                        </div>
                        <div class="text-right">
                            <a href="{{ route('customers.show', $customer->id) }}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                    </div>
                @empty
                    <div class="text-center text-muted">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <p>No customer data available</p>
                    </div>
                @endforelse
            </div>
        </div>
    </div>
</div>

<!-- Additional Statistics and Transport Section -->
<div class="row">
    <!-- Transport Overview -->
    @permission('transport.view')
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-truck mr-1"></i> Transport Overview
                </h6>
                <a href="{{ route('entries.transport') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-eye mr-1"></i> View All
                </a>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 font-weight-bold text-info">{{ number_format($stats['total_transport']) }}</div>
                            <div class="text-muted">Total Transports</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 font-weight-bold text-warning">{{ number_format($stats['in_transit_shipments']) }}</div>
                            <div class="text-muted">In Transit</div>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-12">
                        <div class="text-center">
                            <div class="h5 font-weight-bold text-success">{{ number_format($stats['delivered_today']) }}</div>
                            <div class="text-muted">Delivered Today</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endpermission

    <!-- Monthly Performance -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-line mr-1"></i> Monthly Performance
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 font-weight-bold text-primary">{{ number_format($stats['monthly_sales']) }}</div>
                            <div class="text-muted">This Month Sales</div>
                            @if($stats['sales_growth'] >= 0)
                                <small class="text-success"><i class="fas fa-arrow-up"></i> {{ number_format($stats['sales_growth'], 1) }}%</small>
                            @else
                                <small class="text-danger"><i class="fas fa-arrow-down"></i> {{ number_format(abs($stats['sales_growth']), 1) }}%</small>
                            @endif
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 font-weight-bold text-success">{{ number_format($stats['approved_quotations']) }}</div>
                            <div class="text-muted">Approved Quotations</div>
                            <small class="text-muted">{{ $stats['total_quotations'] > 0 ? number_format(($stats['approved_quotations'] / $stats['total_quotations']) * 100, 1) : 0 }}% approval rate</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Recent Activity</h6>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">New quotation created</h6>
                            <p class="timeline-text">Quotation #QT-2024-001 created for ABC Company</p>
                            <small class="text-muted">2 hours ago</small>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">Customer added</h6>
                            <p class="timeline-text">New customer "XYZ Enterprises" added to system</p>
                            <small class="text-muted">4 hours ago</small>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-marker bg-info"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">Product updated</h6>
                            <p class="timeline-text">Product "Steel Pipes" inventory updated</p>
                            <small class="text-muted">6 hours ago</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@else
<!-- No Company Access Message -->
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-lg border-0">
                <div class="card-body text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-building fa-4x text-muted mb-3"></i>
                        <h2 class="text-muted">No Company Access</h2>
                    </div>

                    <div class="alert alert-warning">
                        <h5 class="alert-heading">
                            <i class="fas fa-exclamation-triangle"></i>
                            Access Required
                        </h5>
                        <p class="mb-3">
                            You are not currently assigned to any company or your company access is inactive.
                            To access the dashboard and application features, you need to be assigned to an active company.
                        </p>
                        <hr>
                        <p class="mb-0">
                            <strong>What to do:</strong> Please contact your system administrator to assign you to a company.
                        </p>
                    </div>

                    <div class="mt-4">
                        <h6 class="text-muted mb-3">Available Actions:</h6>
                        <div class="d-flex justify-content-center gap-3">
                            <a href="{{ route('profile.edit') }}" class="btn btn-outline-primary">
                                <i class="fas fa-user"></i> View Profile
                            </a>
                            <a href="{{ route('logout') }}" class="btn btn-outline-secondary"
                               onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </div>
                    </div>

                    <div class="mt-4 text-muted">
                        <small>
                            <i class="fas fa-info-circle"></i>
                            If you believe this is an error, please contact your administrator.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endhascompany
@endsection

@push('styles')
<style>
    /* Enhanced Card Styles */
    .border-left-primary {
        border-left: 0.25rem solid #4e73df !important;
    }
    .border-left-success {
        border-left: 0.25rem solid #1cc88a !important;
    }
    .border-left-info {
        border-left: 0.25rem solid #36b9cc !important;
    }
    .border-left-warning {
        border-left: 0.25rem solid #f6c23e !important;
    }

    /* Hover Effects */
    .hover-shadow:hover {
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
        transform: translateY(-2px);
        transition: all 0.3s ease;
    }

    /* Icon Circles */
    .icon-circle {
        width: 3rem;
        height: 3rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Avatar Styles */
    .avatar-sm {
        width: 2rem;
        height: 2rem;
    }

    /* Badge Outline */
    .badge-outline-primary {
        color: #4e73df;
        border: 1px solid #4e73df;
        background: transparent;
    }

    /* Gradient Background */
    .bg-gradient-primary {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    }

    /* Chart Containers */
    .chart-area {
        position: relative;
        height: 20rem;
        width: 100%;
    }

    .chart-pie {
        position: relative;
        height: 15rem;
        width: 100%;
    }

    /* Table Enhancements */
    .table-hover tbody tr:hover {
        background-color: rgba(78, 115, 223, 0.05);
    }

    .thead-light th {
        background-color: #f8f9fc;
        border-color: #e3e6f0;
        font-weight: 600;
        font-size: 0.8rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* Button Enhancements */
    .btn-block {
        border-radius: 0.35rem;
        font-weight: 500;
        padding: 0.75rem 1rem;
    }

    .btn-group-sm > .btn, .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        border-radius: 0.2rem;
    }

    /* Card Header Enhancements */
    .card-header {
        background-color: #f8f9fc;
        border-bottom: 1px solid #e3e6f0;
    }

    /* Animation Classes */
    .fade-in {
        animation: fadeIn 0.5s ease-in;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
        .icon-circle {
            width: 2.5rem;
            height: 2.5rem;
        }

        .chart-area {
            height: 15rem;
        }

        .chart-pie {
            height: 12rem;
        }
    }

    /* Status Colors */
    .text-pending { color: #f6c23e; }
    .text-approved { color: #1cc88a; }
    .text-rejected { color: #e74a3b; }
    .text-draft { color: #6c757d; }

    /* Loading Animation */
    .loading-spinner {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #4e73df;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Sales Trend Chart
    const salesTrendCtx = document.getElementById('salesTrendChart').getContext('2d');
    const salesTrendData = @json($salesTrendData);

    new Chart(salesTrendCtx, {
        type: 'line',
        data: {
            labels: salesTrendData.map(item => item.month),
            datasets: [{
                label: 'Sales Count',
                data: salesTrendData.map(item => item.sales),
                borderColor: '#4e73df',
                backgroundColor: 'rgba(78, 115, 223, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#4e73df',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 5,
                pointHoverRadius: 7
            }, {
                label: 'Revenue (₹)',
                data: salesTrendData.map(item => item.revenue),
                borderColor: '#1cc88a',
                backgroundColor: 'rgba(28, 200, 138, 0.1)',
                borderWidth: 3,
                fill: false,
                tension: 0.4,
                pointBackgroundColor: '#1cc88a',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 5,
                pointHoverRadius: 7,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            if (context.datasetIndex === 1) {
                                return 'Revenue: ₹' + new Intl.NumberFormat('en-IN').format(context.parsed.y);
                            }
                            return 'Sales: ' + context.parsed.y;
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    title: {
                        display: true,
                        text: 'Sales Count'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    grid: {
                        drawOnChartArea: false,
                    },
                    title: {
                        display: true,
                        text: 'Revenue (₹)'
                    },
                    ticks: {
                        callback: function(value) {
                            return '₹' + new Intl.NumberFormat('en-IN', { notation: 'compact' }).format(value);
                        }
                    }
                }
            },
            interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
            }
        }
    });

    // Status Distribution Pie Chart
    const statusPieCtx = document.getElementById('statusPieChart').getContext('2d');
    const statusData = @json($statusDistribution);

    new Chart(statusPieCtx, {
        type: 'doughnut',
        data: {
            labels: ['Pending', 'Approved', 'Draft', 'Rejected'],
            datasets: [{
                data: [
                    statusData.pending,
                    statusData.approved,
                    statusData.draft,
                    statusData.rejected
                ],
                backgroundColor: [
                    '#f6c23e',
                    '#1cc88a',
                    '#6c757d',
                    '#e74a3b'
                ],
                borderColor: '#ffffff',
                borderWidth: 2,
                hoverBorderWidth: 3
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                        }
                    }
                }
            },
            cutout: '60%'
        }
    });

    // Auto-refresh dashboard data every 5 minutes
    setInterval(function() {
        refreshDashboardStats();
    }, 300000);

    // Refresh dashboard statistics
    function refreshDashboardStats() {
        fetch('/dashboard/data?type=stats')
            .then(response => response.json())
            .then(data => {
                // Update statistics cards
                updateStatCard('total_quotations', data.total_quotations);
                updateStatCard('pending_quotations', data.pending_quotations);
                updateStatCard('total_customers', data.total_customers);
                updateStatCard('total_products', data.total_products);
            })
            .catch(error => {
                console.error('Error refreshing dashboard data:', error);
            });
    }

    function updateStatCard(statName, value) {
        const element = document.querySelector(`[data-stat="${statName}"]`);
        if (element) {
            element.textContent = new Intl.NumberFormat('en-IN').format(value);
            element.classList.add('fade-in');
        }
    }

    // Add fade-in animation to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('fade-in');
        }, index * 100);
    });

    // Show welcome notification
    if (typeof showNotification === 'function') {
        showNotification('Welcome to JMD Traders Dashboard!', 'success');
    }
});
</script>
@endpush
