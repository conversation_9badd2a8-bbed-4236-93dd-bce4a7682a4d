@extends('layouts.app')

@section('title', 'Profile - JMD Traders')
@section('page-title', 'My Profile')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Profile Header -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-user"></i> Profile Information
                    </h6>
                    <a href="{{ route('profile.edit') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-edit"></i> Edit Profile
                    </a>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 text-center">
                            <div class="profile-avatar mb-3">
                                <i class="fas fa-user-circle fa-5x text-muted"></i>
                            </div>
                            <h5 class="mb-1">{{ $user->name }}</h5>
                            <p class="text-muted">{{ $user->email }}</p>
                        </div>
                        <div class="col-md-8">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Name:</strong></td>
                                    <td>{{ $user->name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td>{{ $user->email }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Phone:</strong></td>
                                    <td>{{ $user->phone ?? 'Not provided' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        <span class="badge badge-{{ $user->status == 'active' ? 'success' : 'secondary' }}">
                                            {{ ucfirst($user->status) }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Member Since:</strong></td>
                                    <td>{{ $user->created_at->format('F j, Y') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Current Company -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-building"></i> Current Company
                    </h6>
                </div>
                <div class="card-body">
                    @if($user->currentCompany)
                        <div class="row">
                            <div class="col-md-6">
                                <h5>{{ $user->currentCompany->name }}</h5>
                                @if($user->currentCompany->description)
                                    <p class="text-muted">{{ $user->currentCompany->description }}</p>
                                @endif
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Your Role:</strong></td>
                                        <td>
                                            @if($user->userRole)
                                                <span class="badge badge-{{ $user->role_badge }}">
                                                    {{ $user->userRole->name }}
                                                </span>
                                            @else
                                                <span class="text-muted">No role assigned</span>
                                            @endif
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Company Status:</strong></td>
                                        <td>
                                            <span class="badge badge-{{ $user->currentCompany->status == 'active' ? 'success' : 'secondary' }}">
                                                {{ ucfirst($user->currentCompany->status) }}
                                            </span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    @else
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>No Company Selected</strong><br>
                            You are not currently assigned to any company. Contact your administrator.
                        </div>
                    @endif
                </div>
            </div>

            <!-- All Companies -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-building"></i> All Company Assignments
                    </h6>
                </div>
                <div class="card-body">
                    @if($user->companies->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Company</th>
                                        <th>Role</th>
                                        <th>Status</th>
                                        <th>Default</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($user->companies as $company)
                                    <tr class="{{ $company->id == $user->current_company_id ? 'table-success' : '' }}">
                                        <td>
                                            <strong>{{ $company->name }}</strong>
                                            @if($company->id == $user->current_company_id)
                                                <span class="badge badge-primary ml-2">Current</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($company->pivot->role_id)
                                                @php
                                                    $role = \App\Models\Role::find($company->pivot->role_id);
                                                @endphp
                                                @if($role)
                                                    <span class="badge badge-info">{{ $role->name }}</span>
                                                @else
                                                    <span class="text-muted">Role not found</span>
                                                @endif
                                            @else
                                                <span class="text-muted">No role assigned</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge badge-{{ $company->pivot->status == 'active' ? 'success' : 'secondary' }}">
                                                {{ ucfirst($company->pivot->status) }}
                                            </span>
                                        </td>
                                        <td>
                                            @if($company->pivot->is_default)
                                                <i class="fas fa-check text-success"></i>
                                            @else
                                                <i class="fas fa-times text-muted"></i>
                                            @endif
                                        </td>
                                        <td>
                                            @if($company->id != $user->current_company_id && $company->pivot->status == 'active')
                                                <form method="POST" action="{{ route('company.switch') }}" style="display: inline;">
                                                    @csrf
                                                    <input type="hidden" name="company_id" value="{{ $company->id }}">
                                                    <button type="submit" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-exchange-alt"></i> Switch
                                                    </button>
                                                </form>
                                            @endif
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            You are not assigned to any companies yet. Contact your administrator.
                        </div>
                    @endif
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-bolt"></i> Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <a href="{{ route('profile.edit') }}" class="btn btn-primary btn-block">
                                <i class="fas fa-edit"></i> Edit Profile
                            </a>
                        </div>
                        
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .table-success {
        background-color: rgba(40, 167, 69, 0.1);
    }
    
    .profile-avatar {
        margin-bottom: 1rem;
    }
    
    .badge {
        font-size: 0.75em;
    }
    
    .card {
        border-radius: 10px;
    }
    
    .btn {
        border-radius: 6px;
    }
</style>
@endpush
