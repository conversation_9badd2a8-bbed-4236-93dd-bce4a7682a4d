@extends('layouts.app')

@section('title', 'Edit Profile - JMD Traders')
@section('page-title', 'Edit Profile')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-edit"></i> Edit Profile Information
                    </h6>
                    <a href="{{ route('profile.show') }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i> Back to Profile
                    </a>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('profile.update') }}">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <div class="col-md-6">
                                <!-- Name -->
                                <div class="form-group">
                                    <label for="name" class="form-label">Full Name *</label>
                                    <input type="text" 
                                           class="form-control @error('name') is-invalid @enderror" 
                                           id="name" 
                                           name="name" 
                                           value="{{ old('name', $user->name) }}" 
                                           required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <!-- Email -->
                                <div class="form-group">
                                    <label for="email" class="form-label">Email Address *</label>
                                    <input type="email" 
                                           class="form-control @error('email') is-invalid @enderror" 
                                           id="email" 
                                           name="email" 
                                           value="{{ old('email', $user->email) }}" 
                                           required>
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <!-- Phone -->
                                <div class="form-group">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="text" 
                                           class="form-control @error('phone') is-invalid @enderror" 
                                           id="phone" 
                                           name="phone" 
                                           value="{{ old('phone', $user->phone) }}" 
                                           placeholder="Enter phone number">
                                    @error('phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <!-- Current Status (Read-only) -->
                                <div class="form-group">
                                    <label class="form-label">Account Status</label>
                                    <div class="form-control-plaintext">
                                        <span class="badge badge-{{ $user->status == 'active' ? 'success' : 'secondary' }}">
                                            {{ ucfirst($user->status) }}
                                        </span>
                                        <small class="text-muted ml-2">Contact admin to change status</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <!-- Password Change Section -->
                        <h6 class="text-muted mb-3">
                            <i class="fas fa-lock"></i> Change Password (Optional)
                        </h6>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <!-- Current Password -->
                                <div class="form-group">
                                    <label for="current_password" class="form-label">Current Password</label>
                                    <input type="password" 
                                           class="form-control @error('current_password') is-invalid @enderror" 
                                           id="current_password" 
                                           name="current_password" 
                                           placeholder="Enter current password">
                                    @error('current_password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">Required only if changing password</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <!-- New Password -->
                                <div class="form-group">
                                    <label for="password" class="form-label">New Password</label>
                                    <input type="password" 
                                           class="form-control @error('password') is-invalid @enderror" 
                                           id="password" 
                                           name="password" 
                                           placeholder="Enter new password">
                                    @error('password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">Leave blank to keep current password</small>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <!-- Confirm Password -->
                                <div class="form-group">
                                    <label for="password_confirmation" class="form-label">Confirm New Password</label>
                                    <input type="password" 
                                           class="form-control" 
                                           id="password_confirmation" 
                                           name="password_confirmation" 
                                           placeholder="Confirm new password">
                                    <small class="form-text text-muted">Must match new password</small>
                                </div>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <!-- Current Company Info (Read-only) -->
                        <h6 class="text-muted mb-3">
                            <i class="fas fa-building"></i> Company Information
                        </h6>
                        
                        <div class="alert alert-info">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Current Company:</strong><br>
                                    @if($user->currentCompany)
                                        {{ $user->currentCompany->name }}
                                    @else
                                        <span class="text-muted">No company assigned</span>
                                    @endif
                                </div>
                                <div class="col-md-6">
                                    <strong>Your Role:</strong><br>
                                    @if($user->userRole)
                                        <span class="badge badge-{{ $user->role_badge }}">
                                            {{ $user->userRole->name }}
                                        </span>
                                    @else
                                        <span class="text-muted">No role assigned</span>
                                    @endif
                                </div>
                            </div>
                            <hr class="my-2">
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i>
                                Company assignments and roles are managed by administrators. 
                                Contact your admin if you need changes.
                            </small>
                        </div>
                        
                        <!-- Submit Buttons -->
                        <div class="form-group mt-4">
                            <div class="d-flex justify-content-between">
                                <a href="{{ route('profile.show') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Update Profile
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    
    .badge {
        font-size: 0.8em;
    }
    
    .card {
        border-radius: 10px;
    }
    
    .btn {
        border-radius: 6px;
        padding: 0.5rem 1rem;
    }
    
    .alert {
        border-radius: 8px;
    }
    
    hr {
        margin: 2rem 0;
    }
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show/hide password confirmation based on new password input
    const passwordInput = document.getElementById('password');
    const confirmPasswordGroup = document.getElementById('password_confirmation').closest('.form-group');
    const currentPasswordGroup = document.getElementById('current_password').closest('.form-group');
    
    passwordInput.addEventListener('input', function() {
        if (this.value.length > 0) {
            confirmPasswordGroup.style.display = 'block';
            currentPasswordGroup.querySelector('input').required = true;
        } else {
            confirmPasswordGroup.style.display = 'block'; // Keep visible for better UX
            currentPasswordGroup.querySelector('input').required = false;
        }
    });
});
</script>
@endpush
