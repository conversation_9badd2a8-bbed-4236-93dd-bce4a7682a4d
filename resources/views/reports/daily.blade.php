@extends('layouts.app')

@section('title', 'Daily Report - JMD Traders')
@section('page-title', 'Daily Report')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Daily Business Report</h6>
                <div class="d-flex gap-2">
                    <input type="date" class="form-control" id="reportDate" value="{{ $selected_date->format('Y-m-d') }}" onchange="loadReport()">
                    <button class="btn btn-success d-none" onclick="exportReport()">
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- Date Navigation -->
                <div class="row mb-4">
                    <div class="col-12 text-center">
                        <div class="btn-group" role="group">
                            <button class="btn btn-outline-primary" onclick="changeDate(-1)">
                                <i class="fas fa-chevron-left"></i> Previous Day
                            </button>
                            <button class="btn btn-outline-primary" onclick="setToday()">
                                Today
                            </button>
                            <button class="btn btn-outline-primary" onclick="changeDate(1)">
                                Next Day <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Summary Cards -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            Quotations Created
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $daily_stats['quotations_created'] }}</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-file-invoice fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            Quotations Approved
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $daily_stats['quotations_approved'] }}</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            Transports Scheduled
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $daily_stats['transports_scheduled'] }}</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-truck fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            Total Value
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">₹{{ number_format($daily_stats['total_sales_value'], 2) }}</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-rupee-sign fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Detailed Tables -->
                <div class="row">
                    <!-- Quotations Created Today -->
                    <div class="col-lg-6 mb-4">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">Quotations Created Today</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-sm">
                                        <thead>
                                            <tr>
                                                <th>Quotation #</th>
                                                <th>Customer</th>
                                                <th>Amount</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse($daily_quotations as $quotation)
                                            <tr>
                                                <td>{{ $quotation->quotation_number ?? '' }}</td>
                                                <td>{{ $quotation->customer->name }}</td>
                                                <td>₹{{ number_format($quotation->total_amount, 2) }}</td>
                                                <td>
                                                    <span class="badge bg-{{ $quotation->status_badge }}">
                                                        {{ ucfirst($quotation->status) }}
                                                    </span>
                                                </td>
                                            </tr>
                                            @empty
                                            <tr>
                                                <td colspan="4" class="text-center">No quotations created today</td>
                                            </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Transports Scheduled Today -->
                    <div class="col-lg-6 mb-4">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">Transports Scheduled Today</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-sm">
                                        <thead>
                                            <tr>
                                                <th>Quotation #</th>
                                                <th>Vehicle</th>
                                                <th>Driver</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse($daily_transports as $transport)
                                            <tr>
                                                <td>{{ $transport->salesEntry->quotation_number ?? '' }}</td>
                                                <td>{{ $transport->vehicle_number }}</td>
                                                <td>{{ $transport->driver_name }}</td>
                                                <td>
                                                    <span class="badge bg-{{ $transport->status_badge }}">
                                                        {{ ucfirst(str_replace('_', ' ', $transport->status)) }}
                                                    </span>
                                                </td>
                                            </tr>
                                            @empty
                                            <tr>
                                                <td colspan="4" class="text-center">No transports scheduled today</td>
                                            </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Customer Activity -->
                <div class="row">
                    <div class="col-12">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">Customer Activity Today</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Customer</th>
                                                <th>Quotations</th>
                                                <th>Total Value</th>
                                                <th>Last Activity</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse($customerActivity as $activity)
                                            <tr>
                                                <td>{{ $activity->customer_name }}</td>
                                                <td>{{ $activity->quotation_count }}</td>
                                                <td>₹{{ number_format($activity->total_value, 2) }}</td>
                                                <td>{{ $activity->last_activity }}</td>
                                            </tr>
                                            @empty
                                            <tr>
                                                <td colspan="4" class="text-center">No customer activity today</td>
                                            </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    function loadReport() {
        const date = document.getElementById('reportDate').value;
        window.location.href = `{{ route('reports.daily') }}?date=${date}`;
    }

    function changeDate(days) {
        const currentDate = new Date(document.getElementById('reportDate').value);
        currentDate.setDate(currentDate.getDate() + days);
        const newDate = currentDate.toISOString().split('T')[0];
        document.getElementById('reportDate').value = newDate;
        loadReport();
    }

    function setToday() {
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('reportDate').value = today;
        loadReport();
    }

    function exportReport() {
        const date = document.getElementById('reportDate').value;
        window.open(`{{ route('reports.daily') }}/export?date=${date}`, '_blank');
    }

    // Auto-refresh every 5 minutes
    setInterval(function() {
        if (document.getElementById('reportDate').value === new Date().toISOString().split('T')[0]) {
            location.reload();
        }
    }, 300000);
</script>
@endpush
