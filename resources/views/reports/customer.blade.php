@extends('layouts.app')

@section('title', 'Customer Report - JMD Traders')
@section('page-title', 'Customer Report')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Customer Report</h6>
                <div class="d-flex gap-2">
                    <button class="btn btn-success d-none" onclick="exportReport()">
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- Summary Cards -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            Total Customers
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $summary['total_customers'] }}</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-users fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            Active Customers
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $summary['active_customers'] }}</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-user-check fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            Customers with Quotations
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $summary['customers_with_quotations'] }}</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-file-invoice fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            Avg. Quotations per Customer
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($summary['avg_quotations_per_customer'], 1) }}</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-chart-bar fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Customer List -->
                <div class="row">
                    <div class="col-12">
                        <div class="card shadow">
                            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                                <h6 class="m-0 font-weight-bold text-primary">Customer Details</h6>
                                <div class="d-flex gap-2">
                                    <select class="form-control" id="sortBy" onchange="changeSorting()">
                                        <option value="total_value" {{ $sort_by == 'total_value' ? 'selected' : '' }}>Sort by Total Value</option>
                                        <option value="quotation_count" {{ $sort_by == 'quotation_count' ? 'selected' : '' }}>Sort by Quotation Count</option>
                                        <option value="avg_value" {{ $sort_by == 'avg_value' ? 'selected' : '' }}>Sort by Average Value</option>
                                        <option value="last_quotation_date" {{ $sort_by == 'last_quotation_date' ? 'selected' : '' }}>Sort by Last Activity</option>
                                        <option value="name" {{ $sort_by == 'name' ? 'selected' : '' }}>Sort by Name</option>
                                    </select>
                                    <select class="form-control" id="sortOrder" onchange="changeSorting()">
                                        <option value="desc" {{ $order == 'desc' ? 'selected' : '' }}>Descending</option>
                                        <option value="asc" {{ $order == 'asc' ? 'selected' : '' }}>Ascending</option>
                                    </select>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="customerTable" width="100%" cellspacing="0">
                                        <thead>
                                            <tr>
                                                <th>Customer Name</th>
                                                <th>Email</th>
                                                <th>Phone</th>
                                                <th>Status</th>
                                                <th>Quotations</th>
                                                <th>Total Value</th>
                                                <th>Avg. Value</th>
                                                <th>Last Activity</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse($customers as $customer)
                                            <tr>
                                                <td>
                                                    <strong>{{ $customer->name }}</strong>
                                                    @if($customer->company)
                                                        <br><small class="text-muted">{{ $customer->company->name }}</small>
                                                    @endif
                                                </td>
                                                <td>{{ $customer->email ?? 'N/A' }}</td>
                                                <td>{{ $customer->phone ?? 'N/A' }}</td>
                                                <td>
                                                    <span class="badge badge-{{ $customer->status_badge }}">
                                                        {{ ucfirst($customer->status) }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge badge-primary">{{ $customer->quotation_count ?? 0 }}</span>
                                                </td>
                                                <td>
                                                    <strong>₹{{ number_format($customer->total_value ?? 0, 2) }}</strong>
                                                </td>
                                                <td>
                                                    ₹{{ number_format($customer->avg_value ?? 0, 2) }}
                                                </td>
                                                <td>
                                                    @if($customer->last_quotation_date)
                                                        {{ \Carbon\Carbon::parse($customer->last_quotation_date)->format('d M Y') }}
                                                    @else
                                                        <span class="text-muted">No activity</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    <a href="{{ route('customers.show', $customer->id) }}" class="btn btn-sm btn-info">
                                                        <i class="fas fa-eye"></i> View
                                                    </a>
                                                </td>
                                            </tr>
                                            @empty
                                            <tr>
                                                <td colspan="9" class="text-center">No customers found</td>
                                            </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Pagination -->
                                <div class="d-flex justify-content-center">
                                    {{ $customers->appends(request()->query())->links() }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function changeSorting() {
    const sortBy = document.getElementById('sortBy').value;
    const sortOrder = document.getElementById('sortOrder').value;
    
    const url = new URL(window.location);
    url.searchParams.set('sort_by', sortBy);
    url.searchParams.set('order', sortOrder);
    
    window.location.href = url.toString();
}

function exportReport() {
    // You can implement export functionality here
    alert('Export functionality can be implemented based on your requirements');
}
</script>
@endsection
