@extends('layouts.app')

@section('title', 'Sales Report - JMD Traders')
@section('page-title', 'Sales Report')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Sales Performance Report</h6>
                <div class="d-flex gap-2">
                    <select class="form-control" id="periodSelect" onchange="changePeriod()">
                        <option value="monthly" {{ $period == 'monthly' ? 'selected' : '' }}>Monthly View</option>
                        <option value="quarterly" {{ $period == 'quarterly' ? 'selected' : '' }}>Quarterly View</option>
                    </select>
                    <select class="form-control" id="yearSelect" onchange="changeYear()">
                        @for($y = date('Y'); $y >= date('Y') - 5; $y--)
                            <option value="{{ $y }}" {{ $year == $y ? 'selected' : '' }}>{{ $y }}</option>
                        @endfor
                    </select>
                    <button class="btn btn-success d-none" onclick="exportReport()">
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- Sales Performance Chart -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    {{ ucfirst($period) }} Sales Performance - {{ $year }}
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="chart-area">
                                    <canvas id="salesChart"></canvas>
                                </div>
                                
                                <!-- Sales Data Table -->
                                <div class="table-responsive mt-4">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>{{ $period == 'monthly' ? 'Month' : 'Quarter' }}</th>
                                                <th>Quotations</th>
                                                <th>Sales Value</th>
                                                <th>Avg. Value</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse($sales_data as $data)
                                            <tr>
                                                <td>
                                                    @if($period == 'monthly')
                                                        {{ DateTime::createFromFormat('!m', $data->period)->format('F') }}
                                                    @else
                                                        Q{{ $data->period }}
                                                    @endif
                                                </td>
                                                <td>{{ $data->quotations }}</td>
                                                <td>₹{{ number_format($data->sales_value, 2) }}</td>
                                                <td>₹{{ number_format($data->quotations > 0 ? $data->sales_value / $data->quotations : 0, 2) }}</td>
                                            </tr>
                                            @empty
                                            <tr>
                                                <td colspan="4" class="text-center">No sales data available for {{ $year }}</td>
                                            </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Top Products and Customers -->
                <div class="row">
                    <!-- Top Products -->
                    <div class="col-xl-6 col-lg-6">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">Top Products by Sales Value</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Product</th>
                                                <th>Quantity Sold</th>
                                                <th>Total Value</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse($top_products as $product)
                                            <tr>
                                                <td>
                                                    <strong>{{ $product->name }}</strong>
                                                    <br><small class="text-muted">{{ $product->code }}</small>
                                                </td>
                                                <td>{{ number_format($product->total_quantity ?? 0) }}</td>
                                                <td>₹{{ number_format($product->total_value ?? 0, 2) }}</td>
                                            </tr>
                                            @empty
                                            <tr>
                                                <td colspan="3" class="text-center">No product sales data available</td>
                                            </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Top Customers -->
                    <div class="col-xl-6 col-lg-6">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">Top Customers by Sales Value</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Customer</th>
                                                <th>Quotations</th>
                                                <th>Total Value</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse($sales_by_customer as $customer)
                                            <tr>
                                                <td>
                                                    <strong>{{ $customer->name }}</strong>
                                                    @if($customer->email)
                                                        <br><small class="text-muted">{{ $customer->email }}</small>
                                                    @endif
                                                </td>
                                                <td>{{ $customer->quotation_count ?? 0 }}</td>
                                                <td>₹{{ number_format($customer->total_value ?? 0, 2) }}</td>
                                            </tr>
                                            @empty
                                            <tr>
                                                <td colspan="3" class="text-center">No customer sales data available</td>
                                            </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Sales Chart
const ctx = document.getElementById('salesChart').getContext('2d');
const salesData = @json($sales_data);

const labels = salesData.map(item => {
    if ('{{ $period }}' === 'monthly') {
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        return months[item.period - 1];
    } else {
        return 'Q' + item.period;
    }
});

const quotationsData = salesData.map(item => item.quotations);
const salesValueData = salesData.map(item => item.sales_value);

const salesChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: labels,
        datasets: [{
            label: 'Quotations',
            data: quotationsData,
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            yAxisID: 'y'
        }, {
            label: 'Sales Value (₹)',
            data: salesValueData,
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            yAxisID: 'y1'
        }]
    },
    options: {
        responsive: true,
        interaction: {
            mode: 'index',
            intersect: false,
        },
        scales: {
            x: {
                display: true,
                title: {
                    display: true,
                    text: '{{ ucfirst($period) }}'
                }
            },
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                title: {
                    display: true,
                    text: 'Quotations'
                }
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                title: {
                    display: true,
                    text: 'Sales Value (₹)'
                },
                grid: {
                    drawOnChartArea: false,
                },
            }
        }
    }
});

function changePeriod() {
    const period = document.getElementById('periodSelect').value;
    const year = document.getElementById('yearSelect').value;
    
    const url = new URL(window.location);
    url.searchParams.set('period', period);
    url.searchParams.set('year', year);
    
    window.location.href = url.toString();
}

function changeYear() {
    const period = document.getElementById('periodSelect').value;
    const year = document.getElementById('yearSelect').value;
    
    const url = new URL(window.location);
    url.searchParams.set('period', period);
    url.searchParams.set('year', year);
    
    window.location.href = url.toString();
}

function exportReport() {
    // You can implement export functionality here
    alert('Export functionality can be implemented based on your requirements');
}
</script>
@endsection
