@extends('layouts.app')

@section('title', 'Quotation Report - JMD Traders')
@section('page-title', 'Quotation Report')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-chart-bar text-primary"></i> Quotation Report
            </h1>
            <p class="mb-0 text-muted">Comprehensive analysis of quotations and sales performance</p>
        </div>
        <div class="d-flex gap-2 ">
            <button type="button" class="btn btn-success btn-sm d-none" onclick="exportToExcel()">
                <i class="fas fa-file-excel"></i> Export Excel
            </button>
            <button type="button" class="btn btn-danger btn-sm d-none" onclick="exportToPdf()">
                <i class="fas fa-file-pdf"></i> Export PDF
            </button>
            <a href="{{ route('entries.sales.create') }}" class="btn btn-primary btn-sm">
                <i class="fas fa-plus"></i> New Quotation
            </a>
        </div>
    </div>

    <!-- Advanced Filter Section -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 bg-gradient-primary">
            <h6 class="m-0 font-weight-bold text-white">
                <i class="fas fa-filter"></i> Advanced Filters & Search
            </h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('reports.quotation') }}" id="filterForm">
                <div class="row">
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="status" class="form-label font-weight-bold">Status</label>
                            <select name="status" id="status" class="form-control form-control-sm">
                                <option value="all" {{ ($status ?? 'all') == 'all' ? 'selected' : '' }}>All Status</option>
                                <option value="pending" {{ ($status ?? '') == 'pending' ? 'selected' : '' }}>Pending</option>
                                <option value="approved" {{ ($status ?? '') == 'approved' ? 'selected' : '' }}>Approved</option>
                                <option value="rejected" {{ ($status ?? '') == 'rejected' ? 'selected' : '' }}>Rejected</option>
                                <option value="draft" {{ ($status ?? '') == 'draft' ? 'selected' : '' }}>Draft</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="customer_id" class="form-label font-weight-bold">Customer</label>
                            <select name="customer_id" id="customer_id" class="form-control form-control-sm">
                                <option value="">All Customers</option>
                                @foreach($customers ?? [] as $customer)
                                    <option value="{{ $customer->id }}" {{ ($customer_id ?? '') == $customer->id ? 'selected' : '' }}>
                                        {{ $customer->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="date_from" class="form-label font-weight-bold">Date From</label>
                            <input type="date" name="date_from" id="date_from" class="form-control form-control-sm" value="{{ $date_from ?? '' }}">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="date_to" class="form-label font-weight-bold">Date To</label>
                            <input type="date" name="date_to" id="date_to" class="form-control form-control-sm" value="{{ $date_to ?? '' }}">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="amount_min" class="form-label font-weight-bold">Min Amount</label>
                            <input type="number" name="amount_min" id="amount_min" class="form-control form-control-sm" placeholder="₹0" value="{{ $amount_min ?? '' }}">
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group">
                            <label class="form-label font-weight-bold">&nbsp;</label>
                            <div class="d-flex flex-column gap-1">
                                <button type="submit" class="btn btn-primary btn-sm">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <a href="{{ route('reports.quotation') }}" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Filter Buttons -->
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="d-flex flex-wrap gap-2">
                            <span class="text-muted font-weight-bold">Quick Filters:</span>
                            <button type="button" class="btn btn-outline-warning btn-sm" onclick="setQuickFilter('pending')">
                                <i class="fas fa-clock"></i> Pending Only
                            </button>
                            <button type="button" class="btn btn-outline-success btn-sm" onclick="setQuickFilter('approved')">
                                <i class="fas fa-check"></i> Approved Only
                            </button>
                            <button type="button" class="btn btn-outline-info btn-sm" onclick="setQuickFilter('today')">
                                <i class="fas fa-calendar-day"></i> Today
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="setQuickFilter('this_month')">
                                <i class="fas fa-calendar-alt"></i> This Month
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Enhanced Summary Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Quotations
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($summary['total_quotations'] ?? 0) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-file-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Value
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ₹{{ number_format($summary['total_value'] ?? 0, 2) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-rupee-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Average Value
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ₹{{ number_format($summary['avg_value'] ?? 0, 2) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Pending Quotations
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($summary['pending_count'] ?? 0) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Analytics Row -->
    <div class="row mb-4">
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Quotation Trends</h6>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="quotationChart" width="100%" height="40"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Status Distribution</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie pt-4 pb-2">
                        <canvas id="statusChart"></canvas>
                    </div>
                    <div class="mt-4 text-center small">
                        <span class="mr-2">
                            <i class="fas fa-circle text-success"></i> Approved
                        </span>
                        <span class="mr-2">
                            <i class="fas fa-circle text-warning"></i> Pending
                        </span>
                        <span class="mr-2">
                            <i class="fas fa-circle text-danger"></i> Rejected
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Quotations Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-list"></i> Quotations List
                @if(isset($quotations) && $quotations->total() > 0)
                    <span class="badge badge-success text-dark ml-2">{{ $quotations->total() }} Total</span>
                @endif
            </h6>
            <div class="dropdown no-arrow">
                <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                </a>
                <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
                    <div class="dropdown-header">Export Options:</div>
                    <a class="dropdown-item" href="#" onclick="exportToExcel()">
                        <i class="fas fa-file-excel fa-sm fa-fw mr-2 text-success"></i> Export to Excel
                    </a>
                    <a class="dropdown-item" href="#" onclick="exportToPdf()">
                        <i class="fas fa-file-pdf fa-sm fa-fw mr-2 text-danger"></i> Export to PDF
                    </a>
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item" href="#" onclick="printReport()">
                        <i class="fas fa-print fa-sm fa-fw mr-2 text-gray-400"></i> Print Report
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body">
            @if(isset($quotations) && $quotations->count() > 0)
                <div class="table-responsive">
                    <table class="table table-bordered" id="quotationsTable" width="100%" cellspacing="0">
                        <thead class="thead-light">
                            <tr>
                                <th class="text-center">#</th>
                                <th>Quotation Number</th>
                                <th>Date</th>
                                <th>Customer</th>
                                <th>Status</th>
                                <th>Items</th>
                                <th class="text-right">Total Amount</th>
                                <th>Created By</th>
                                <th class="text-center">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($quotations as $index => $quotation)
                                <tr>
                                    <td class="text-center">{{ $quotations->firstItem() + $index }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="mr-3">
                                                <div class="icon-circle bg-primary">
                                                    <i class="fas fa-file-alt text-white"></i>
                                                </div>
                                            </div>
                                            <div>
                                                <div class="font-weight-bold">{{ $quotation->quotation_number }}</div>
                                                <div class="small text-muted">ID: {{ $quotation->id }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            {{ \Carbon\Carbon::parse($quotation->quotation_date)->format('d M Y') }}
                                            <div class="small text-muted">
                                                {{ \Carbon\Carbon::parse($quotation->quotation_date)->diffForHumans() }}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <div class="font-weight-bold">{{ $quotation->customer->name ?? 'N/A' }}</div>
                                            @if($quotation->customer && $quotation->customer->company)
                                                <div class="small text-muted">{{ $quotation->customer->company->email }}</div>
                                            @endif
                                            @if($quotation->customer && $quotation->customer->phone)
                                                <div class="small text-muted">
                                                    <i class="fas fa-phone fa-sm"></i> {{ $quotation->customer->phone }}
                                                </div>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        @if($quotation->status == 'pending')
                                            <span class="badge badge-warning badge-pill">
                                                <i class="fas fa-clock"></i> Pending
                                            </span>
                                        @elseif($quotation->status == 'approved')
                                            <span class="badge badge-success badge-pill">
                                                <i class="fas fa-check"></i> Approved
                                            </span>
                                        @elseif($quotation->status == 'rejected')
                                            <span class="badge badge-danger badge-pill">
                                                <i class="fas fa-times"></i> Rejected
                                            </span>
                                        @else
                                            <span class="badge badge-secondary badge-pill">
                                                {{ ucfirst($quotation->status) }}
                                            </span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge badge-info badge-pill">
                                            {{ $quotation->items->count() }} items
                                        </span>
                                        @if($quotation->items->count() > 0)
                                            <div class="small text-muted mt-1">
                                                Qty: {{ $quotation->items->sum('quantity') }}
                                            </div>
                                        @endif
                                    </td>
                                    <td class="text-right">
                                        <div class="font-weight-bold text-success">
                                            ₹{{ number_format($quotation->total_amount ?? 0, 2) }}
                                        </div>
                                        @if(($quotation->total_amount ?? 0) > 0)
                                            <div class="small text-muted">
                                                Avg: ₹{{ number_format(($quotation->total_amount ?? 0) / max($quotation->items->count(), 1), 2) }}
                                            </div>
                                        @endif
                                    </td>
                                    <td>
                                        <div>
                                            {{ $quotation->user->name ?? 'N/A' }}
                                            <div class="small text-muted">
                                                {{ \Carbon\Carbon::parse($quotation->created_at)->format('d M Y') }}
                                            </div>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('entries.sales.show', $quotation->id) }}"
                                               class="btn btn-sm btn-outline-info" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('entries.sales.edit', $quotation->id) }}"
                                               class="btn btn-sm btn-outline-warning" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ route('entries.sales.print', $quotation->id) }}"
                                               class="btn btn-sm btn-outline-primary" title="Print" target="_blank">
                                                <i class="fas fa-print"></i>
                                            </a>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <i class="fas fa-ellipsis-h"></i>
                                                </button>
                                                <div class="dropdown-menu">
                                                    <a class="dropdown-item" href="{{ route('entries.sales.print', $quotation->id) }}" target="_blank">
                                                        <i class="fas fa-download"></i> Download PDF
                                                    </a>
                                                    <a class="dropdown-item" href="#" onclick="duplicateQuotation({{ $quotation->id }})">
                                                        <i class="fas fa-copy"></i> Duplicate
                                                    </a>
                                                    <div class="dropdown-divider"></div>
                                                    <a class="dropdown-item text-danger" href="#" onclick="deleteQuotation({{ $quotation->id }})">
                                                        <i class="fas fa-trash"></i> Delete
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-file-alt fa-4x text-gray-300"></i>
                    </div>
                    <h5 class="text-gray-600">No quotations found</h5>
                    <p class="text-muted mb-4">Try adjusting your filters or create a new quotation to get started.</p>
                    <a href="{{ route('entries.sales.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create New Quotation
                    </a>
                </div>
            @endif
        </div>
        @if(isset($quotations) && $quotations->hasPages())
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="text-muted">
                        Showing {{ $quotations->firstItem() }} to {{ $quotations->lastItem() }} of {{ $quotations->total() }} results
                    </div>
                    <div>
                        {{ $quotations->appends(request()->query())->links() }}
                    </div>
                </div>
            </div>
        @endif
    </div>
</div>

<!-- Chart.js CDN -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Enhanced JavaScript functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize charts
    initializeCharts();

    // Auto-submit form when filters change
    const statusSelect = document.getElementById('status');
    const customerSelect = document.getElementById('customer_id');

    if (statusSelect) {
        statusSelect.addEventListener('change', function() {
            this.form.submit();
        });
    }

    if (customerSelect) {
        customerSelect.addEventListener('change', function() {
            this.form.submit();
        });
    }

    // Initialize DataTable if available
    if (typeof $.fn.DataTable !== 'undefined' && document.getElementById('quotationsTable')) {
        $('#quotationsTable').DataTable({
            "pageLength": 25,
            "order": [[ 2, "desc" ]],
            "columnDefs": [
                { "orderable": false, "targets": [8] }
            ]
        });
    }
});

// Chart initialization
function initializeCharts() {
    // Quotation Trends Chart
    const ctx1 = document.getElementById('quotationChart');
    if (ctx1) {
        new Chart(ctx1, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                datasets: [{
                    label: 'Quotations',
                    data: [12, 19, 3, 5, 2, 3],
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    // Status Distribution Chart
    const ctx2 = document.getElementById('statusChart');
    if (ctx2) {
        new Chart(ctx2, {
            type: 'doughnut',
            data: {
                labels: ['Approved', 'Pending', 'Rejected'],
                datasets: [{
                    data: [{{ $summary['approved_count'] ?? 0 }}, {{ $summary['pending_count'] ?? 0 }}, {{ $summary['rejected_count'] ?? 0 }}],
                    backgroundColor: ['#1cc88a', '#f6c23e', '#e74a3b'],
                    hoverBackgroundColor: ['#17a673', '#f4b619', '#e02d1b'],
                    hoverBorderColor: "rgba(234, 236, 244, 1)",
                }],
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }
}

// Quick filter functions
function setQuickFilter(type) {
    const form = document.getElementById('filterForm');
    const statusSelect = document.getElementById('status');
    const dateFromInput = document.getElementById('date_from');
    const dateToInput = document.getElementById('date_to');

    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    switch(type) {
        case 'pending':
            statusSelect.value = 'pending';
            break;
        case 'approved':
            statusSelect.value = 'approved';
            break;
        case 'today':
            dateFromInput.value = today.toISOString().split('T')[0];
            dateToInput.value = today.toISOString().split('T')[0];
            break;
        case 'this_month':
            dateFromInput.value = firstDayOfMonth.toISOString().split('T')[0];
            dateToInput.value = today.toISOString().split('T')[0];
            break;
    }

    form.submit();
}

// Export functions
function exportToExcel() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.location.href = '{{ route("reports.quotation") }}?' + params.toString();
}

function exportToPdf() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'pdf');
    window.open('{{ route("reports.quotation") }}?' + params.toString(), '_blank');
}

function printReport() {
    window.print();
}

// Quotation actions
function duplicateQuotation(id) {
    if (confirm('Are you sure you want to duplicate this quotation?')) {
        fetch(`/entries/sales/${id}/duplicate`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Quotation duplicated successfully!');
                window.location.href = `/entries/sales/${data.new_id}/edit`;
            } else {
                alert('Error duplicating quotation: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error: ' + error.message);
        });
    }
}

function deleteQuotation(id) {
    if (confirm('Are you sure you want to delete this quotation? This action cannot be undone.')) {
        fetch(`/entries/sales/${id}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Quotation deleted successfully!');
                location.reload();
            } else {
                alert('Error deleting quotation: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error: ' + error.message);
        });
    }
}

// Print styles for better report printing
const printStyles = `
    @media print {
        .btn, .dropdown, .card-tools, .pagination { display: none !important; }
        .card { border: none !important; box-shadow: none !important; }
        .table { font-size: 12px; }
        .badge { color: #000 !important; background: #fff !important; border: 1px solid #000 !important; }
    }
`;

// Add print styles to head
const styleSheet = document.createElement("style");
styleSheet.type = "text/css";
styleSheet.innerText = printStyles;
document.head.appendChild(styleSheet);
</script>

<style>
/* Enhanced styling for better visual appeal */
.icon-circle {
    height: 2rem;
    width: 2rem;
    border-radius: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.chart-area {
    position: relative;
    height: 10rem;
    width: 100%;
}

.chart-pie {
    position: relative;
    height: 15rem;
    width: 100%;
}

.table th {
    border-top: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
}

.badge-pill {
    padding-right: 0.6em;
    padding-left: 0.6em;
    border-radius: 10rem;
}

.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

.dropdown-menu {
    font-size: 0.85rem;
}

.small {
    font-size: 0.75rem;
}

.text-gray-300 {
    color: #dddfeb !important;
}

.text-gray-400 {
    color: #d1d3e2 !important;
}

.text-gray-600 {
    color: #858796 !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.bg-gradient-primary {
    background: linear-gradient(180deg, #4e73df 10%, #224abe 100%);
}
</style>
@endsection
