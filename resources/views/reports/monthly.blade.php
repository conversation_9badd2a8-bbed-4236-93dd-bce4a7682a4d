@extends('layouts.app')

@section('title', 'Monthly Report - JMD Traders')
@section('page-title', 'Monthly Report')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Monthly Business Report</h6>
                <div class="d-flex gap-2">
                    <input type="month" class="form-control" id="reportMonth" value="{{ $selected_month->format('Y-m') }}" onchange="loadReport()">
                    <button class="btn btn-success d-none" onclick="exportReport()">
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- Month Navigation -->
                <div class="row mb-4">
                    <div class="col-12 text-center">
                        <div class="btn-group" role="group">
                            <button class="btn btn-outline-primary" onclick="changeMonth(-1)">
                                <i class="fas fa-chevron-left"></i> Previous Month
                            </button>
                            <button class="btn btn-outline-primary" onclick="setCurrentMonth()">
                                Current Month
                            </button>
                            <button class="btn btn-outline-primary" onclick="changeMonth(1)">
                                Next Month <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Summary Cards -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            Total Quotations
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $monthly_stats['total_quotations'] }}</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-file-invoice fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            Approved Quotations
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $monthly_stats['approved_quotations'] }}</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            Total Revenue
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">₹{{ number_format($monthly_stats['total_sales_value'], 2) }}</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-rupee-sign fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            Avg. Quotation Value
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">₹{{ number_format($monthly_stats['avg_quotation_value'], 2) }}</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-calculator fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Row -->
                <div class="row mb-4">
                    <div class="col-lg-8">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">Daily Quotations Trend</h6>
                            </div>
                            <div class="card-body">
                                <canvas id="dailyTrendChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">Status Distribution</h6>
                            </div>
                            <div class="card-body">
                                <canvas id="statusChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Top Customers -->
                <div class="row mb-4">
                    <div class="col-lg-6">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">Top Customers by Value</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Customer</th>
                                                <th>Quotations</th>
                                                <th>Total Value</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse($top_customers as $customer)
                                            <tr>
                                                <td>{{ $customer->customer_name }}</td>
                                                <td>{{ $customer->quotation_count }}</td>
                                                <td>₹{{ number_format($customer->total_value, 2) }}</td>
                                            </tr>
                                            @empty
                                            <tr>
                                                <td colspan="3" class="text-center">No data available</td>
                                            </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">Top Products by Quantity</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Product</th>
                                                <th>Quantity Sold</th>
                                                <th>Revenue</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse($topProducts as $product)
                                            <tr>
                                                <td>{{ $product->product_name }}</td>
                                                <td>{{ $product->total_quantity }}</td>
                                                <td>₹{{ number_format($product->total_revenue, 2) }}</td>
                                            </tr>
                                            @empty
                                            <tr>
                                                <td colspan="3" class="text-center">No data available</td>
                                            </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Performance Metrics -->
                <div class="row">
                    <div class="col-12">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">Performance Metrics</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3 text-center">
                                        <div class="h4 text-success">{{ number_format($performanceMetrics['approval_rate'], 1) }}%</div>
                                        <div class="text-muted">Approval Rate</div>
                                    </div>
                                    <div class="col-md-3 text-center">
                                        <div class="h4 text-info">{{ number_format($performanceMetrics['avg_processing_days'], 1) }}</div>
                                        <div class="text-muted">Avg. Processing Days</div>
                                    </div>
                                    <div class="col-md-3 text-center">
                                        <div class="h4 text-warning">{{ $performanceMetrics['active_customers'] }}</div>
                                        <div class="text-muted">Active Customers</div>
                                    </div>
                                    <div class="col-md-3 text-center">
                                        <div class="h4 text-primary">{{ number_format($performanceMetrics['delivery_rate'], 1) }}%</div>
                                        <div class="text-muted">On-Time Delivery</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Daily Trend Chart
    const dailyTrendCtx = document.getElementById('dailyTrendChart').getContext('2d');
    const dailyTrendChart = new Chart(dailyTrendCtx, {
        type: 'line',
        data: {
            labels: @json($dailyTrendData['labels']),
            datasets: [{
                label: 'Quotations',
                data: @json($dailyTrendData['data']),
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Status Distribution Chart
    const statusCtx = document.getElementById('statusChart').getContext('2d');
    const statusChart = new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: @json($statusDistribution['labels']),
            datasets: [{
                data: @json($statusDistribution['data']),
                backgroundColor: [
                    '#36b9cc',
                    '#f6c23e',
                    '#1cc88a',
                    '#e74a3b'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });

    function loadReport() {
        const month = document.getElementById('reportMonth').value;
        window.location.href = `{{ route('reports.monthly') }}?month=${month}`;
    }

    function changeMonth(months) {
        const currentMonth = new Date(document.getElementById('reportMonth').value + '-01');
        currentMonth.setMonth(currentMonth.getMonth() + months);
        const newMonth = currentMonth.toISOString().slice(0, 7);
        document.getElementById('reportMonth').value = newMonth;
        loadReport();
    }

    function setCurrentMonth() {
        const currentMonth = new Date().toISOString().slice(0, 7);
        document.getElementById('reportMonth').value = currentMonth;
        loadReport();
    }

    function exportReport() {
        const month = document.getElementById('reportMonth').value;
        window.open(`{{ route('reports.monthly') }}/export?month=${month}`, '_blank');
    }
</script>
@endpush
