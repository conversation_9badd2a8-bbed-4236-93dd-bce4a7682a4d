<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transport {{ $transportEntry->transport_number }} - {{ $company->name ?? 'Transport Service' }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: {{ $pdfSettings['font_family'] }}, sans-serif;
            font-size: {{ $pdfSettings['font_size'] }};
            line-height: 1.6;
            color: #333;
            background: #fff;
        }
        
        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 20px;
        }
        
        /* Header Styles */
        .header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 30px;
            margin-bottom: 30px;
            border-radius: 10px;
            position: relative;
            overflow: hidden;
        }
        
        .header-content {
            position: relative;
            z-index: 2;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .company-info {
            flex: 1;
        }
        
        .company-name {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .company-details {
            font-size: 14px;
            opacity: 0.9;
            line-height: 1.4;
        }
        
        .logo-section {
            text-align: {{ $pdfSettings['logo_position'] }};
            @if($pdfSettings['logo_position'] == 'right')
            margin-left: 30px;
            @else
            margin-right: 30px;
            @endif
        }
        
        .company-logo {
            @if($pdfSettings['logo_size'] == 'small')
            width: 60px;
            height: 60px;
            @elseif($pdfSettings['logo_size'] == 'large')
            width: 120px;
            height: 120px;
            @else
            width: 80px;
            height: 80px;
            @endif
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            border: 3px solid rgba(255,255,255,0.3);
        }
        
        /* Document Title */
        .document-title {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            border-left: 5px solid #28a745;
        }
        
        .document-title h1 {
            color: #28a745;
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .document-subtitle {
            color: #666;
            font-size: 14px;
        }
        
        /* Details Section */
        .details-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            gap: 30px;
        }
        
        .customer-details, .transport-details {
            flex: 1;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-top: 4px solid #28a745;
        }
        
        .section-title {
            color: #28a745;
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 15px;
            padding-bottom: 5px;
            border-bottom: 2px solid #28a745;
        }
        
        .detail-row {
            margin-bottom: 8px;
            display: flex;
        }
        
        .detail-label {
            font-weight: bold;
            color: #555;
            width: 120px;
            flex-shrink: 0;
        }
        
        .detail-value {
            color: #333;
            flex: 1;
        }
        
        /* Route Section */
        .route-section {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #28a745;
        }
        
        .route-title {
            color: #28a745;
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .route-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .route-point {
            text-align: center;
            flex: 1;
        }
        
        .route-arrow {
            flex: 0 0 50px;
            text-align: center;
            color: #28a745;
            font-size: 24px;
        }
        
        .route-label {
            font-weight: bold;
            color: #28a745;
            margin-bottom: 5px;
        }
        
        .route-location {
            font-size: 16px;
            color: #333;
        }
        
        /* Goods Table */
        .goods-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .goods-table th {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 15px 10px;
            text-align: left;
            font-weight: bold;
            font-size: 14px;
        }
        
        .goods-table td {
            padding: 12px 10px;
            border-bottom: 1px solid #eee;
            vertical-align: top;
        }
        
        .goods-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .text-right {
            text-align: right;
        }
        
        .text-center {
            text-align: center;
        }
        
        /* Footer */
        .footer {
            margin-top: 50px;
            padding: 30px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            text-align: center;
        }
        
        .terms-conditions {
            margin-bottom: 20px;
            color: #555;
            font-style: italic;
        }
        
        .footer-text {
            color: #777;
            font-size: 11px;
        }
        

        
        /* Currency Symbol */
        .currency {
            font-weight: bold;
            color: #28a745;
        }
    </style>
</head>
<body>
    @if($company && $company->watermark && ($pdfSettings['show_watermark'] ?? false))
    <div class="watermark">
        <img src="{{ public_path('storage/' . $company->watermark) }}" alt="Watermark" style="opacity: 0.1; width: 300px; height: auto;">
    </div>
    @endif
    
    <div class="container">
        <!-- Header Section -->
        <div class="header">
            <div class="header-content">
                @if($pdfSettings['logo_position'] == 'left')
                <div class="logo-section">
                    @if($pdfSettings['show_logo'] && $company && $company->logo)
                    <img src="{{ public_path('storage/' . $company->logo) }}" alt="{{ $company->name }}" class="company-logo">
                    @else
                    <div class="company-logo">
                        {{ $company && $company->name ? strtoupper(substr($company->name, 0, 2)) : 'TS' }}
                    </div>
                    @endif
                </div>
                @endif
                
                <div class="company-info">
                    <div class="company-name">{{ $company->name ?? 'Transport Service' }}</div>
                    @if($pdfSettings['show_company_details'] && $company)
                    <div class="company-details">
                        @if($company->address)
                        <div>{{ $company->address }}</div>
                        @endif
                        @if($company->city || $company->state)
                        <div>
                            @if($company->city){{ $company->city->name }}@endif
                            @if($company->city && $company->state), @endif
                            @if($company->state){{ $company->state->name }}@endif
                            @if($company->pincode) - {{ $company->pincode }}@endif
                        </div>
                        @endif
                        @if($company->phone || $company->email)
                        <div>
                            @if($company->phone)Phone: {{ $company->phone }}@endif
                            @if($company->phone && $company->email) | @endif
                            @if($company->email)Email: {{ $company->email }}@endif
                        </div>
                        @endif
                        @if($pdfSettings['show_gst_details'] && ($company->gst_number || $company->pan_number))
                        <div>
                            @if($company->gst_number)GST: {{ $company->gst_number }}@endif
                            @if($company->gst_number && $company->pan_number) | @endif
                            @if($company->pan_number)PAN: {{ $company->pan_number }}@endif
                        </div>
                        @endif
                        @if($company->website)
                        <div>Website: {{ $company->website }}</div>
                        @endif
                    </div>
                    @endif
                </div>
                
                @if($pdfSettings['logo_position'] == 'right')
                <div class="logo-section">
                    @if($pdfSettings['show_logo'] && $company && $company->logo)
                    <img src="{{ public_path('storage/' . $company->logo) }}" alt="{{ $company->name }}" class="company-logo">
                    @else
                    <div class="company-logo">
                        {{ $company && $company->name ? strtoupper(substr($company->name, 0, 2)) : 'TS' }}
                    </div>
                    @endif
                </div>
                @endif
            </div>
        </div>

        <!-- Document Title -->
        <div class="document-title">
            <h1>Transport Document</h1>
            <div class="document-subtitle">{{ $transportEntry->transport_number }} | Date: {{ $transportEntry->transport_date->format($pdfSettings['date_format']) }}</div>
        </div>

        <!-- Customer and Transport Details -->
        <div class="details-section">
            <div class="customer-details">
                <div class="section-title">Customer Details</div>
                <div class="detail-row">
                    <span class="detail-label">Customer:</span>
                    <span class="detail-value">{{ $transportEntry->customer->name ?? 'Not specified' }}</span>
                </div>
                @if($transportEntry->customer && $transportEntry->customer->address)
                <div class="detail-row">
                    <span class="detail-label">Address:</span>
                    <span class="detail-value">{{ $transportEntry->customer->address }}</span>
                </div>
                @endif
                @if($transportEntry->customer && $transportEntry->customer->phone)
                <div class="detail-row">
                    <span class="detail-label">Phone:</span>
                    <span class="detail-value">{{ $transportEntry->customer->phone }}</span>
                </div>
                @endif
                @if($transportEntry->customer && $transportEntry->customer->email)
                <div class="detail-row">
                    <span class="detail-label">Email:</span>
                    <span class="detail-value">{{ $transportEntry->customer->email }}</span>
                </div>
                @endif
            </div>

            <div class="transport-details">
                <div class="section-title">Transport Details</div>
                <div class="detail-row">
                    <span class="detail-label">Transport No:</span>
                    <span class="detail-value">{{ $transportEntry->transport_number }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Date:</span>
                    <span class="detail-value">{{ $transportEntry->transport_date->format($pdfSettings['date_format']) }}</span>
                </div>
                @if($transportEntry->due_date)
                <div class="detail-row">
                    <span class="detail-label">Due Date:</span>
                    <span class="detail-value">{{ $transportEntry->due_date->format($pdfSettings['date_format']) }}</span>
                </div>
                @endif
                @if($transportEntry->vehicle_number)
                <div class="detail-row">
                    <span class="detail-label">Vehicle No:</span>
                    <span class="detail-value">{{ $transportEntry->vehicle_number }}</span>
                </div>
                @endif
                @if($transportEntry->driver_name)
                <div class="detail-row">
                    <span class="detail-label">Driver:</span>
                    <span class="detail-value">{{ $transportEntry->driver_name }}</span>
                </div>
                @endif
                @if($transportEntry->driver_phone)
                <div class="detail-row">
                    <span class="detail-label">Driver Phone:</span>
                    <span class="detail-value">{{ $transportEntry->driver_phone }}</span>
                </div>
                @endif
            </div>
        </div>

        <!-- Route Information -->
        @if($transportEntry->from_location || $transportEntry->to_location)
        <div class="route-section">
            <div class="route-title">Route Information</div>
            <div class="route-info">
                <div class="route-point">
                    <div class="route-label">FROM</div>
                    <div class="route-location">{{ $transportEntry->from_location ?? 'Not specified' }}</div>
                </div>
                <div class="route-arrow">→</div>
                <div class="route-point">
                    <div class="route-label">TO</div>
                    <div class="route-location">{{ $transportEntry->to_location ?? 'Not specified' }}</div>
                </div>
            </div>
        </div>
        @endif

        <!-- Goods Information -->
        @if($transportEntry->goods_description || $transportEntry->weight || $transportEntry->packages)
        <table class="goods-table">
            <thead>
                <tr>
                    <th style="width: 50%;">Goods Description</th>
                    <th style="width: 15%;">Weight</th>
                    <th style="width: 15%;">Packages</th>
                    <th style="width: 20%;">Amount</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>{{ $transportEntry->goods_description ?? 'General Goods' }}</td>
                    <td class="text-center">{{ $transportEntry->weight ? $transportEntry->weight . ' kg' : '-' }}</td>
                    <td class="text-center">{{ $transportEntry->packages ?? '-' }}</td>
                    <td class="text-right">
                        @if($transportEntry->total_amount)
                        <span class="currency">{{ $pdfSettings['currency_symbol'] }}</span>{{ number_format($transportEntry->total_amount, 2) }}
                        @else
                        -
                        @endif
                    </td>
                </tr>
            </tbody>
        </table>
        @endif

        <!-- Footer -->
        <div class="footer">
            <div class="terms-conditions">
                {{ $pdfSettings['terms_and_conditions'] }}
            </div>
            <div class="footer-text">
                {{ $pdfSettings['footer_text'] }} | Generated on {{ now()->format($pdfSettings['date_format']) }}
            </div>
        </div>
    </div>
</body>
</html>
