@extends('layouts.app')

@section('title', 'Transport Entries')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Transport Entries</h4>
                    <a href="{{ route('entries.transport.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Transport Entry
                    </a>
                </div>
                <div class="card-body">
                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-2">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="text-white-50">Total</h6>
                                            <h4>{{ $stats['total'] }}</h4>
                                        </div>
                                        <i class="fas fa-truck fa-2x text-white-50"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="text-white-50">Scheduled</h6>
                                            <h4>{{ $stats['scheduled'] }}</h4>
                                        </div>
                                        <i class="fas fa-clock fa-2x text-white-50"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="text-white-50">In Transit</h6>
                                            <h4>{{ $stats['in_transit'] }}</h4>
                                        </div>
                                        <i class="fas fa-shipping-fast fa-2x text-white-50"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="text-white-50">Delivered</h6>
                                            <h4>{{ $stats['delivered'] }}</h4>
                                        </div>
                                        <i class="fas fa-check-circle fa-2x text-white-50"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="text-white-50">Delayed</h6>
                                            <h4>{{ $stats['delayed'] }}</h4>
                                        </div>
                                        <i class="fas fa-exclamation-triangle fa-2x text-white-50"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-dark text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="text-white-50">Total Value</h6>
                                            <h4>₹{{ number_format($stats['total_value'], 0) }}</h4>
                                        </div>
                                        <i class="fas fa-rupee-sign fa-2x text-white-50"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <select class="form-select" id="statusFilter">
                                <option value="">All Status</option>
                                <option value="scheduled">Scheduled</option>
                                <option value="in_transit">In Transit</option>
                                <option value="delivered">Delivered</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control" id="dateFilter" placeholder="Filter by date">
                        </div>
                        <div class="col-md-3">
                            <input type="text" class="form-control" id="searchFilter" placeholder="Search entries...">
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-secondary" onclick="clearFilters()">
                                <i class="fas fa-times"></i> Clear Filters
                            </button>
                        </div>
                    </div>

                    <!-- Transport Entries Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="transportTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>Sales Entry</th>
                                    <th>Customer</th>
                                    <th>Transport Date</th>
                                    <th>Vehicle</th>
                                    <th>Driver</th>
                                    <th>Estimated Delivery</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($transportEntries as $entry)
                                <tr>
                                    <td>{{ $entry->salesEntry->quotation_number }}</td>
                                    <td>{{ $entry->salesEntry->customer->name }}</td>
                                    <td>{{ $entry->transport_date->format('d/m/Y') }}</td>
                                    <td>{{ $entry->vehicle_number }}</td>
                                    <td>
                                        {{ $entry->driver_name }}<br>
                                        <small class="text-muted">{{ $entry->driver_phone }}</small>
                                    </td>
                                    <td>
                                        {{ $entry->estimated_delivery->format('d/m/Y') }}
                                        @if($entry->is_delayed)
                                            <span class="badge bg-danger ms-1">Delayed</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ $entry->status_badge }}">
                                            {{ ucfirst(str_replace('_', ' ', $entry->status)) }}
                                        </span>
                                        <br>
                                        <small class="text-muted">{{ $entry->delivery_status }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('entries.transport.show', $entry) }}" class="btn btn-sm btn-outline-primary" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('entries.transport.edit', $entry) }}" class="btn btn-sm btn-outline-warning" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            @if($entry->status === 'scheduled')
                                            <button class="btn btn-sm btn-outline-success" onclick="updateStatus({{ $entry->id }}, 'in_transit')" title="Start Transit">
                                                <i class="fas fa-play"></i>
                                            </button>
                                            @elseif($entry->status === 'in_transit')
                                            <button class="btn btn-sm btn-outline-info" onclick="markDelivered({{ $entry->id }})" title="Mark Delivered">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            @endif
                                            <a href="{{ route('entries.transport.print', $entry) }}" class="btn btn-sm btn-outline-secondary" title="Print" target="_blank">
                                                <i class="fas fa-print"></i>
                                            </a>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteEntry({{ $entry->id }})" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                                        <br>
                                        <strong>No transport entries found</strong>
                                        <br>
                                        <a href="{{ route('entries.transport.create') }}" class="btn btn-primary mt-2">
                                            <i class="fas fa-plus"></i> Create First Transport Entry
                                        </a>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center">
                        {{ $transportEntries->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Mark Delivered Modal -->
<div class="modal fade" id="deliveredModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Mark as Delivered</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="deliveredForm">
                    <div class="mb-3">
                        <label for="actual_delivery" class="form-label">Actual Delivery Date</label>
                        <input type="date" class="form-control" id="actual_delivery" required>
                    </div>
                    <div class="mb-3">
                        <label for="delivery_notes" class="form-label">Delivery Notes</label>
                        <textarea class="form-control" id="delivery_notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" onclick="confirmDelivery()">Mark Delivered</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    let currentEntryId = null;

    function clearFilters() {
        document.getElementById('statusFilter').value = '';
        document.getElementById('dateFilter').value = '';
        document.getElementById('vehicleFilter').value = '';
        document.getElementById('searchFilter').value = '';
        
        const rows = document.querySelectorAll('#transportTable tbody tr');
        rows.forEach(row => row.style.display = '');
    }

    function updateStatus(entryId, status) {
        if (confirm('Are you sure you want to update the status?')) {
            fetch(`/entries/transport/${entryId}/status`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ status: status })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Error updating status');
                }
            });
        }
    }

    function markDelivered(entryId) {
        currentEntryId = entryId;
        document.getElementById('actual_delivery').value = new Date().toISOString().split('T')[0];
        new bootstrap.Modal(document.getElementById('deliveredModal')).show();
    }

    function confirmDelivery() {
        const actualDelivery = document.getElementById('actual_delivery').value;
        const deliveryNotes = document.getElementById('delivery_notes').value;

        fetch(`/entries/transport/${currentEntryId}/deliver`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                actual_delivery: actualDelivery,
                delivery_notes: deliveryNotes
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                bootstrap.Modal.getInstance(document.getElementById('deliveredModal')).hide();
                location.reload();
            } else {
                alert('Error marking as delivered');
            }
        });
    }

    function deleteEntry(entryId) {
        if (confirm('Are you sure you want to delete this transport entry?')) {
            fetch(`/entries/transport/${entryId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Error deleting entry');
                }
            });
        }
    }

    // Filter functionality
    document.addEventListener('DOMContentLoaded', function() {
        const statusFilter = document.getElementById('statusFilter');
        const dateFilter = document.getElementById('dateFilter');
        const searchFilter = document.getElementById('searchFilter');
        const table = document.getElementById('transportTable');
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));

        function filterTable() {
            const statusValue = statusFilter.value.toLowerCase();
            const dateValue = dateFilter.value;
            const searchValue = searchFilter.value.toLowerCase();

            rows.forEach(row => {
                if (row.cells.length < 2) return; // Skip empty state row

                const statusCell = row.cells[6].textContent.toLowerCase();
                const dateCell = row.cells[2].textContent;
                const searchText = row.textContent.toLowerCase();

                const statusMatch = !statusValue || statusCell.includes(statusValue);
                const dateMatch = !dateValue || dateCell.includes(dateValue);
                const searchMatch = !searchValue || searchText.includes(searchValue);

                row.style.display = statusMatch && dateMatch && searchMatch ? '' : 'none';
            });
        }

        statusFilter.addEventListener('change', filterTable);
        dateFilter.addEventListener('change', filterTable);
        searchFilter.addEventListener('input', debounce(filterTable, 300));
    });

    // Debounce function for search input
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = function() {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
</script>
@endpush
