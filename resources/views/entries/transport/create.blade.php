@extends('layouts.app')

@section('title', 'Create Transport Entry - JMD Traders')

@section('content')
<style>
    .compact-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 15px;
    }

    .header-card {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .header-card h3 {
        margin: 0;
        font-size: 1.4rem;
        font-weight: 700;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .form-card {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .form-section {
        border-bottom: 1px solid #e3e6f0;
        padding: 15px 20px;
    }

    .form-section:last-child {
        border-bottom: none;
    }

    .section-title {
        color: #28a745;
        font-weight: 700;
        font-size: 0.9rem;
        margin-bottom: 12px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        display: flex;
        align-items: center;
        gap: 8px;
        padding-bottom: 8px;
        border-bottom: 2px solid #e3e6f0;
    }

    .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }

    .form-group {
        margin-bottom: 0;
    }

    .form-label {
        display: block;
        font-weight: 600;
        color: #5a5c69;
        font-size: 0.8rem;
        margin-bottom: 6px;
        text-transform: uppercase;
        letter-spacing: 0.3px;
    }

    .form-control, .form-select {
        width: 100%;
        padding: 10px 12px;
        border: 1px solid #d1d3e2;
        border-radius: 4px;
        font-size: 0.85rem;
        background: #fff;
        transition: all 0.2s ease;
        box-shadow: 0 1px 2px rgba(0,0,0,0.05);
    }

    .form-control:focus, .form-select:focus {
        border-color: #28a745;
        background: white;
        outline: none;
        box-shadow: 0 0 0 0.1rem rgba(40, 167, 69, 0.2);
        transform: translateY(-1px);
    }

    .form-control:hover, .form-select:hover {
        border-color: #b8b9ba;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        font-weight: 600;
        font-size: 0.8rem;
        cursor: pointer;
        transition: all 0.2s ease;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        text-decoration: none;
    }

    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .btn-primary {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        color: white;
        border: 1px solid #4e73df;
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #224abe 0%, #1a365c 100%);
        color: white;
    }

    .btn-success {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        color: white;
        border: 1px solid #28a745;
    }

    .btn-success:hover {
        background: linear-gradient(135deg, #1e7e34 0%, #155724 100%);
        color: white;
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
        border: 1px solid #6c757d;
    }

    .btn-secondary:hover {
        background: #5a6268;
        color: white;
    }

    .btn-light {
        background: rgba(255,255,255,0.9);
        color: #28a745;
        border: 1px solid rgba(255,255,255,0.5);
    }

    .btn-light:hover {
        background: white;
        color: #1e7e34;
    }

    .btn-outline-primary {
        background: transparent;
        color: #4e73df;
        border: 1px solid #4e73df;
    }

    .btn-outline-primary:hover {
        background: #4e73df;
        color: white;
    }

    .totals-section {
        background: linear-gradient(135deg, #f8f9fc 0%, #e3e6f0 100%);
        border: 1px solid #d1d3e2;
        border-radius: 8px;
        padding: 20px;
        margin-top: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }

    .totals-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
    }

    .total-item {
        text-align: center;
        padding: 15px 10px;
        background: white;
        border-radius: 6px;
        border: 1px solid #dee2e6;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        transition: all 0.2s ease;
    }

    .total-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    .total-label {
        font-size: 0.7rem;
        color: #6c757d;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        margin-bottom: 8px;
    }

    .total-amount {
        font-size: 1.1rem;
        font-weight: 700;
        color: #28a745;
    }

    .invalid-feedback {
        display: block;
        width: 100%;
        margin-top: 0.25rem;
        font-size: 0.75rem;
        color: #dc3545;
    }

    .is-invalid {
        border-color: #dc3545;
        padding-right: calc(1.5em + 0.75rem);
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 3.6.7.7 1.1-1.1'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right calc(0.375em + 0.1875rem) center;
        background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    }

    .d-flex {
        display: flex !important;
    }

    .justify-content-between {
        justify-content: space-between !important;
    }

    .align-items-center {
        align-items: center !important;
    }

    .gap-2 {
        gap: 0.5rem !important;
    }

    .w-100 {
        width: 100% !important;
    }

    .alert {
        position: relative;
        padding: 0.75rem 1.25rem;
        margin-bottom: 1rem;
        border: 1px solid transparent;
        border-radius: 0.375rem;
        font-size: 0.875rem;
    }

    .alert-dismissible {
        padding-right: 4rem;
    }

    .alert-danger {
        background-color: #f8d7da;
        border-color: #f5c6cb;
        color: #721c24;
    }

    .alert-success {
        background-color: #d4edda;
        border-color: #c3e6cb;
        color: #155724;
    }

    .alert ul {
        margin-bottom: 0;
    }

    .alert .btn-close {
        position: absolute;
        top: 0;
        right: 0;
        z-index: 2;
        padding: 0.75rem 1.25rem;
    }

    .form-text {
        font-size: 0.7rem;
        color: #6c757d;
        margin-top: 0.25rem;
        display: block;
    }

    .text-muted {
        color: #6c757d !important;
    }

    @media (max-width: 768px) {
        .compact-container {
            padding: 10px;
        }
        
        .form-grid {
            grid-template-columns: 1fr;
        }
        
        .totals-grid {
            grid-template-columns: repeat(2, 1fr);
        }

        .btn {
            padding: 8px 16px;
            font-size: 0.75rem;
        }

        .header-card h3 {
            font-size: 1.2rem;
        }
    }
</style>

<div class="compact-container">
    <!-- Header -->
    <div class="header-card">
        <div class="d-flex justify-content-between align-items-center">
            <h3>
                <i class="fas fa-truck"></i>
                Create Transport Entry
            </h3>
            <a href="{{ route('entries.transport') }}" class="btn btn-light">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <!-- Error Display -->
    @if ($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; border: none; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);">
            <div style="display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-exclamation-triangle" style="font-size: 1.2rem;"></i>
                <div>
                    <strong>Please fix the following errors:</strong>
                    <ul style="margin: 8px 0 0 0; padding-left: 20px;">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            </div>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="alert" aria-label="Close" style="background: none; border: none; color: white; font-size: 1.5rem; opacity: 0.8; cursor: pointer; margin-left: auto;">&times;</button>
        </div>
    @endif

    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert" style="background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%); color: white; border: none; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);">
            <div style="display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-check-circle" style="font-size: 1.2rem;"></i>
                <strong>{{ session('success') }}</strong>
            </div>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="alert" aria-label="Close" style="background: none; border: none; color: white; font-size: 1.5rem; opacity: 0.8; cursor: pointer; margin-left: auto;">&times;</button>
        </div>
    @endif

    <!-- Form Card -->
    <div class="form-card">
        <form action="{{ route('entries.transport.store') }}" method="POST" id="transportForm">
            @csrf
            
            <!-- Basic Information -->
            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-calendar-alt"></i>
                    Basic Information
                </div>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Transport Date <span style="color: red;">*</span></label>
                        <div class="date-input-wrapper">
                            <input type="date" class="form-control datepicker @error('transport_date') is-invalid @enderror" name="transport_date" id="transport_date" value="{{ old('transport_date', date('Y-m-d')) }}" required placeholder="Select transport date">
                            <i class="fas fa-calendar-alt calendar-icon"></i>
                        </div>
                        @error('transport_date')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group">
                        <label class="form-label">Due Days</label>
                        <input type="number" class="form-control @error('due_days') is-invalid @enderror" name="due_days" id="due_days" min="0" value="{{ old('due_days') }}">
                        @error('due_days')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group">
                        <label class="form-label">Due Date</label>
                        <div class="date-input-wrapper">
                            <input type="date" class="form-control datepicker @error('due_date') is-invalid @enderror" name="due_date" id="due_date" value="{{ old('due_date') }}" placeholder="Select due date">
                            <i class="fas fa-calendar-alt calendar-icon"></i>
                        </div>
                        @error('due_date')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Transport & Freight Information -->
            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-truck"></i>
                    Transport & Freight Information
                </div>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Freight Cond.</label>
                        <input type="text" class="form-control @error('freight_cond') is-invalid @enderror" name="freight_cond" id="freight_cond" value="{{ old('freight_cond') }}">
                        @error('freight_cond')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group">
                        <label class="form-label">L.R.No.</label>
                        <input type="text" class="form-control @error('lr_no') is-invalid @enderror" name="lr_no" id="lr_no" value="{{ old('lr_no') }}">
                        @error('lr_no')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group">
                        <label class="form-label">Doc.Through</label>
                        <input type="text" class="form-control @error('doc_through') is-invalid @enderror" name="doc_through" id="doc_through" value="{{ old('doc_through') }}">
                        @error('doc_through')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group">
                        <label class="form-label">Way Bill No.</label>
                        <input type="text" class="form-control @error('way_bill_no') is-invalid @enderror" name="way_bill_no" id="way_bill_no" value="{{ old('way_bill_no') }}">
                        @error('way_bill_no')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group">
                        <label class="form-label">Road Permit No.</label>
                        <input type="text" class="form-control @error('road_permit_no') is-invalid @enderror" name="road_permit_no" id="road_permit_no" value="{{ old('road_permit_no') }}">
                        @error('road_permit_no')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Driver Information -->
            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-id-card"></i>
                    Driver Information
                </div>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Driver Name</label>
                        <input type="text" class="form-control" name="driver_name" id="driver_name" value="{{ old('driver_name') }}">
                    </div>
                    <div class="form-group">
                        <label class="form-label">D.L.No.</label>
                        <input type="text" class="form-control" name="dl_no" id="dl_no" value="{{ old('dl_no') }}">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Driver Mob.No.</label>
                        <input type="tel" class="form-control" name="driver_mob_no" id="driver_mob_no" value="{{ old('driver_mob_no') }}">
                    </div>
                </div>
            </div>

            <!-- Location Information -->
            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-map-marker-alt"></i>
                    Location Information
                </div>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">From City</label>
                        <input type="text" class="form-control" name="from_city" id="from_city" value="{{ old('from_city') }}">
                    </div>
                    <div class="form-group">
                        <label class="form-label">To City</label>
                        <input type="text" class="form-control" name="to_city" id="to_city" value="{{ old('to_city') }}">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Destination</label>
                        <input type="text" class="form-control" name="destination" id="destination" value="{{ old('destination') }}">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Delivery 1</label>
                        <input type="text" class="form-control" name="delivery_1" id="delivery_1" value="{{ old('delivery_1') }}">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Delivery 2</label>
                        <input type="text" class="form-control" name="delivery_2" id="delivery_2" value="{{ old('delivery_2') }}">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Material Direct Delivered from</label>
                        <input type="text" class="form-control" name="material_direct_delivered_from" id="material_direct_delivered_from" value="{{ old('material_direct_delivered_from') }}">
                    </div>
                </div>
            </div>

            <!-- Insurance & Documentation -->
            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-shield-alt"></i>
                    Insurance & Documentation
                </div>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Goods Insured by</label>
                        <input type="text" class="form-control" name="goods_insured_by" id="goods_insured_by" value="{{ old('goods_insured_by') }}">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Policy No.</label>
                        <input type="text" class="form-control" name="policy_no" id="policy_no" value="{{ old('policy_no') }}">
                    </div>
                    <div class="form-group">
                        <label class="form-label">L.C.No.</label>
                        <input type="text" class="form-control" name="lc_no" id="lc_no" value="{{ old('lc_no') }}">
                    </div>
                    <div class="form-group">
                        <label class="form-label">A/c Of</label>
                        <input type="text" class="form-control" name="ac_of" id="ac_of" value="{{ old('ac_of') }}">
                    </div>
                    <div class="form-group">
                        <label class="form-label">E.Way Bill No.</label>
                        <input type="text" class="form-control" name="eway_bill_no" id="eway_bill_no" value="{{ old('eway_bill_no') }}">
                    </div>
                    <div class="form-group">
                        <label class="form-label">E.Way Bill Date</label>
                        <div class="date-input-wrapper">
                            <input type="date" class="form-control datepicker" name="eway_bill_date" id="eway_bill_date" value="{{ old('eway_bill_date') }}" placeholder="Select e-way bill date">
                            <i class="fas fa-calendar-alt calendar-icon"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Business Information -->
            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-briefcase"></i>
                    Business Information
                </div>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Our Offer No.</label>
                        <input type="text" class="form-control" name="our_offer_no" id="our_offer_no" value="{{ old('our_offer_no') }}">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Export Inv.No.</label>
                        <input type="text" class="form-control" name="export_inv_no" id="export_inv_no" value="{{ old('export_inv_no') }}">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Export Inv. Date</label>
                        <div class="date-input-wrapper">
                            <input type="date" class="form-control datepicker" name="export_inv_date" id="export_inv_date" value="{{ old('export_inv_date') }}" placeholder="Select export invoice date">
                            <i class="fas fa-calendar-alt calendar-icon"></i>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Prepared by</label>
                        <input type="text" class="form-control" name="prepared_by" id="prepared_by" value="{{ old('prepared_by') }}">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Cash Address</label>
                        <textarea class="form-control" name="cash_address" id="cash_address" rows="2">{{ old('cash_address') }}</textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Remarks</label>
                        <textarea class="form-control" name="remarks" id="remarks" rows="2">{{ old('remarks') }}</textarea>
                    </div>
                </div>
            </div>

            <!-- Weight Information -->
            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-weight"></i>
                    Weight Information
                </div>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Gross wt.</label>
                        <input type="number" class="form-control" name="gross_wt" id="gross_wt" step="0.01" value="{{ old('gross_wt', 0) }}" min="0">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Tare wt.</label>
                        <input type="number" class="form-control" name="tare_wt" id="tare_wt" step="0.01" value="{{ old('tare_wt', 0) }}" min="0">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Net Wt.</label>
                        <input type="number" class="form-control" name="net_wt" id="net_wt" step="0.01" value="{{ old('net_wt', 0) }}" min="0" readonly>
                    </div>
                </div>
            </div>

            <!-- Financial Information -->
            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-rupee-sign"></i>
                    Financial Information
                </div>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Total</label>
                        <input type="number" class="form-control total-field @error('total') is-invalid @enderror" name="total" id="total" step="0.01" value="{{ old('total', 0) }}" min="0">
                        @error('total')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group">
                        <label class="form-label">Ins.P/MT</label>
                        <input type="number" class="form-control total-field @error('ins_pmt') is-invalid @enderror" name="ins_pmt" id="ins_pmt" step="0.01" value="{{ old('ins_pmt', 0) }}" min="0">
                        @error('ins_pmt')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group">
                        <label class="form-label">Insurance</label>
                        <input type="number" class="form-control total-field @error('insurance') is-invalid @enderror" name="insurance" id="insurance" step="0.01" value="{{ old('insurance', 0) }}" min="0">
                        @error('insurance')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group">
                        <label class="form-label">Frt.Advance</label>
                        <input type="number" class="form-control total-field @error('frt_advance') is-invalid @enderror" name="frt_advance" id="frt_advance" step="0.01" value="{{ old('frt_advance', 0) }}" min="0">
                        @error('frt_advance')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group">
                        <label class="form-label">Grand Total</label>
                        <input type="number" class="form-control @error('grand_total') is-invalid @enderror" name="grand_total" id="grand_total" step="0.01" value="{{ old('grand_total', 0) }}" min="0" readonly>
                        @error('grand_total')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group">
                        <label class="form-label">TCS %</label>
                        <input type="number" class="form-control @error('tcs_percent') is-invalid @enderror" name="tcs_percent" id="tcs_percent" step="0.01" min="0" max="100" value="{{ old('tcs_percent', 0) }}">
                        @error('tcs_percent')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group">
                        <label class="form-label">TCS Amt.</label>
                        <input type="number" class="form-control @error('tcs_amount') is-invalid @enderror" name="tcs_amount" id="tcs_amount" step="0.01" value="{{ old('tcs_amount', 0) }}" min="0" readonly>
                        @error('tcs_amount')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group">
                        <label class="form-label">Net Amt.</label>
                        <input type="number" class="form-control @error('net_amount') is-invalid @enderror" name="net_amount" id="net_amount" step="0.01" value="{{ old('net_amount', 0) }}" min="0" readonly>
                        @error('net_amount')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Totals Summary -->
            <div class="totals-section">
                <div class="section-title">
                    <i class="fas fa-calculator"></i>
                    Transport Summary
                </div>
                <div class="totals-grid">
                    <div class="total-item">
                        <div class="total-label">Grand Total</div>
                        <div class="total-amount" id="display_grand_total">₹0.00</div>
                    </div>
                    <div class="total-item">
                        <div class="total-label">TCS Amount</div>
                        <div class="total-amount" id="display_tcs_amount">₹0.00</div>
                    </div>
                    <div class="total-item">
                        <div class="total-label">Net Amount</div>
                        <div class="total-amount" id="display_net_amount">₹0.00</div>
                    </div>
                    <div class="total-item">
                        <div class="total-label">Net Weight</div>
                        <div class="total-amount" id="display_net_weight">0 kg</div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="form-section">
                <div class="d-flex justify-content-between align-items-center">
                    <a href="{{ route('entries.transport') }}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-outline-primary" onclick="saveDraft()">
                            <i class="fas fa-save"></i> Save as Draft
                        </button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-check"></i> Create Transport Entry
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Calculate net weight
    function calculateNetWeight() {
        const grossWt = parseFloat(document.getElementById('gross_wt').value) || 0;
        const tareWt = parseFloat(document.getElementById('tare_wt').value) || 0;
        const netWt = Math.max(0, grossWt - tareWt); // Ensure net weight is never negative
        document.getElementById('net_wt').value = netWt.toFixed(2);
        
        // Update display
        const displayNetWeight = document.getElementById('display_net_weight');
        if (displayNetWeight) displayNetWeight.textContent = netWt.toFixed(2) + ' kg';
    }

    // Calculate totals
    function calculateTotals() {
        const total = parseFloat(document.getElementById('total').value) || 0;
        const insPmt = parseFloat(document.getElementById('ins_pmt').value) || 0;
        const insurance = parseFloat(document.getElementById('insurance').value) || 0;
        const frtAdvance = parseFloat(document.getElementById('frt_advance').value) || 0;
        const tcsPercent = parseFloat(document.getElementById('tcs_percent').value) || 0;

        // Calculate grand total
        const grandTotal = total + insPmt + insurance + frtAdvance;
        document.getElementById('grand_total').value = grandTotal.toFixed(2);

        // Calculate TCS amount
        const tcsAmount = (grandTotal * tcsPercent) / 100;
        document.getElementById('tcs_amount').value = tcsAmount.toFixed(2);

        // Calculate net amount
        const netAmount = grandTotal + tcsAmount;
        document.getElementById('net_amount').value = netAmount.toFixed(2);

        // Update display values
        const displayGrandTotal = document.getElementById('display_grand_total');
        const displayTcsAmount = document.getElementById('display_tcs_amount');
        const displayNetAmount = document.getElementById('display_net_amount');
        
        if (displayGrandTotal) displayGrandTotal.textContent = '₹' + grandTotal.toFixed(2);
        if (displayTcsAmount) displayTcsAmount.textContent = '₹' + tcsAmount.toFixed(2);
        if (displayNetAmount) displayNetAmount.textContent = '₹' + netAmount.toFixed(2);
    }

    // Event listeners for weight calculation
    const grossWtInput = document.getElementById('gross_wt');
    const tareWtInput = document.getElementById('tare_wt');
    if (grossWtInput) {
        grossWtInput.addEventListener('input', calculateNetWeight);
        grossWtInput.addEventListener('blur', function() {
            if (!this.value || this.value.trim() === '') this.value = '0';
            if (parseFloat(this.value) < 0) this.value = '0';
        });
    }
    if (tareWtInput) {
        tareWtInput.addEventListener('input', calculateNetWeight);
        tareWtInput.addEventListener('blur', function() {
            if (!this.value || this.value.trim() === '') this.value = '0';
            if (parseFloat(this.value) < 0) this.value = '0';
        });
    }

    // Event listeners for totals calculation
    document.querySelectorAll('.total-field, #tcs_percent').forEach(field => {
        field.addEventListener('input', calculateTotals);
        
        // Add real-time validation and normalization
        field.addEventListener('blur', function() {
            // Normalize empty values to 0
            if (!this.value || this.value.trim() === '') {
                this.value = '0';
            }
            
            const value = parseFloat(this.value);
            if (isNaN(value) || value < 0) {
                this.classList.add('is-invalid');
                let feedback = this.nextElementSibling;
                if (!feedback || !feedback.classList.contains('invalid-feedback')) {
                    feedback = document.createElement('div');
                    feedback.className = 'invalid-feedback';
                    this.parentNode.appendChild(feedback);
                }
                feedback.textContent = 'Please enter a valid positive number';
                // Set to 0 if invalid
                this.value = '0';
            } else {
                this.classList.remove('is-invalid');
                const feedback = this.nextElementSibling;
                if (feedback && feedback.classList.contains('invalid-feedback')) {
                    feedback.remove();
                }
            }
            
            // Recalculate after normalization
            calculateTotals();
        });
    });

    // Due date calculation based on due days
    const dueDaysInput = document.getElementById('due_days');
    const dueDateInput = document.getElementById('due_date');

    function calculateDueDate() {
        const dueDays = parseInt(dueDaysInput.value) || 0;
        
        if (dueDays > 0) {
            const today = new Date();
            today.setDate(today.getDate() + dueDays);
            dueDateInput.value = today.toISOString().split('T')[0];
        }
    }

    if (dueDaysInput) dueDaysInput.addEventListener('input', calculateDueDate);

    // Transport date validation
    const transportDateInput = document.getElementById('transport_date');
    if (transportDateInput) {
        transportDateInput.addEventListener('change', function() {
            const selectedDate = new Date(this.value);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            
            if (selectedDate < today) {
                this.classList.add('is-invalid');
                let feedback = this.nextElementSibling;
                if (!feedback || !feedback.classList.contains('invalid-feedback')) {
                    feedback = document.createElement('div');
                    feedback.className = 'invalid-feedback';
                    this.parentNode.appendChild(feedback);
                }
                feedback.textContent = 'Transport date cannot be in the past';
            } else {
                this.classList.remove('is-invalid');
                const feedback = this.nextElementSibling;
                if (feedback && feedback.classList.contains('invalid-feedback') && feedback.textContent.includes('past')) {
                    feedback.remove();
                }
            }
        });
    }

    // Form validation
    const form = document.getElementById('transportForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            // Clear previous validation states
            const allFields = form.querySelectorAll('.form-control');
            allFields.forEach(field => field.classList.remove('is-invalid'));

            // Ensure numeric fields have valid values before submission
            const numericFields = ['gross_wt', 'tare_wt', 'net_wt', 'total', 'ins_pmt', 'insurance', 'frt_advance', 'grand_total', 'tcs_percent', 'tcs_amount', 'net_amount'];
            numericFields.forEach(fieldName => {
                const field = document.getElementById(fieldName);
                if (field) {
                    // Set empty fields to 0
                    if (!field.value || field.value.trim() === '') {
                        field.value = '0';
                    }
                    // Ensure minimum value of 0
                    if (parseFloat(field.value) < 0) {
                        field.value = '0';
                    }
                }
            });

            let isValid = true;
            let errorMessages = [];

            // Check transport_date (required field)
            const transportDate = document.getElementById('transport_date');
            if (!transportDate.value || !transportDate.value.trim()) {
                isValid = false;
                transportDate.classList.add('is-invalid');
                errorMessages.push('Transport Date is required');
            }

            // Validate numeric fields are valid numbers
            numericFields.forEach(fieldName => {
                const field = document.getElementById(fieldName);
                if (field && field.value && isNaN(parseFloat(field.value))) {
                    isValid = false;
                    field.classList.add('is-invalid');
                    errorMessages.push(`${field.previousElementSibling.textContent.replace('*', '').trim()} must be a valid number`);
                }
            });

            // Validate TCS percentage
            const tcsPercent = document.getElementById('tcs_percent');
            if (tcsPercent && tcsPercent.value && (parseFloat(tcsPercent.value) < 0 || parseFloat(tcsPercent.value) > 100)) {
                isValid = false;
                tcsPercent.classList.add('is-invalid');
                errorMessages.push('TCS percentage must be between 0 and 100');
            }

            // Validate mobile number format (only if provided)
            const driverMob = document.getElementById('driver_mob_no');
            if (driverMob && driverMob.value && driverMob.value.trim() && !/^\d{10}$/.test(driverMob.value.replace(/[^\d]/g, ''))) {
                isValid = false;
                driverMob.classList.add('is-invalid');
                errorMessages.push('Driver mobile number should be 10 digits');
            }

            if (!isValid) {
                e.preventDefault();
                
                // Show error summary
                let errorHtml = '<strong>Please fix the following errors:</strong><ul>';
                errorMessages.forEach(msg => {
                    errorHtml += `<li>${msg}</li>`;
                });
                errorHtml += '</ul>';
                
                // Create or update error alert
                let errorAlert = document.querySelector('.alert-danger');
                if (!errorAlert) {
                    errorAlert = document.createElement('div');
                    errorAlert.className = 'alert alert-danger alert-dismissible fade show';
                    errorAlert.innerHTML = `
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <i class="fas fa-exclamation-triangle" style="font-size: 1.2rem;"></i>
                            <div>${errorHtml}</div>
                        </div>
                        <button type="button" class="btn-close" onclick="this.parentElement.remove()">&times;</button>
                    `;
                    form.parentElement.insertBefore(errorAlert, form);
                } else {
                    errorAlert.querySelector('div:last-of-type').innerHTML = errorHtml;
                }
                
                // Scroll to top to show error
                errorAlert.scrollIntoView({ behavior: 'smooth', block: 'center' });
                
                return false;
            }
        });
    }

    // Initialize calculations
    calculateNetWeight();
    calculateTotals();

    // Auto-dismiss success alerts after 5 seconds
    setTimeout(() => {
        const successAlert = document.querySelector('.alert-success');
        if (successAlert) {
            successAlert.style.transition = 'opacity 0.5s ease';
            successAlert.style.opacity = '0';
            setTimeout(() => successAlert.remove(), 500);
        }
    }, 5000);

    // Add click handlers for alert close buttons
    document.querySelectorAll('.btn-close').forEach(button => {
        button.addEventListener('click', function() {
            this.parentElement.remove();
        });
    });
});

function saveDraft() {
    const form = document.getElementById('transportForm');
    
    if (form) {
        // Add a hidden input to indicate this is a draft
        const draftInput = document.createElement('input');
        draftInput.type = 'hidden';
        draftInput.name = 'is_draft';
        draftInput.value = '1';
        form.appendChild(draftInput);
        
        // Submit form
        form.submit();
    }
}

function printInvoice() {
    alert('Print functionality will be available after saving the transport entry.');
}

function exit() {
    if (confirm('Are you sure you want to exit? Any unsaved changes will be lost.')) {
        window.location.href = "{{ route('entries.transport') }}";
    }
}
</script>
@endpush
