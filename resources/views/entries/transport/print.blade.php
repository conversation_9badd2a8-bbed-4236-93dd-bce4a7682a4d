<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Transport Entry - {{ $transportEntry->id }} | {{ $transportEntry->company->name ?? 'Company' }}</title>
    <style>
        body {
            font-family: <PERSON>ja<PERSON><PERSON>, sans-serif;
            font-size: 12px;
            /* color: #fffefeff; */
            margin: 0;
            padding: 0;
        }
        .container {
            width: 100%;
            
        }
        .border-box {
            background: #1cc88a;
            /* border: 1px solid #000; */
            padding: 8px;
            margin-bottom: 12px;
        }
        .title {
            color:#fffefeff;
            font-size: 14px;
            font-weight: bold;
            background: #1cc88a;
            padding: 5px;
            /* border-bottom: 1px solid #000; */
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 8px;
        }
        table td, table th {
            border: 1px solid #ccc;
            padding: 5px;
            font-size: 11px;
            vertical-align: top;
        }
        .no-border td {
            border: none;
        }
        .section {
           
            margin-top: 12px;
        }
        .footer {
            font-size: 10px;
            margin-top: 15px;
            border-top: 1px solid #000;
            padding-top: 10px;
        }
        .signature {
            text-align: right;
            margin-top: 50px;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="container">

        <!-- Company Info -->
        <div class="border-box">
            <table class="no-border" width="100%" cellspacing="0" cellpadding="8">
                <tr>
                    <td width="20%" style="text-align:center;vertical-align:middle;">
                        @if($company->logo)
                            <img src="{{ public_path('storage/' . $company->logo) }}"  style="width:80px; height:80px; border-radius:10%">
                        @endif
                    </td>
                    <td width="80%" style="text-align:center; color:#fffefeff; ">
                        <strong style="font-size:25px;">{{ $transportEntry->company->name ?? 'Company Name' }}</strong><br>
                <b><div> {{ $company->email ?? '' }} |  {{ $company->phone ?? '' }} | {{ $company->mobile ?? '' }}</div>

                       <div>
                    {{ $company->address ?? '' }}<br>
                    {{ $company->city ?? '' }} {{ $company->state ?? '' }} - {{ $company->pincode ?? '' }}
                </div>
                        <div>Website: {{ $company->website ?? '' }}</div></b>
                    </td>
                   
                </tr>
            </table>
        </div>

        <!-- Driver & Transport Info -->
        <div class="section">
            <div class="title">Transport & Driver Information</div>
            <table>
                <tr>
                    <td><b>Driver Name</b></td>
                    <td>{{ $transportEntry->driver_name ?? '-' }}</td>
                    <td><b>Driver Mobile</b></td>
                    <td>{{ $transportEntry->driver_mob_no ?? '-' }}</td>
                </tr>
                <tr>
                    <td><b>D.L Number</b></td>
                    <td>{{ $transportEntry->dl_no ?? '-' }}</td>
                    <td><b>Vehicle Number</b></td>
                    <td>{{ $transportEntry->vehicle_number ?? '-' }}</td>
                </tr>
                <tr>
                    <td><b>L.R. No</b></td>
                    <td>{{ $transportEntry->lr_no ?? '-' }}</td>
                    <td><b>Way Bill No</b></td>
                    <td>{{ $transportEntry->way_bill_no ?? '-' }}</td>
                </tr>
            </table>
        </div>

        <!-- Location Info -->
        <div class="section">
            <div class="title">Location & Route</div>
            <table>
                <tr>
                    <td><b>From City</b></td>
                    <td>{{ $transportEntry->from_city ?? '-' }}</td>
                    <td><b>To City</b></td>
                    <td>{{ $transportEntry->to_city ?? '-' }}</td>
                </tr>
                <tr>
                    <td><b>Destination</b></td>
                    <td>{{ $transportEntry->destination ?? '-' }}</td>
                    <td><b>Road Permit</b></td>
                    <td>{{ $transportEntry->road_permit_no ?? '-' }}</td>
                </tr>
            </table>
        </div>

        <!-- Weight -->
        <div class="section">
            <div class="title">Weight Information</div>
            <table>
                <tr>
                    <td><b>Gross Weight</b></td>
                    <td>{{ number_format($transportEntry->gross_wt ?? 0,2) }} Kg</td>
                    <td><b>Tare Weight</b></td>
                    <td>{{ number_format($transportEntry->tare_wt ?? 0,2) }} Kg</td>
                </tr>
                <tr>
                    <td><b>Net Weight</b></td>
                    <td colspan="3">{{ number_format($transportEntry->net_wt ?? 0,2) }} Kg</td>
                </tr>
            </table>
        </div>

        <!-- Financial -->
        <div class="section">
            <div class="title">Financial Summary</div>
            <table>
                <tr><td>Total</td><td colspan="3">₹{{ number_format($transportEntry->total ?? 0,2) }}</td></tr>
                @if($transportEntry->frt_advance)
                <tr><td>Freight Advance</td><td colspan="3">₹{{ number_format($transportEntry->frt_advance,2) }}</td></tr>
                @endif
                <tr><td><b>Grand Total</b></td><td colspan="3"><b>₹{{ number_format($transportEntry->grand_total ?? 0,2) }}</b></td></tr>
            </table>
        </div>

        <!-- Bank -->
        <div class="section">
            <div class="title">Bank Details</div>
            <table>
                <tr>
                    <td><b>Bank Name</b></td>
                    <td>{{ $transportEntry->company->bankname ?? '' }}</td>
                    <td><b>A/C No</b></td>
                    <td>{{ $transportEntry->company->bankaccount ?? '' }}</td>
                </tr>
                <tr>
                    <td><b>Branch</b></td>
                    <td>{{ $transportEntry->company->branch ?? '' }}</td>
                    <td><b>IFSC</b></td>
                    <td>{{ $transportEntry->company->ifsccode ?? '' }}</td>
                </tr>
            </table>
        </div>

        <!-- Terms -->
        <div class="footer">
            <b>Terms & Conditions:</b><br>
            1. Payment on Tax invoice.<br>
            2. Booking amount 20%.<br>
            3. Prices valid only after booking.<br>
            4. Subject to Raipur Jurisdiction.<br>
        </div>

        <!-- Signature -->
        <div class="signature">
            <b>For, {{ $transportEntry->company->name ?? '' }}</b><br><br><br>
            ____________________<br>
            Authorized Signatory
        </div>
    </div>
</body>
</html>
