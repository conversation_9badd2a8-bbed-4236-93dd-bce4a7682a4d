@extends('layouts.app')

@section('title', 'Edit Transport Entry - JMD Traders')

@section('content')
<style>
    .compact-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 15px;
    }

    .header-card {
        background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .header-card h3 {
        margin: 0;
        font-size: 1.4rem;
        font-weight: 700;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .form-card {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .form-section {
        border-bottom: 1px solid #e3e6f0;
        padding: 15px 20px;
    }

    .form-section:last-child {
        border-bottom: none;
    }

    .section-title {
        font-size: 1rem;
        font-weight: 600;
        color: #495057;
        margin-bottom: 15px;
        padding-bottom: 8px;
        border-bottom: 2px solid #e3e6f0;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
        align-items: start;
    }

    .form-group {
        margin-bottom: 0;
    }

    .form-label {
        font-weight: 500;
        color: #495057;
        margin-bottom: 5px;
        font-size: 0.875rem;
    }

    .form-control, .form-select {
        border: 1px solid #d1d3e2;
        border-radius: 4px;
        padding: 8px 12px;
        font-size: 0.875rem;
        transition: all 0.2s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #4e73df;
        box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    }

    .btn {
        padding: 8px 16px;
        font-size: 0.875rem;
        font-weight: 500;
        border-radius: 4px;
        transition: all 0.2s ease;
    }

    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    .btn-success {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        border: none;
    }

    .btn-outline-primary {
        border-color: #4e73df;
        color: #4e73df;
    }

    .btn-outline-primary:hover {
        background: #4e73df;
        border-color: #4e73df;
    }

    .btn-secondary {
        background: #6c757d;
        border-color: #6c757d;
    }

    .btn-light {
        background: rgba(255, 255, 255, 0.9);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: #495057;
    }

    .btn-light:hover {
        background: white;
        color: #495057;
    }

    .totals-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }

    .total-item {
        background: #f8f9fc;
        padding: 12px;
        border-radius: 6px;
        border-left: 4px solid #4e73df;
    }

    .total-label {
        font-size: 0.75rem;
        color: #6c757d;
        text-transform: uppercase;
        font-weight: 600;
        margin-bottom: 4px;
    }

    .total-value {
        font-size: 1.1rem;
        font-weight: 700;
        color: #495057;
    }

    .alert {
        border: none;
        border-radius: 6px;
        padding: 12px 16px;
        margin-bottom: 15px;
    }

    .alert-success {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        color: #155724;
    }

    .alert-danger {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        color: #721c24;
    }

    .required {
        color: #e74c3c;
    }

    .d-flex.gap-2 > * {
        margin-right: 8px;
    }

    .d-flex.gap-2 > *:last-child {
        margin-right: 0;
    }

    @media (max-width: 768px) {
        .compact-container {
            padding: 10px;
        }

        .form-grid {
            grid-template-columns: 1fr;
        }

        .totals-grid {
            grid-template-columns: repeat(2, 1fr);
        }

        .btn {
            padding: 8px 16px;
            font-size: 0.75rem;
        }

        .header-card h3 {
            font-size: 1.2rem;
        }
    }
</style>

<div class="compact-container">
    <!-- Header -->
    <div class="header-card">
        <div class="d-flex justify-content-between align-items-center">
            <h3>
                <i class="fas fa-edit"></i>
                Edit Transport Entry #{{ $transportEntry->id }}
            </h3>
            <div class="d-flex gap-2">
                <a href="{{ route('entries.transport.show', $transportEntry->id) }}" class="btn btn-light">
                    <i class="fas fa-eye"></i> View Details
                </a>
                <a href="{{ route('entries.transport') }}" class="btn btn-light">
                    <i class="fas fa-arrow-left"></i> Back to List
                </a>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Please fix the following errors:</strong>
            <ul class="mb-0 mt-2">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <!-- Form -->
    <div class="form-card">
        <form action="{{ route('entries.transport.update', $transportEntry->id) }}" method="POST" id="transportForm">
            @csrf
            @method('PUT')

            <!-- Basic Information Section -->
            <div class="form-section">
                <h5 class="section-title">
                    <i class="fas fa-info-circle"></i>
                    Basic Information
                </h5>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="sales_entry_id" class="form-label">Sales Entry <span class="required">*</span></label>
                        <select class="form-select @error('sales_entry_id') is-invalid @enderror" name="sales_entry_id" id="sales_entry_id" required>
                            <option value="">Select Sales Entry</option>
                            @foreach($salesEntries as $salesEntry)
                                <option value="{{ $salesEntry->id }}"
                                        {{ old('sales_entry_id', $transportEntry->sales_entry_id) == $salesEntry->id ? 'selected' : '' }}>
                                    #{{ $salesEntry->id }} - {{ $salesEntry->customer->name ?? 'N/A' }}
                                    ({{ $salesEntry->created_at->format('d/m/Y') }})
                                </option>
                            @endforeach
                        </select>
                        @error('sales_entry_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="transport_date" class="form-label">Transport Date <span class="required">*</span></label>
                        <input type="date" class="form-control @error('transport_date') is-invalid @enderror"
                               name="transport_date" id="transport_date"
                               value="{{ old('transport_date', $transportEntry->transport_date ? $transportEntry->transport_date->format('Y-m-d') : '') }}" required>
                        @error('transport_date')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="due_days" class="form-label">Due Days</label>
                        <input type="number" class="form-control @error('due_days') is-invalid @enderror"
                               name="due_days" id="due_days" min="0"
                               value="{{ old('due_days', $transportEntry->due_days) }}">
                        @error('due_days')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="due_date" class="form-label">Due Date</label>
                        <input type="date" class="form-control @error('due_date') is-invalid @enderror"
                               name="due_date" id="due_date"
                               value="{{ old('due_date', $transportEntry->due_date ? $transportEntry->due_date->format('Y-m-d') : '') }}">
                        @error('due_date')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Transport Details Section -->
            <div class="form-section">
                <h5 class="section-title">
                    <i class="fas fa-truck"></i>
                    Transport Details
                </h5>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="freight_cond" class="form-label">Freight Condition</label>
                        <input type="text" class="form-control @error('freight_cond') is-invalid @enderror"
                               name="freight_cond" id="freight_cond"
                               value="{{ old('freight_cond', $transportEntry->freight_cond) }}">
                        @error('freight_cond')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="lr_no" class="form-label">L.R. Number</label>
                        <input type="text" class="form-control @error('lr_no') is-invalid @enderror"
                               name="lr_no" id="lr_no"
                               value="{{ old('lr_no', $transportEntry->lr_no) }}">
                        @error('lr_no')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="way_bill_no" class="form-label">Way Bill Number</label>
                        <input type="text" class="form-control @error('way_bill_no') is-invalid @enderror"
                               name="way_bill_no" id="way_bill_no"
                               value="{{ old('way_bill_no', $transportEntry->way_bill_no) }}">
                        @error('way_bill_no')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="eway_bill_no" class="form-label">E-Way Bill Number</label>
                        <input type="text" class="form-control @error('eway_bill_no') is-invalid @enderror"
                               name="eway_bill_no" id="eway_bill_no"
                               value="{{ old('eway_bill_no', $transportEntry->eway_bill_no) }}">
                        @error('eway_bill_no')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="eway_bill_date" class="form-label">E-Way Bill Date</label>
                        <input type="date" class="form-control @error('eway_bill_date') is-invalid @enderror"
                               name="eway_bill_date" id="eway_bill_date"
                               value="{{ old('eway_bill_date', $transportEntry->eway_bill_date ? $transportEntry->eway_bill_date->format('Y-m-d') : '') }}">
                        @error('eway_bill_date')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="doc_through" class="form-label">Document Through</label>
                        <input type="text" class="form-control @error('doc_through') is-invalid @enderror"
                               name="doc_through" id="doc_through"
                               value="{{ old('doc_through', $transportEntry->doc_through) }}">
                        @error('doc_through')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Driver & Vehicle Details Section -->
            <div class="form-section">
                <h5 class="section-title">
                    <i class="fas fa-id-card"></i>
                    Driver & Vehicle Details
                </h5>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="driver_name" class="form-label">Driver Name</label>
                        <input type="text" class="form-control @error('driver_name') is-invalid @enderror"
                               name="driver_name" id="driver_name"
                               value="{{ old('driver_name', $transportEntry->driver_name) }}">
                        @error('driver_name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="dl_no" class="form-label">Driving License Number</label>
                        <input type="text" class="form-control @error('dl_no') is-invalid @enderror"
                               name="dl_no" id="dl_no"
                               value="{{ old('dl_no', $transportEntry->dl_no) }}">
                        @error('dl_no')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="driver_mob_no" class="form-label">Driver Mobile Number</label>
                        <input type="text" class="form-control @error('driver_mob_no') is-invalid @enderror"
                               name="driver_mob_no" id="driver_mob_no"
                               value="{{ old('driver_mob_no', $transportEntry->driver_mob_no) }}">
                        @error('driver_mob_no')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="road_permit_no" class="form-label">Road Permit Number</label>
                        <input type="text" class="form-control @error('road_permit_no') is-invalid @enderror"
                               name="road_permit_no" id="road_permit_no"
                               value="{{ old('road_permit_no', $transportEntry->road_permit_no) }}">
                        @error('road_permit_no')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Location Details Section -->
            <div class="form-section">
                <h5 class="section-title">
                    <i class="fas fa-map-marker-alt"></i>
                    Location Details
                </h5>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="from_city" class="form-label">From City</label>
                        <input type="text" class="form-control @error('from_city') is-invalid @enderror"
                               name="from_city" id="from_city"
                               value="{{ old('from_city', $transportEntry->from_city) }}">
                        @error('from_city')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="to_city" class="form-label">To City</label>
                        <input type="text" class="form-control @error('to_city') is-invalid @enderror"
                               name="to_city" id="to_city"
                               value="{{ old('to_city', $transportEntry->to_city) }}">
                        @error('to_city')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="destination" class="form-label">Destination</label>
                        <input type="text" class="form-control @error('destination') is-invalid @enderror"
                               name="destination" id="destination"
                               value="{{ old('destination', $transportEntry->destination) }}">
                        @error('destination')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="material_direct_delivered_from" class="form-label">Material Direct Delivered From</label>
                        <input type="text" class="form-control @error('material_direct_delivered_from') is-invalid @enderror"
                               name="material_direct_delivered_from" id="material_direct_delivered_from"
                               value="{{ old('material_direct_delivered_from', $transportEntry->material_direct_delivered_from) }}">
                        @error('material_direct_delivered_from')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="delivery_1" class="form-label">Delivery Address 1</label>
                        <input type="text" class="form-control @error('delivery_1') is-invalid @enderror"
                               name="delivery_1" id="delivery_1"
                               value="{{ old('delivery_1', $transportEntry->delivery_1) }}">
                        @error('delivery_1')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="delivery_2" class="form-label">Delivery Address 2</label>
                        <input type="text" class="form-control @error('delivery_2') is-invalid @enderror"
                               name="delivery_2" id="delivery_2"
                               value="{{ old('delivery_2', $transportEntry->delivery_2) }}">
                        @error('delivery_2')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Insurance & Financial Details Section -->
            <div class="form-section">
                <h5 class="section-title">
                    <i class="fas fa-shield-alt"></i>
                    Insurance & Financial Details
                </h5>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="goods_insured_by" class="form-label">Goods Insured By</label>
                        <input type="text" class="form-control @error('goods_insured_by') is-invalid @enderror"
                               name="goods_insured_by" id="goods_insured_by"
                               value="{{ old('goods_insured_by', $transportEntry->goods_insured_by) }}">
                        @error('goods_insured_by')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="policy_no" class="form-label">Policy Number</label>
                        <input type="text" class="form-control @error('policy_no') is-invalid @enderror"
                               name="policy_no" id="policy_no"
                               value="{{ old('policy_no', $transportEntry->policy_no) }}">
                        @error('policy_no')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="lc_no" class="form-label">L.C. Number</label>
                        <input type="text" class="form-control @error('lc_no') is-invalid @enderror"
                               name="lc_no" id="lc_no"
                               value="{{ old('lc_no', $transportEntry->lc_no) }}">
                        @error('lc_no')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="ac_of" class="form-label">Account Of</label>
                        <input type="text" class="form-control @error('ac_of') is-invalid @enderror"
                               name="ac_of" id="ac_of"
                               value="{{ old('ac_of', $transportEntry->ac_of) }}">
                        @error('ac_of')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="our_offer_no" class="form-label">Our Offer Number</label>
                        <input type="text" class="form-control @error('our_offer_no') is-invalid @enderror"
                               name="our_offer_no" id="our_offer_no"
                               value="{{ old('our_offer_no', $transportEntry->our_offer_no) }}">
                        @error('our_offer_no')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="cash_address" class="form-label">Cash Address</label>
                        <textarea class="form-control @error('cash_address') is-invalid @enderror"
                                  name="cash_address" id="cash_address" rows="3">{{ old('cash_address', $transportEntry->cash_address) }}</textarea>
                        @error('cash_address')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Export & Additional Details Section -->
            <div class="form-section">
                <h5 class="section-title">
                    <i class="fas fa-file-export"></i>
                    Export & Additional Details
                </h5>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="export_inv_no" class="form-label">Export Invoice Number</label>
                        <input type="text" class="form-control @error('export_inv_no') is-invalid @enderror"
                               name="export_inv_no" id="export_inv_no"
                               value="{{ old('export_inv_no', $transportEntry->export_inv_no) }}">
                        @error('export_inv_no')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="export_inv_date" class="form-label">Export Invoice Date</label>
                        <input type="date" class="form-control @error('export_inv_date') is-invalid @enderror"
                               name="export_inv_date" id="export_inv_date"
                               value="{{ old('export_inv_date', $transportEntry->export_inv_date ? $transportEntry->export_inv_date->format('Y-m-d') : '') }}">
                        @error('export_inv_date')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="prepared_by" class="form-label">Prepared By</label>
                        <input type="text" class="form-control @error('prepared_by') is-invalid @enderror"
                               name="prepared_by" id="prepared_by"
                               value="{{ old('prepared_by', $transportEntry->prepared_by) }}">
                        @error('prepared_by')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="remarks" class="form-label">Remarks</label>
                        <textarea class="form-control @error('remarks') is-invalid @enderror"
                                  name="remarks" id="remarks" rows="3">{{ old('remarks', $transportEntry->remarks) }}</textarea>
                        @error('remarks')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Weight Details Section -->
            <div class="form-section">
                <h5 class="section-title">
                    <i class="fas fa-weight"></i>
                    Weight Details
                </h5>
                <div class="totals-grid">
                    <div class="total-item">
                        <div class="total-label">Gross Weight</div>
                        <input type="number" class="form-control total-value @error('gross_wt') is-invalid @enderror"
                               name="gross_wt" id="gross_wt" step="0.01"
                               value="{{ old('gross_wt', $transportEntry->gross_wt ?? 0) }}"
                               style="border: none; background: transparent; font-size: 1.1rem; font-weight: 700; color: #495057;">
                        @error('gross_wt')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="total-item">
                        <div class="total-label">Tare Weight</div>
                        <input type="number" class="form-control total-value @error('tare_wt') is-invalid @enderror"
                               name="tare_wt" id="tare_wt" step="0.01"
                               value="{{ old('tare_wt', $transportEntry->tare_wt ?? 0) }}"
                               style="border: none; background: transparent; font-size: 1.1rem; font-weight: 700; color: #495057;">
                        @error('tare_wt')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="total-item">
                        <div class="total-label">Net Weight</div>
                        <input type="number" class="form-control total-value"
                               name="net_wt" id="net_wt" step="0.01"
                               value="{{ old('net_wt', $transportEntry->net_wt ?? 0) }}" readonly
                               style="border: none; background: transparent; font-size: 1.1rem; font-weight: 700; color: #495057;">
                    </div>
                </div>
            </div>

            <!-- Financial Details Section -->
            <div class="form-section">
                <h5 class="section-title">
                    <i class="fas fa-calculator"></i>
                    Financial Details
                </h5>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="total" class="form-label">Total Amount</label>
                        <input type="number" class="form-control @error('total') is-invalid @enderror"
                               name="total" id="total" step="0.01"
                               value="{{ old('total', $transportEntry->total ?? 0) }}">
                        @error('total')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="ins_pmt" class="form-label">Insurance Per MT</label>
                        <input type="number" class="form-control @error('ins_pmt') is-invalid @enderror"
                               name="ins_pmt" id="ins_pmt" step="0.01"
                               value="{{ old('ins_pmt', $transportEntry->ins_pmt ?? 0) }}">
                        @error('ins_pmt')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="insurance" class="form-label">Insurance Amount</label>
                        <input type="number" class="form-control @error('insurance') is-invalid @enderror"
                               name="insurance" id="insurance" step="0.01"
                               value="{{ old('insurance', $transportEntry->insurance ?? 0) }}">
                        @error('insurance')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="frt_advance" class="form-label">Freight Advance</label>
                        <input type="number" class="form-control @error('frt_advance') is-invalid @enderror"
                               name="frt_advance" id="frt_advance" step="0.01"
                               value="{{ old('frt_advance', $transportEntry->frt_advance ?? 0) }}">
                        @error('frt_advance')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="tcs_percent" class="form-label">TCS Percentage</label>
                        <input type="number" class="form-control @error('tcs_percent') is-invalid @enderror"
                               name="tcs_percent" id="tcs_percent" step="0.01" min="0" max="100"
                               value="{{ old('tcs_percent', $transportEntry->tcs_percent ?? 0) }}">
                        @error('tcs_percent')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Calculated Totals -->
                <div class="totals-grid mt-3">
                    <div class="total-item">
                        <div class="total-label">Grand Total</div>
                        <input type="number" class="form-control total-value"
                               name="grand_total" id="grand_total" step="0.01"
                               value="{{ old('grand_total', $transportEntry->grand_total ?? 0) }}" readonly
                               style="border: none; background: transparent; font-size: 1.1rem; font-weight: 700; color: #495057;">
                    </div>

                    <div class="total-item">
                        <div class="total-label">TCS Amount</div>
                        <input type="number" class="form-control total-value"
                               name="tcs_amount" id="tcs_amount" step="0.01"
                               value="{{ old('tcs_amount', $transportEntry->tcs_amount ?? 0) }}" readonly
                               style="border: none; background: transparent; font-size: 1.1rem; font-weight: 700; color: #495057;">
                    </div>

                    <div class="total-item">
                        <div class="total-label">Net Amount</div>
                        <input type="number" class="form-control total-value"
                               name="net_amount" id="net_amount" step="0.01"
                               value="{{ old('net_amount', $transportEntry->net_amount ?? 0) }}" readonly
                               style="border: none; background: transparent; font-size: 1.1rem; font-weight: 700; color: #495057;">
                    </div>
                </div>
            </div>

            <!-- Vehicle & Status Details Section -->
            <div class="form-section">
                <h5 class="section-title">
                    <i class="fas fa-truck-moving"></i>
                    Vehicle & Status Details
                </h5>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="vehicle_number" class="form-label">Vehicle Number</label>
                        <input type="text" class="form-control @error('vehicle_number') is-invalid @enderror"
                               name="vehicle_number" id="vehicle_number"
                               value="{{ old('vehicle_number', $transportEntry->vehicle_number) }}">
                        @error('vehicle_number')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="status" class="form-label">Status <span class="required">*</span></label>
                        <select class="form-select @error('status') is-invalid @enderror" name="status" id="status" required>
                            <option value="">Select Status</option>
                            <option value="scheduled" {{ old('status', $transportEntry->status) == 'scheduled' ? 'selected' : '' }}>Scheduled</option>
                            <option value="in_transit" {{ old('status', $transportEntry->status) == 'in_transit' ? 'selected' : '' }}>In Transit</option>
                            <option value="delivered" {{ old('status', $transportEntry->status) == 'delivered' ? 'selected' : '' }}>Delivered</option>
                            <option value="cancelled" {{ old('status', $transportEntry->status) == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                        </select>
                        @error('status')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group" style="grid-column: 1 / -1;">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control @error('notes') is-invalid @enderror"
                                  name="notes" id="notes" rows="4">{{ old('notes', $transportEntry->notes) }}</textarea>
                        @error('notes')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- IRN Details Section -->
            <div class="form-section">
                <h5 class="section-title">
                    <i class="fas fa-file-invoice"></i>
                    IRN Details
                </h5>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="irn_number" class="form-label">IRN Number</label>
                        <input type="text" class="form-control @error('irn_number') is-invalid @enderror"
                               name="irn_number" id="irn_number"
                               value="{{ old('irn_number', $transportEntry->irn_number) }}">
                        @error('irn_number')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="irn_date" class="form-label">IRN Date</label>
                        <input type="date" class="form-control @error('irn_date') is-invalid @enderror"
                               name="irn_date" id="irn_date"
                               value="{{ old('irn_date', $transportEntry->irn_date ? $transportEntry->irn_date->format('Y-m-d') : '') }}">
                        @error('irn_date')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="irn_status" class="form-label">IRN Status</label>
                        <select class="form-select @error('irn_status') is-invalid @enderror" name="irn_status" id="irn_status">
                            <option value="">Select IRN Status</option>
                            <option value="pending" {{ old('irn_status', $transportEntry->irn_status) == 'pending' ? 'selected' : '' }}>Pending</option>
                            <option value="generated" {{ old('irn_status', $transportEntry->irn_status) == 'generated' ? 'selected' : '' }}>Generated</option>
                            <option value="cancelled" {{ old('irn_status', $transportEntry->irn_status) == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                        </select>
                        @error('irn_status')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group" style="grid-column: 1 / -1;">
                        <label for="irn_remarks" class="form-label">IRN Remarks</label>
                        <textarea class="form-control @error('irn_remarks') is-invalid @enderror"
                                  name="irn_remarks" id="irn_remarks" rows="3">{{ old('irn_remarks', $transportEntry->irn_remarks) }}</textarea>
                        @error('irn_remarks')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="form-section">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex gap-2">
                        <a href="{{ route('entries.transport') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                        <a href="{{ route('entries.transport.show', $transportEntry->id) }}" class="btn btn-outline-primary">
                            <i class="fas fa-eye"></i> View Details
                        </a>
                        <a href="{{ route('entries.transport.print', $transportEntry->id) }}" class="btn btn-outline-secondary" target="_blank">
                            <i class="fas fa-print"></i> Print Invoice
                        </a>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                            <i class="fas fa-undo"></i> Reset
                        </button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> Update Transport Entry
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Calculate net weight
    function calculateNetWeight() {
        const grossWt = parseFloat(document.getElementById('gross_wt').value) || 0;
        const tareWt = parseFloat(document.getElementById('tare_wt').value) || 0;
        const netWt = grossWt - tareWt;
        document.getElementById('net_wt').value = netWt.toFixed(2);
    }

    // Calculate totals
    function calculateTotals() {
        const total = parseFloat(document.getElementById('total').value) || 0;
        const insPmt = parseFloat(document.getElementById('ins_pmt').value) || 0;
        const insurance = parseFloat(document.getElementById('insurance').value) || 0;
        const frtAdvance = parseFloat(document.getElementById('frt_advance').value) || 0;
        const tcsPercent = parseFloat(document.getElementById('tcs_percent').value) || 0;

        // Calculate grand total
        const grandTotal = total + insPmt + insurance + frtAdvance;
        document.getElementById('grand_total').value = grandTotal.toFixed(2);

        // Calculate TCS amount
        const tcsAmount = (grandTotal * tcsPercent) / 100;
        document.getElementById('tcs_amount').value = tcsAmount.toFixed(2);

        // Calculate net amount
        const netAmount = grandTotal + tcsAmount;
        document.getElementById('net_amount').value = netAmount.toFixed(2);
    }

    // Event listeners for weight calculation
    document.getElementById('gross_wt').addEventListener('input', calculateNetWeight);
    document.getElementById('tare_wt').addEventListener('input', calculateNetWeight);

    // Event listeners for totals calculation
    document.querySelectorAll('#total, #ins_pmt, #insurance, #frt_advance, #tcs_percent').forEach(field => {
        field.addEventListener('input', calculateTotals);
    });

    // Calculate due date when due days is entered
    document.getElementById('due_days').addEventListener('input', function() {
        const dueDays = parseInt(this.value) || 0;
        const transportDate = document.getElementById('transport_date').value;

        if (transportDate && dueDays > 0) {
            const date = new Date(transportDate);
            date.setDate(date.getDate() + dueDays);
            document.getElementById('due_date').value = date.toISOString().split('T')[0];
        }
    });

    // Initialize calculations
    calculateNetWeight();
    calculateTotals();
});

// Reset form function
function resetForm() {
    if (confirm('Are you sure you want to reset the form? All unsaved changes will be lost.')) {
        document.getElementById('transportForm').reset();
        // Recalculate after reset
        setTimeout(() => {
            calculateNetWeight();
            calculateTotals();
        }, 100);
    }
}

// Make functions global for inline calls
window.calculateNetWeight = function() {
    const grossWt = parseFloat(document.getElementById('gross_wt').value) || 0;
    const tareWt = parseFloat(document.getElementById('tare_wt').value) || 0;
    const netWt = grossWt - tareWt;
    document.getElementById('net_wt').value = netWt.toFixed(2);
};

window.calculateTotals = function() {
    const total = parseFloat(document.getElementById('total').value) || 0;
    const insPmt = parseFloat(document.getElementById('ins_pmt').value) || 0;
    const insurance = parseFloat(document.getElementById('insurance').value) || 0;
    const frtAdvance = parseFloat(document.getElementById('frt_advance').value) || 0;
    const tcsPercent = parseFloat(document.getElementById('tcs_percent').value) || 0;

    // Calculate grand total
    const grandTotal = total + insPmt + insurance + frtAdvance;
    document.getElementById('grand_total').value = grandTotal.toFixed(2);

    // Calculate TCS amount
    const tcsAmount = (grandTotal * tcsPercent) / 100;
    document.getElementById('tcs_amount').value = tcsAmount.toFixed(2);

    // Calculate net amount
    const netAmount = grandTotal + tcsAmount;
    document.getElementById('net_amount').value = netAmount.toFixed(2);
};
</script>
@endsection
