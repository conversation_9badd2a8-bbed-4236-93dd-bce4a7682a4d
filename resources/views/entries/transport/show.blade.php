@extends('layouts.app')

@section('title', 'Transport Entry Details - JMD Traders')

@section('content')
<style>
    :root {
        --primary-color: #4e73df;
        --secondary-color: #858796;
        --success-color: #1cc88a;
        --info-color: #36b9cc;
        --warning-color: #f6c23e;
        --danger-color: #e74a3b;
        --light-color: #f8f9fc;
        --dark-color: #5a5c69;
        --transport-color: #28a745;
    }

    /* Document Container - Quotation Style */
    .document-container {
      
        margin: 0 auto;
        padding: 20px;
        background: white;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
    }

    /* Header Section - Professional Look */
    .document-header {
        border: 2px solid var(--primary-color);
        margin-bottom: 25px;
        border-radius: 8px;
        overflow: hidden;
    }

    .header-top {
        background: linear-gradient(135deg, var(--primary-color) 0%, #224abe 100%);
        color: white;
        padding: 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .company-logo {
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        font-weight: bold;
        margin-right: 20px;
    }

    .company-info h1 {
        font-size: 28px;
        font-weight: 700;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .company-subtitle {
        font-size: 14px;
        opacity: 0.9;
        margin-top: 5px;
    }

    .document-type {
        text-align: center;
        background: rgba(255, 255, 255, 0.15);
        padding: 12px 24px;
        border-radius: 25px;
        font-size: 16px;
        font-weight: 600;
        letter-spacing: 1px;
        text-transform: uppercase;
    }

    .header-details {
        background: var(--light-color);
        padding: 20px;
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        border-top: 1px solid #e3e6f0;
    }

    /* Status Badge */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 8px 16px;
        border-radius: 25px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        gap: 6px;
    }

    .status-scheduled { background: #fff3cd; color: #856404; border: 2px solid #ffeaa7; }
    .status-in_transit { background: #d1ecf1; color: #0c5460; border: 2px solid #bee5eb; }
    .status-delivered { background: #d4edda; color: #155724; border: 2px solid #c3e6cb; }
    .status-cancelled { background: #f8d7da; color: #721c24; border: 2px solid #f5c6cb; }

    /* Action Buttons */
    .action-toolbar {
        display: flex;
        gap: 12px;
        margin-bottom: 25px;
        flex-wrap: wrap;
        justify-content: center;
    }

    .btn-action {
        padding: 12px 24px;
        border: none;
        border-radius: 6px;
        font-weight: 600;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        min-width: 140px;
        justify-content: center;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        text-decoration: none;
    }

    .btn-primary { background: linear-gradient(135deg, var(--primary-color) 0%, #224abe 100%); color: white; }
    .btn-success { background: linear-gradient(135deg, var(--success-color) 0%, #17a673 100%); color: white; }
    .btn-warning { background: linear-gradient(135deg, var(--warning-color) 0%, #e0a800 100%); color: #212529; }
    .btn-secondary { background: linear-gradient(135deg, var(--secondary-color) 0%, #6c757d 100%); color: white; }
    .btn-whatsapp { background: linear-gradient(135deg, #25d366 0%, #1da851 100%); color: white; }

    /* Information Cards */
    .info-section {
        background: white;
        border: 1px solid #e3e6f0;
        border-radius: 8px;
        margin-bottom: 20px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .section-header {
        background: linear-gradient(135deg, var(--light-color) 0%, #e3e6f0 100%);
        padding: 15px 20px;
        border-bottom: 1px solid #e3e6f0;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .section-header h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 700;
        color: var(--primary-color);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .section-header i {
        color: var(--primary-color);
        font-size: 18px;
    }

    /* Detail Grid */
    .detail-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 0;
        padding: 0;
    }

    .detail-item {
        display: flex;
        align-items: center;
        padding: 15px 20px;
        border-bottom: 1px solid #f8f9fa;
        transition: background 0.2s ease;
    }

    .detail-item:hover {
        background: #f8f9fa;
    }

    .detail-item:last-child {
        border-bottom: none;
    }

    .detail-label {
        font-weight: 600;
        color: var(--dark-color);
        font-size: 13px;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        min-width: 120px;
        margin-right: 15px;
    }

    .detail-value {
        font-weight: 500;
        color: #333;
        font-size: 14px;
        flex: 1;
        padding: 8px 15px;
        background: #f8f9fa;
        border-radius: 4px;
        border-left: 3px solid var(--primary-color);
    }

    /* Weight Display */
    .weight-container {
        background: linear-gradient(135deg, var(--light-color) 0%, #e3e6f0 100%);
        padding: 25px;
        border-radius: 8px;
        margin: 20px 0;
    }

    .weight-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
    }

    .weight-card {
        background: white;
        padding: 20px;
        border-radius: 8px;
        text-align: center;
        border: 2px solid #e3e6f0;
        transition: all 0.3s ease;
    }

    .weight-card:hover {
        border-color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    }

    .weight-label {
        font-size: 12px;
        color: var(--secondary-color);
        font-weight: 600;
        text-transform: uppercase;
        margin-bottom: 10px;
        letter-spacing: 0.5px;
    }

    .weight-value {
        font-size: 24px;
        font-weight: 700;
        color: var(--success-color);
    }

    /* Financial Summary */
    .financial-summary {
        background: linear-gradient(135deg, #fff 0%, var(--light-color) 100%);
        border: 2px solid var(--primary-color);
        border-radius: 8px;
        padding: 25px;
        margin: 25px 0;
    }

    .financial-header {
        text-align: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 2px solid var(--primary-color);
    }

    .financial-title {
        font-size: 20px;
        font-weight: 700;
        color: var(--primary-color);
        margin: 0;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .financial-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 0;
        border-bottom: 1px solid #e3e6f0;
        font-size: 14px;
    }

    .financial-item:last-child {
        border-bottom: none;
        padding-top: 20px;
        margin-top: 15px;
        border-top: 2px solid var(--success-color);
        font-size: 18px;
        font-weight: 700;
        color: var(--success-color);
    }

    .financial-label {
        font-weight: 600;
        color: var(--dark-color);
    }

    .financial-value {
        font-weight: 600;
        color: #333;
        font-family: 'Courier New', monospace;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .document-container { padding: 15px; margin: 10px; }
        .header-details { grid-template-columns: 1fr; gap: 15px; }
        .detail-grid { grid-template-columns: 1fr; }
        .weight-grid { grid-template-columns: 1fr; }
        .action-toolbar { flex-direction: column; align-items: center; }
        .btn-action { width: 100%; max-width: 300px; }
    }

    @media print {
        .action-toolbar { display: none; }
        .document-container { box-shadow: none; margin: 0; padding: 0; }
        .btn-action { display: none; }
    }
</style>

<div class="document-container">
    <!-- Document Header -->
    <div class="document-header">
        <div class="header-top">
            <div style="display: flex; align-items: center;">
                <div class="company-logo">JMD</div>
                <div class="company-info">
                    <h1>JMD Trading Co.</h1>
                    <div class="company-subtitle">Transport & Logistics Solutions</div>
                </div>
            </div>
            <div class="document-type">
                <i class="fas fa-truck"></i>
                Transport Entry
            </div>
        </div>
        
        <div class="header-details">
            <div class="left-details">
                <div class="detail-item">
                    <span class="detail-label">Entry Number:</span>
                    <span class="detail-value">#{{ str_pad($transportEntry->id, 6, '0', STR_PAD_LEFT) }}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Date:</span>
                    <span class="detail-value">{{ $transportEntry->transport_date ? $transportEntry->transport_date->format('d M Y') : 'Not Set' }}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Status:</span>
                    <span class="status-badge status-{{ $transportEntry->status }}">
                        @if($transportEntry->status === 'scheduled')
                            <i class="fas fa-clock"></i>
                        @elseif($transportEntry->status === 'in_transit')
                            <i class="fas fa-shipping-fast"></i>
                        @elseif($transportEntry->status === 'delivered')
                            <i class="fas fa-check-circle"></i>
                        @else
                            <i class="fas fa-times-circle"></i>
                        @endif
                        {{ ucfirst(str_replace('_', ' ', $transportEntry->status ?? 'scheduled')) }}
                    </span>
                </div>
            </div>
            <div class="right-details">
                <div class="detail-item">
                    <span class="detail-label">Created By:</span>
                    <span class="detail-value">{{ $transportEntry->user->name ?? 'System' }}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Created At:</span>
                    <span class="detail-value">{{ $transportEntry->created_at->format('d M Y, h:i A') }}</span>
                </div>
                @if($transportEntry->salesEntry)
                <div class="detail-item">
                    <span class="detail-label">Linked Sales:</span>
                    <span class="detail-value">{{ $transportEntry->salesEntry->quotation_number }}</span>
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Action Toolbar -->
    <div class="action-toolbar">
        <a href="{{ route('entries.transport.edit', $transportEntry->id) }}" class="btn-action btn-warning">
            <i class="fas fa-edit"></i> Edit Entry
        </a>
        <a href="{{ route('entries.transport.print', $transportEntry->id) }}" class="btn-action btn-info" target="_blank">
            <i class="fas fa-file-pdf"></i> Print PDF with Company Header
        </a>
        <a href="{{ route('entries.transport.pdf', $transportEntry->id) }}" class="btn-action btn-success">
            <i class="fas fa-download"></i> Download PDF
        </a>
        <button onclick="shareToWhatsApp()" class="btn-action btn-whatsapp">
            <i class="fab fa-whatsapp"></i> Share on WhatsApp
        </button>
        <a href="{{ route('entries.transport') }}" class="btn-action btn-primary">
            <i class="fas fa-arrow-left"></i> Back to List
        </a>
    </div>

    <!-- Information Sections -->
    <div class="row">
        <!-- Transport & Driver Information -->
        <div class="col-lg-6">
            <div class="info-section">
                <div class="section-header">
                    <i class="fas fa-truck"></i>
                    <h3>Transport & Driver Information</h3>
                </div>
                <div class="detail-grid" style="grid-template-columns: 1fr;">
                    <div class="detail-item">
                        <span class="detail-label">Driver Name:</span>
                        <span class="detail-value">{{ $transportEntry->driver_name ?? 'Not Assigned' }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Driver Mobile:</span>
                        <span class="detail-value">{{ $transportEntry->driver_mob_no ?? 'Not Provided' }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">D.L. Number:</span>
                        <span class="detail-value">{{ $transportEntry->dl_no ?? 'Not Provided' }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">L.R. Number:</span>
                        <span class="detail-value">{{ $transportEntry->lr_no ?? 'Not Provided' }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Way Bill Number:</span>
                        <span class="detail-value">{{ $transportEntry->way_bill_no ?? 'Not Provided' }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Location Information -->
        <div class="col-lg-6">
            <div class="info-section">
                <div class="section-header">
                    <i class="fas fa-map-marker-alt"></i>
                    <h3>Location Information</h3>
                </div>
                <div class="detail-grid" style="grid-template-columns: 1fr;">
                    <div class="detail-item">
                        <span class="detail-label">From City:</span>
                        <span class="detail-value">{{ $transportEntry->from_city ?? 'Not Specified' }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">To City:</span>
                        <span class="detail-value">{{ $transportEntry->to_city ?? 'Not Specified' }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Destination:</span>
                        <span class="detail-value">{{ $transportEntry->destination ?? 'Not Specified' }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Delivery 1:</span>
                        <span class="detail-value">{{ $transportEntry->delivery_1 ?? 'Not Specified' }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Delivery 2:</span>
                        <span class="detail-value">{{ $transportEntry->delivery_2 ?? 'Not Specified' }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Documentation -->
        <div class="col-lg-6">
            <div class="info-section">
                <div class="section-header">
                    <i class="fas fa-file-alt"></i>
                    <h3>Documentation</h3>
                </div>
                <div class="detail-grid" style="grid-template-columns: 1fr;">
                    <div class="detail-item">
                        <span class="detail-label">E-Way Bill No:</span>
                        <span class="detail-value">{{ $transportEntry->eway_bill_no ?? 'Not Provided' }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">E-Way Bill Date:</span>
                        <span class="detail-value">{{ $transportEntry->eway_bill_date ? $transportEntry->eway_bill_date->format('d M Y') : 'Not Set' }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Export Inv. No:</span>
                        <span class="detail-value">{{ $transportEntry->export_inv_no ?? 'Not Provided' }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Export Inv. Date:</span>
                        <span class="detail-value">{{ $transportEntry->export_inv_date ? $transportEntry->export_inv_date->format('d M Y') : 'Not Set' }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Road Permit No:</span>
                        <span class="detail-value">{{ $transportEntry->road_permit_no ?? 'Not Provided' }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Business Information -->
        <div class="col-lg-6">
            <div class="info-section">
                <div class="section-header">
                    <i class="fas fa-briefcase"></i>
                    <h3>Business Information</h3>
                </div>
                <div class="detail-grid" style="grid-template-columns: 1fr;">
                    @if($transportEntry->freight_cond)
                    <div class="detail-item">
                        <span class="detail-label">Freight Condition:</span>
                        <span class="detail-value">{{ $transportEntry->freight_cond }}</span>
                    </div>
                    @endif
                    
                    @if($transportEntry->goods_insured_by)
                    <div class="detail-item">
                        <span class="detail-label">Goods Insured By:</span>
                        <span class="detail-value">{{ $transportEntry->goods_insured_by }}</span>
                    </div>
                    @endif
                    
                    @if($transportEntry->policy_no)
                    <div class="detail-item">
                        <span class="detail-label">Policy Number:</span>
                        <span class="detail-value">{{ $transportEntry->policy_no }}</span>
                    </div>
                    @endif
                    
                    @if($transportEntry->prepared_by)
                    <div class="detail-item">
                        <span class="detail-label">Prepared By:</span>
                        <span class="detail-value">{{ $transportEntry->prepared_by }}</span>
                    </div>
                    @endif
                    
                    @if($transportEntry->remarks)
                    <div class="detail-item">
                        <span class="detail-label">Remarks:</span>
                        <span class="detail-value">{{ $transportEntry->remarks }}</span>
                    </div>
                    @endif
                    
                    @if(!$transportEntry->freight_cond && !$transportEntry->goods_insured_by && !$transportEntry->policy_no && !$transportEntry->prepared_by && !$transportEntry->remarks)
                    <div style="padding: 40px; text-align: center; color: var(--secondary-color); font-style: italic;">
                        <i class="fas fa-info-circle" style="font-size: 24px; margin-bottom: 10px; opacity: 0.5;"></i>
                        <p>No additional business information provided</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Weight Information -->
    <div class="info-section">
        <div class="section-header">
            <i class="fas fa-weight"></i>
            <h3>Weight Information</h3>
        </div>
        <div class="weight-container">
            <div class="weight-grid">
                <div class="weight-card">
                    <div class="weight-label">Gross Weight</div>
                    <div class="weight-value">{{ number_format($transportEntry->gross_wt ?? 0, 2) }} <small style="font-size: 14px; color: var(--secondary-color);">kg</small></div>
                </div>
                <div class="weight-card">
                    <div class="weight-label">Tare Weight</div>
                    <div class="weight-value">{{ number_format($transportEntry->tare_wt ?? 0, 2) }} <small style="font-size: 14px; color: var(--secondary-color);">kg</small></div>
                </div>
                <div class="weight-card">
                    <div class="weight-label">Net Weight</div>
                    <div class="weight-value">{{ number_format($transportEntry->net_wt ?? 0, 2) }} <small style="font-size: 14px; color: var(--secondary-color);">kg</small></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Financial Summary -->
    <div class="financial-summary">
        <div class="financial-header">
            <h2 class="financial-title">
                <i class="fas fa-rupee-sign"></i>
                Financial Summary
            </h2>
        </div>
        
        <div class="financial-item">
            <span class="financial-label">Total Amount</span>
            <span class="financial-value">₹{{ number_format($transportEntry->total ?? 0, 2) }}</span>
        </div>
        
        @if(($transportEntry->ins_pmt ?? 0) > 0)
        <div class="financial-item">
            <span class="financial-label">Insurance P/MT</span>
            <span class="financial-value">₹{{ number_format($transportEntry->ins_pmt, 2) }}</span>
        </div>
        @endif
        
        @if(($transportEntry->insurance ?? 0) > 0)
        <div class="financial-item">
            <span class="financial-label">Insurance</span>
            <span class="financial-value">₹{{ number_format($transportEntry->insurance, 2) }}</span>
        </div>
        @endif
        
        @if(($transportEntry->frt_advance ?? 0) > 0)
        <div class="financial-item">
            <span class="financial-label">Freight Advance</span>
            <span class="financial-value">₹{{ number_format($transportEntry->frt_advance, 2) }}</span>
        </div>
        @endif
        
        <div class="financial-item">
            <span class="financial-label">Grand Total</span>
            <span class="financial-value">₹{{ number_format($transportEntry->grand_total ?? 0, 2) }}</span>
        </div>
        
        @if(($transportEntry->tcs_percent ?? 0) > 0)
        <div class="financial-item">
            <span class="financial-label">TCS ({{ number_format($transportEntry->tcs_percent, 2) }}%)</span>
            <span class="financial-value">₹{{ number_format($transportEntry->tcs_amount ?? 0, 2) }}</span>
        </div>
        @endif
        
        <div class="financial-item">
            <span class="financial-label">
                <i class="fas fa-check-circle"></i>
                Net Amount
            </span>
            <span class="financial-value">₹{{ number_format($transportEntry->calculated_net_amount, 2) }}</span>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// WhatsApp Share Function - Direct PDF Share
function shareToWhatsApp() {
    const transportEntry = @json($transportEntry);
    
    // Get current page URL for PDF link
    const currentUrl = window.location.origin;
    const pdfUrl = `${currentUrl}/entries/transport/${transportEntry.id}/pdf`;
    
    // Create simple message with PDF link
    const message = `🚛 *JMD TRADERS - TRANSPORT ENTRY*

📋 *Entry #${String(transportEntry.id).padStart(6, '0')}*
📅 Date: ${transportEntry.transport_date ? new Date(transportEntry.transport_date).toLocaleDateString('en-GB') : 'Not Set'}
🚛 Driver: ${transportEntry.driver_name || 'Not Assigned'}
📱 Mobile: ${transportEntry.driver_mob_no || 'Not Provided'}
🚗 Vehicle: ${transportEntry.vehicle_number || 'Not Provided'}

📍 *Route:* ${transportEntry.from_city || 'N/A'} → ${transportEntry.to_city || 'N/A'}
💰 *Amount:* ₹${transportEntry.calculated_net_amount ? Number(transportEntry.calculated_net_amount).toLocaleString() : '0'}

📎 *Download PDF Document:*
${pdfUrl}

---
*JMD Traders Transport Management System*`;

    // Encode message for URL
    const encodedMessage = encodeURIComponent(message);
    
    // Create WhatsApp URL
    const whatsappUrl = `https://wa.me/?text=${encodedMessage}`;
    
    // Open WhatsApp
    window.open(whatsappUrl, '_blank');
}
</script>
@endpush
