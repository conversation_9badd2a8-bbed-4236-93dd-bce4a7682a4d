@extends('layouts.app')

@section('title', 'Transport Entries - JMD Traders')

@section('content')


<style>
    :root {
        --primary-color: #4e73df;
        --secondary-color: #858796;
        --success-color: #1cc88a;
        --info-color: #36b9cc;
        --warning-color: #f6c23e;
        --danger-color: #e74a3b;
        --light-color: #f8f9fc;
        --dark-color: #5a5c69;
        --transport-color: #28a745;
    }

    .compact-container {
        /* max-width: 1400px; */
        margin: 0 auto;
        padding: 15px;
    }

    .header-card {
        background: linear-gradient(135deg, var(--primary-color) 0%, #224abe 100%);
        color: white;
        padding: 20px 25px;
        border-radius: 8px;
        margin-bottom: 20px;
        box-shadow: 0 4px 12px rgba(78, 115, 223, 0.15);
        border: none;
    }

    .header-card h3 {
        margin: 0;
        font-size: 1.5rem;
        font-weight: 700;
        display: flex;
        align-items: center;
        gap: 12px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .stats-card {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        overflow: hidden;
        border: 1px solid #e3e6f0;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 0;
    }

    .stat-item {
        padding: 18px 20px;
        border-right: 1px solid #e3e6f0;
        text-align: center;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-item:last-child {
        border-right: none;
    }

    .stat-item:hover {
        background: var(--light-color);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 6px;
        color: var(--dark-color);
    }

    .stat-label {
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-weight: 600;
        color: var(--secondary-color);
    }

    .stat-icon {
        font-size: 1.4rem;
        margin-bottom: 8px;
        opacity: 0.8;
    }

    /* Status-specific colors */
    .stat-item.stat-total .stat-number { color: var(--primary-color); }
    .stat-item.stat-total .stat-icon { color: var(--primary-color); }
    
    .stat-item.stat-scheduled .stat-number { color: var(--warning-color); }
    .stat-item.stat-scheduled .stat-icon { color: var(--warning-color); }
    
    .stat-item.stat-transit .stat-number { color: var(--info-color); }
    .stat-item.stat-transit .stat-icon { color: var(--info-color); }
    
    .stat-item.stat-delivered .stat-number { color: var(--success-color); }
    .stat-item.stat-delivered .stat-icon { color: var(--success-color); }

    .filters-card {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        padding: 18px 25px;
        border: 1px solid #e3e6f0;
    }

    .filter-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        align-items: end;
    }

    .table-card {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    /* Custom pagination integration */
    .table-card .pagination-wrapper {
        border-radius: 0;
        margin: 0;
    }

    .table-header {
        background: linear-gradient(135deg, var(--light-color) 0%, #e3e6f0 100%);
        padding: 18px 25px;
        border-bottom: 2px solid var(--primary-color);
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
    }

    .custom-table {
        margin: 0;
        font-size: 0.875rem;
        border: none;
    }

    .custom-table th {
        background: linear-gradient(135deg, var(--primary-color) 0%, #224abe 100%);
        color: white;
        font-weight: 600;
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        padding: 15px 18px;
        border: none;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .custom-table td {
        padding: 15px 18px;
        vertical-align: middle;
        border-top: 1px solid #e3e6f0;
        color: var(--dark-color);
    }

    .custom-table tbody tr {
        transition: all 0.3s ease;
    }

    .custom-table tbody tr:hover {
        background: var(--light-color);
        transform: translateX(2px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .form-label {
        display: block;
        font-weight: 600;
        color: var(--dark-color);
        font-size: 0.75rem;
        margin-bottom: 6px;
        text-transform: uppercase;
        letter-spacing: 0.3px;
    }

    .form-control, .form-select {
        width: 100%;
        padding: 10px 12px;
        border: 1px solid #e3e6f0;
        border-radius: 6px;
        font-size: 0.875rem;
        background: white;
        transition: all 0.3s ease;
        color: var(--dark-color);
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
        background: white;
        transition: all 0.2s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #28a745;
        background: white;
        outline: none;
        box-shadow: 0 0 0 0.1rem rgba(40, 167, 69, 0.2);
    }

    .btn {
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        font-weight: 600;
        font-size: 0.8rem;
    }

    /* Buttons */
    .btn {
        padding: 10px 20px;
        border-radius: 6px;
        font-weight: 600;
        font-size: 0.875rem;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .btn-primary {
        background: linear-gradient(135deg, var(--primary-color) 0%, #224abe 100%);
        color: white;
        box-shadow: 0 2px 4px rgba(78, 115, 223, 0.2);
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #224abe 0%, #1a365c 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(78, 115, 223, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-success {
        background: linear-gradient(135deg, var(--success-color) 0%, #17a673 100%);
        color: white;
        box-shadow: 0 2px 4px rgba(28, 200, 138, 0.2);
    }

    .btn-success:hover {
        background: linear-gradient(135deg, #17a673 0%, #138f63 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(28, 200, 138, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-outline-secondary {
        border: 2px solid #e3e6f0;
        color: var(--secondary-color);
        background: white;
        transition: all 0.3s ease;
    }

    .btn-outline-secondary:hover {
        background: var(--light-color);
        border-color: var(--primary-color);
        color: var(--primary-color);
        transform: translateY(-2px);
        text-decoration: none;
    }

    .btn-light {
        background: linear-gradient(135deg, var(--light-color) 0%, #e3e6f0 100%);
        color: var(--primary-color);
        border: 1px solid #e3e6f0;
    }

    .btn-light:hover {
        background: linear-gradient(135deg, var(--primary-color) 0%, #224abe 100%);
        color: white;
        transform: translateY(-2px);
        text-decoration: none;
    }

    /* Badges */
    .badge {
        font-size: 0.7rem;
        padding: 6px 12px;
        border-radius: 15px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        display: inline-flex;
        align-items: center;
        gap: 4px;
    }

    .bg-primary { background: var(--primary-color) !important; color: white; }
    .bg-info { background: var(--info-color) !important; color: white; }
    .bg-success { background: var(--success-color) !important; color: white; }
    .bg-warning { background: var(--warning-color) !important; color: #212529; }
    .bg-danger { background: var(--danger-color) !important; color: white; }

    .status-scheduled { background: var(--warning-color); color: #856404; border: 2px solid #ffeaa7; }
    .status-in_transit { background: var(--info-color); color: white; border: 2px solid #bee5eb; }
    .status-delivered { background: var(--success-color); color: white; border: 2px solid #c3e6cb; }
    .status-delayed { background: var(--danger-color); color: white; border: 2px solid #f5c6cb; }

    .action-buttons {
        display: flex;
        gap: 6px;
        flex-wrap: wrap;
        align-items: center;
    }

    .btn-sm {
        padding: 6px 12px;
        font-size: 0.75rem;
        min-width: auto;
    }

    .empty-state {
        text-align: center;
        padding: 50px 20px;
        color: var(--secondary-color);
    }

    .empty-state i {
        font-size: 3rem;
        margin-bottom: 15px;
        opacity: 0.3;
    }

    @media (max-width: 768px) {
        .compact-container {
            padding: 10px;
        }
        
        .filter-grid {
            grid-template-columns: 1fr;
        }
        
        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .custom-table {
            font-size: 0.75rem;
        }
        
        .action-buttons {
            flex-direction: column;
        }
    }
</style>

<div class="compact-container">
    <!-- Header -->
    <div class="header-card">
        <div class="d-flex justify-content-between align-items-center">
            <h3>
                <i class="fas fa-truck"></i>
                Transport Management
            </h3>
            <div class="btn-group">
                @permission('transport.create')
                <a href="{{ route('entries.transport.create') }}" class="btn btn-light">
                    <i class="fas fa-plus"></i> Add Transport Entry
                </a>
                @endpermission

                @permission('transport.view')
                <button type="button" class="btn btn-success" onclick="exportTransport()">
                    <i class="fas fa-file-excel"></i> Export Excel
                </button>
                <button type="button" class="btn btn-info ml-2" onclick="exportTransportCSV()">
                    <i class="fas fa-file-csv"></i> Export CSV
                </button>
                @endpermission
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-card">
        <div class="stats-grid">
            <div class="stat-item stat-total">
                <div class="stat-icon">
                    <i class="fas fa-truck"></i>
                </div>
                <div class="stat-number">{{ $stats['total'] }}</div>
                <div class="stat-label">Total Entries</div>
            </div>
            <div class="stat-item stat-scheduled">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-number">{{ $stats['scheduled'] }}</div>
                <div class="stat-label">Scheduled</div>
            </div>
            <div class="stat-item stat-transit">
                <div class="stat-icon">
                    <i class="fas fa-shipping-fast"></i>
                </div>
                <div class="stat-number">{{ $stats['in_transit'] }}</div>
                <div class="stat-label">In Transit</div>
            </div>
            <div class="stat-item stat-delivered">
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-number">{{ $stats['delivered'] }}</div>
                <div class="stat-label">Delivered</div>
            </div>
            <div class="stat-item">
                <div class="stat-icon" style="color: var(--danger-color);">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-number" style="color: var(--danger-color);">{{ $stats['delayed'] }}</div>
                <div class="stat-label">Delayed</div>
            </div>
            <div class="stat-item">
                <div class="stat-icon" style="color: var(--dark-color);">
                    <i class="fas fa-rupee-sign"></i>
                </div>
                <div class="stat-number" style="color: var(--dark-color);">₹{{ number_format($stats['total_value'], 0) }}</div>
                <div class="stat-label">Total Value</div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filters-card">
        <div class="filter-grid">
            <div class="form-group">
                <label class="form-label">Filter by Status</label>
                <select class="form-select myselect" id="statusFilter">
                    <option value="">All Status</option>
                    <option value="scheduled">Scheduled</option>
                    <option value="in_transit">In Transit</option>
                    <option value="delivered">Delivered</option>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label">Filter by Date</label>
                <input type="date" class="form-control" id="dateFilter">
            </div>
            <div class="form-group">
                <label class="form-label">Search Entries</label>
                <input type="text" class="form-control" id="searchFilter" placeholder="Search by customer, vehicle, driver...">
            </div>
            <div class="form-group">
                <label class="form-label">&nbsp;</label>
                <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                    <i class="fas fa-times"></i> Clear Filters
                </button>
            </div>
        </div>
    </div>

    <!-- Transport Entries Table -->
    <div class="table-card">
        <div class="table-header">
            <h5 class="mb-0">Transport Entries</h5>
        </div>

        <!-- Results Summary -->
        @if(request()->hasAny(['search', 'sales_entry_id', 'status', 'date_from', 'date_to', 'company_id']))
        <div class="alert alert-info mx-3 mt-3 mb-0">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>{{ $transportEntries->total() }}</strong> results found
                    @if(request('search'))
                        for "<strong>{{ request('search') }}</strong>"
                    @endif
                    @if(request()->hasAny(['sales_entry_id', 'status', 'date_from', 'date_to', 'company_id']))
                        with applied filters
                    @endif
                </div>
                <a href="{{ route('entries.transport') }}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-times me-1"></i>
                    Clear All
                </a>
            </div>
        </div>
        @endif

        <div class="table-responsive">
            <table class="table custom-table" id="transportTable">
                <thead>
                    <tr>
                        <th>Sales Entry</th>
                        <th>Customer</th>
                        <th>Transport Date</th>
                        <th>Vehicle</th>
                        <th>Driver Details</th>
                        <th>Delivery Date</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($transportEntries as $entry)
                    <tr>
                        <td>
                            @if($entry->salesEntry)
                                <strong>{{ $entry->salesEntry->quotation_number }}</strong>
                            @else
                                <span class="text-muted">Independent Transport</span>
                            @endif
                        </td>
                        <td>
                            @if($entry->salesEntry && $entry->salesEntry->customer)
                                <div>
                                    <strong>{{ $entry->salesEntry->customer->name }}</strong>
                                    <br>
                                    <small class="text-muted">{{ $entry->salesEntry->customer->mobile ?? 'N/A' }}</small>
                                </div>
                            @else
                                <span class="text-muted">No Customer Linked</span>
                            @endif
                        </td>
                        <td>
                            <span class="badge bg-primary">{{ $entry->transport_date->format('d M Y') }}</span>
                        </td>
                        <td>
                            @if($entry->vehicle_number)
                                <strong>{{ $entry->vehicle_number }}</strong>
                                <br>
                                <small class="text-muted">{{ $entry->vehicle_type ?? 'N/A' }}</small>
                            @else
                                <span class="text-muted">Not Assigned</span>
                            @endif
                        </td>
                        <td>
                            @if($entry->driver_name)
                                <div>
                                    <strong>{{ $entry->driver_name }}</strong>
                                    <br>
                                    <small class="text-muted">
                                        <i class="fas fa-phone"></i> {{ $entry->driver_phone ?? $entry->driver_mob_no ?? 'N/A' }}
                                    </small>
                                </div>
                            @else
                                <span class="text-muted">Not Assigned</span>
                            @endif
                        </td>
                        <td>
                            @if($entry->estimated_delivery)
                                <span class="badge bg-info">{{ $entry->estimated_delivery->format('d M Y') }}</span>
                                @if($entry->is_delayed)
                                    <br><span class="badge status-delayed mt-1">
                                        <i class="fas fa-exclamation-triangle"></i> Delayed
                                    </span>
                                @endif
                            @else
                                <span class="text-muted">Not Set</span>
                            @endif
                        </td>
                        <td>
                            <span class="badge status-{{ $entry->status }}">
                                @if($entry->status === 'scheduled')
                                    <i class="fas fa-clock"></i> Scheduled
                                @elseif($entry->status === 'in_transit')
                                    <i class="fas fa-shipping-fast"></i> In Transit
                                @elseif($entry->status === 'delivered')
                                    <i class="fas fa-check-circle"></i> Delivered
                                @endif
                            </span>
                            <br>
                            <small class="text-muted">{{ $entry->delivery_status }}</small>
                        </td>
                        <td>
                            <div class="action-buttons">
                                @permission('transport.view')
                                <a href="{{ route('entries.transport.show', $entry) }}" class="btn btn-outline-primary btn-sm" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                @endpermission
                                   
                                @permission('transport.edit')
                                <a href="{{ route('entries.transport.edit', $entry) }}" class="btn btn-outline-warning btn-sm" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                @endpermission
                                    
                                @if($entry->status === 'scheduled')
                                <button class="btn btn-outline-success btn-sm" onclick="updateStatus({{ $entry->id }}, 'in_transit')" title="Start Transit">
                                    <i class="fas fa-play"></i>
                                </button>
                                @elseif($entry->status === 'in_transit')
                                <button class="btn btn-outline-info btn-sm" onclick="markDelivered({{ $entry->id }})" title="Mark Delivered">
                                    <i class="fas fa-check"></i>
                                </button>
                                @endif
                                @permission('transport.print')
                                <a href="{{ route('entries.transport.print', $entry) }}" class="btn btn-outline-info btn-sm" title="Print PDF with Company Header" target="_blank">
                                    <i class="fas fa-file-pdf"></i>
                                </a>
                                @endpermission

                                <button class="btn btn-sm btn-outline-success" onclick="openTransportWhatsAppModal({{ $entry->id }}, '{{ $entry->transport_number }}', '{{ $entry->transport_date->format('d M Y') }}', '{{ $entry->customer->phone ?? '' }}')" title="Share Transport via WhatsApp">
                                    <i class="fab fa-whatsapp"></i>
                                    <i class="fas fa-truck" style="font-size: 0.7em; margin-left: 2px;"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-primary" onclick="generateTransportShareLink({{ $entry->id }})" title="Generate Share Links">
                                    <i class="fas fa-share-alt"></i>
                                </button>

                                <button class="btn btn-outline-danger btn-sm" onclick="deleteEntry({{ $entry->id }})" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="8">
                            <div class="empty-state">
                                <i class="fas fa-truck"></i>
                                <h5>No Transport Entries Found</h5>
                                @if(request()->hasAny(['search', 'sales_entry_id', 'status', 'date_from', 'date_to', 'company_id']))
                                    <p class="text-muted">Try adjusting your search criteria or <a href="{{ route('entries.transport') }}">clear all filters</a></p>
                                @else
                                    <p class="text-muted">Create your first transport entry to get started.</p>
                                    @permission('transport.create')
                                    <a href="{{ route('entries.transport.create') }}" class="btn btn-primary">
                                        <i class="fas fa-plus"></i> Create Transport Entry
                                    </a>
                                    @endpermission
                                @endif
                            </div>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Custom Pagination -->
        @include('components.pagination', ['paginator' => $transportEntries])
    </div>
</div>

<!-- Mark Delivered Modal -->
<div class="modal fade" id="deliveredModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Mark as Delivered</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="deliveredForm">
                    <div class="mb-3">
                        <label for="actual_delivery" class="form-label">Actual Delivery Date</label>
                        <input type="date" class="form-control" id="actual_delivery" required>
                    </div>
                    <div class="mb-3">
                        <label for="delivery_notes" class="form-label">Delivery Notes</label>
                        <textarea class="form-control" id="delivery_notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" onclick="confirmDelivery()">Mark Delivered</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    let currentEntryId = null;

    function clearFilters() {
        document.getElementById('statusFilter').value = '';
        document.getElementById('dateFilter').value = '';
        document.getElementById('vehicleFilter').value = '';
        document.getElementById('searchFilter').value = '';
        
        const rows = document.querySelectorAll('#transportTable tbody tr');
        rows.forEach(row => row.style.display = '');
    }

    function updateStatus(entryId, status) {
        if (confirm('Are you sure you want to update the status?')) {
            fetch(`/entries/transport/${entryId}/status`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ status: status })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Error updating status');
                }
            });
        }
    }

    function markDelivered(entryId) {
        currentEntryId = entryId;
        document.getElementById('actual_delivery').value = new Date().toISOString().split('T')[0];
        new bootstrap.Modal(document.getElementById('deliveredModal')).show();
    }

    function confirmDelivery() {
        const actualDelivery = document.getElementById('actual_delivery').value;
        const deliveryNotes = document.getElementById('delivery_notes').value;

        fetch(`/entries/transport/${currentEntryId}/deliver`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                actual_delivery: actualDelivery,
                delivery_notes: deliveryNotes
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                bootstrap.Modal.getInstance(document.getElementById('deliveredModal')).hide();
                location.reload();
            } else {
                alert('Error marking as delivered');
            }
        });
    }

    function deleteEntry(entryId) {
        if (confirm('Are you sure you want to delete this transport entry?')) {
            fetch(`/entries/transport/${entryId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Error deleting entry');
                }
            });
        }
    }

    // Filter functionality
    document.addEventListener('DOMContentLoaded', function() {
        const statusFilter = document.getElementById('statusFilter');
        const dateFilter = document.getElementById('dateFilter');
        const searchFilter = document.getElementById('searchFilter');
        const table = document.getElementById('transportTable');
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));

        function filterTable() {
            const statusValue = statusFilter.value.toLowerCase();
            const dateValue = dateFilter.value;
            const searchValue = searchFilter.value.toLowerCase();

            rows.forEach(row => {
                if (row.cells.length < 2) return; // Skip empty state row

                const statusCell = row.cells[6].textContent.toLowerCase();
                const dateCell = row.cells[2].textContent;
                const searchText = row.textContent.toLowerCase();

                const statusMatch = !statusValue || statusCell.includes(statusValue);
                const dateMatch = !dateValue || dateCell.includes(dateValue);
                const searchMatch = !searchValue || searchText.includes(searchValue);

                row.style.display = statusMatch && dateMatch && searchMatch ? '' : 'none';
            });
        }

        statusFilter.addEventListener('change', filterTable);
        dateFilter.addEventListener('change', filterTable);
        searchFilter.addEventListener('input', debounce(filterTable, 300));
    });

    // Debounce function for search input
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = function() {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Transport WhatsApp Sharing Variables
    let currentTransportData = {};

    function openTransportWhatsAppModal(transportId, transportNumber, transportDate, customerPhone) {
        // Get customer name from the table row
        const row = event.target.closest('tr');
        const customerCell = row.querySelector('td:nth-child(3)'); // Assuming customer is in 3rd column
        const customerName = customerCell ? customerCell.textContent.trim() : 'Customer';

        currentTransportData = {
            id: transportId,
            customerName: customerName,
            transportNumber: transportNumber,
            transportDate: transportDate
        };

        // Pre-fill phone number if available
        document.getElementById('transportPhoneNumber').value = customerPhone || '';

        // Update message preview
        updateTransportMessagePreview();

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('transportWhatsappModal'));
        modal.show();
    }

    function updateTransportMessagePreview() {
        const message = `🚛 *JMD Traders - Transport Service*

Hi ${currentTransportData.customerName},

📋 *Transport:* ${currentTransportData.transportNumber}
📅 *Date:* ${currentTransportData.transportDate}

🔗 *View Online:* ${window.location.origin}/entries/transport/${currentTransportData.id}/print
📎 *Download PDF:* ${window.location.origin}/entries/transport/${currentTransportData.id}/print-pdf

Thank you for choosing our transport service! 🙏
*JMD Traders*
📞 07714007253`;

        document.getElementById('transportMessagePreview').textContent = message;
    }

    function sendTransportWhatsAppMessage() {
        let phoneNumber = document.getElementById('transportPhoneNumber').value.trim();
        const customMessage = document.getElementById('transportCustomMessage').value.trim();

        if (!phoneNumber) {
            alert('Please enter a phone number');
            return;
        }

        // Clean phone number - remove any non-digits
        phoneNumber = phoneNumber.replace(/\D/g, '');

        // Handle different phone number formats
        if (phoneNumber.length === 10) {
            phoneNumber = '91' + phoneNumber;
        } else if (phoneNumber.length === 11 && phoneNumber.startsWith('0')) {
            phoneNumber = '91' + phoneNumber.substring(1);
        } else if (phoneNumber.length === 12 && phoneNumber.startsWith('91')) {
            phoneNumber = phoneNumber;
        } else if (phoneNumber.length === 13 && phoneNumber.startsWith('+91')) {
            phoneNumber = phoneNumber.substring(1);
        } else {
            alert('Please enter a valid phone number (10 digits)');
            return;
        }

        let finalMessage = document.getElementById('transportMessagePreview').textContent;

        if (customMessage) {
            finalMessage += '\n\n' + customMessage;
        }

        const encodedMessage = encodeURIComponent(finalMessage);
        const waUrl = `https://wa.me/${phoneNumber}?text=${encodedMessage}`;

        // Try to open WhatsApp
        let whatsappWindow = window.open(waUrl, '_blank');

        if (!whatsappWindow) {
            const webWhatsappUrl = `https://web.whatsapp.com/send?phone=${phoneNumber}&text=${encodedMessage}`;
            whatsappWindow = window.open(webWhatsappUrl, '_blank');

            if (!whatsappWindow) {
                alert('Please allow popups for this site to open WhatsApp.\n\nAlternatively, copy this number and message manually:\nPhone: +' + phoneNumber + '\nMessage: ' + finalMessage);
                return;
            }
        }

        // Close modal after a short delay
        setTimeout(() => {
            const modal = bootstrap.Modal.getInstance(document.getElementById('transportWhatsappModal'));
            if (modal) {
                modal.hide();
            }
        }, 1000);
    }

    function generateTransportShareLink(transportId) {
        const viewUrl = `${window.location.origin}/entries/transport/${transportId}/print`;
        const pdfUrl = `${window.location.origin}/entries/transport/${transportId}/print-pdf`;

        // Copy view URL to clipboard
        navigator.clipboard.writeText(viewUrl).then(() => {
            alert(`✅ Transport share links generated and copied to clipboard!

🔗 View URL: ${viewUrl}
📎 PDF URL: ${pdfUrl}

💡 You can now:
1. Paste these links anywhere to share
2. Recipients can view transport details online
3. Direct PDF download available
4. Links are always accessible`);
        }).catch(() => {
            alert(`✅ Transport share links generated!

🔗 View URL: ${viewUrl}
📎 PDF URL: ${pdfUrl}

Please copy the URLs manually.`);
        });
    }

    // Export Transport Function
    function exportTransport() {
        // Get current filter values
        const filters = {
            status: document.getElementById('statusFilter')?.value || '',
            date_from: document.getElementById('dateFromFilter')?.value || '',
            date_to: document.getElementById('dateToFilter')?.value || ''
        };

        // Build query string
        const queryParams = new URLSearchParams();
        Object.keys(filters).forEach(key => {
            if (filters[key]) {
                queryParams.append(key, filters[key]);
            }
        });

        // Show loading state
        const exportButton = event.target;
        const originalText = exportButton.innerHTML;
        exportButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Exporting...';
        exportButton.disabled = true;

        // Create export URL
        const exportUrl = `{{ route('entries.transport.export') }}?${queryParams.toString()}`;

        // Use fetch to handle the download properly
        fetch(exportUrl, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // Check if response is JSON (error) or blob (file)
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return response.json().then(data => {
                    throw new Error(data.error || 'Export failed');
                });
            }

            return response.blob();
        })
        .then(blob => {
            // Create download link for the blob
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `transport_entries_${new Date().toISOString().slice(0,19).replace(/:/g, '-')}.xlsx`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);

            // Show success message
            alert('✅ Excel export completed!\n\nThe Excel file has been downloaded with current filter settings.');
        })
        .catch(error => {
            console.error('Export error:', error);
            alert('❌ Export failed!\n\nError: ' + error.message + '\n\nPlease try again or contact support.');
        })
        .finally(() => {
            // Reset button
            exportButton.innerHTML = originalText;
            exportButton.disabled = false;
        });
    }

    // Export Transport to CSV Function (Backup)
    function exportTransportCSV() {
        // Get current filter values
        const filters = {
            status: document.getElementById('statusFilter')?.value || '',
            date_from: document.getElementById('dateFromFilter')?.value || '',
            date_to: document.getElementById('dateToFilter')?.value || ''
        };

        // Build query string
        const queryParams = new URLSearchParams();
        Object.keys(filters).forEach(key => {
            if (filters[key]) {
                queryParams.append(key, filters[key]);
            }
        });

        // Show loading state
        const exportButton = event.target;
        const originalText = exportButton.innerHTML;
        exportButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Exporting...';
        exportButton.disabled = true;

        // Create export URL
        const exportUrl = `{{ route('entries.transport.export.csv') }}?${queryParams.toString()}`;

        // Create download link
        const link = document.createElement('a');
        link.href = exportUrl;
        link.download = '';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Reset button after a delay
        setTimeout(() => {
            exportButton.innerHTML = originalText;
            exportButton.disabled = false;
        }, 3000);

        // Show success message
        setTimeout(() => {
            alert('✅ CSV export completed!\n\nThe CSV file has been downloaded with current filter settings.');
        }, 1000);
    }
</script>
@endpush

<!-- Transport WhatsApp Modal -->
<div class="modal fade" id="transportWhatsappModal" tabindex="-1" aria-labelledby="transportWhatsappModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="transportWhatsappModalLabel">
                    <i class="fab fa-whatsapp me-2"></i>Share Transport via WhatsApp
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="transportPhoneNumber" class="form-label">Phone Number <span class="text-danger">*</span></label>
                            <input type="tel" class="form-control" id="transportPhoneNumber" placeholder="Enter 10-digit phone number" maxlength="15">
                            <small class="form-text text-muted">Enter 10-digit phone number (without country code)</small>
                        </div>

                        <div class="form-group mb-3">
                            <label for="transportCustomMessage" class="form-label">Additional Message (Optional)</label>
                            <textarea class="form-control" id="transportCustomMessage" rows="3" placeholder="Add any additional message..."></textarea>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">Message Preview</label>
                        <div class="border rounded p-3 bg-light" style="height: 200px; overflow-y: auto; white-space: pre-wrap; font-family: monospace; font-size: 0.9rem;" id="transportMessagePreview">
                            <!-- Message preview will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- Transport PDF Sharing Instructions -->
                <div class="alert alert-info">
                    <h6 class="alert-heading"><i class="fas fa-info-circle me-2"></i>Transport PDF Sharing:</h6>
                    <small>
                        The message includes transport document links that open directly in browser for viewing/downloading.
                    </small>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
                <button type="button" class="btn btn-success" onclick="sendTransportWhatsAppMessage()">
                    <i class="fab fa-whatsapp me-1"></i>Send via WhatsApp
                </button>
            </div>
        </div>
    </div>
</div>
