<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transport {{ $transportEntry->transport_number }} - {{ $transportEntry->company->name ?? 'Transport Service' }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .container {
            padding: 2rem 0;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .card-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 2rem;
        }
        
        .company-logo {
            width: 60px;
            height: 60px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
        }
        
        .btn-download {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-block;
            margin: 10px;
        }
        
        .btn-download:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            color: white;
        }
        
        .info-item {
            padding: 0.75rem 0;
            border-bottom: 1px solid #eee;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #666;
            margin-bottom: 0.25rem;
        }
        
        .info-value {
            color: #333;
            font-size: 1.1rem;
        }
        
        .amount-highlight {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: bold;
            font-size: 1.5rem;
        }
        
        .footer-text {
            color: #666;
            font-size: 0.9rem;
            text-align: center;
            margin-top: 2rem;
        }
        
        .transport-icon {
            font-size: 2rem;
            color: #28a745;
        }
        
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.8rem;
        }
        
        .route-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1rem 0;
            border-left: 4px solid #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-10 col-lg-8">
                <div class="card">
                    <div class="card-header text-center">
                        <div class="company-logo mx-auto">
                            @if($transportEntry->company && $transportEntry->company->logo)
                            <img src="{{ asset('storage/' . $transportEntry->company->logo) }}" alt="{{ $transportEntry->company->name }}" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">
                            @else
                            <i class="fas fa-truck transport-icon"></i>
                            @endif
                        </div>
                        <h3 class="mb-0">{{ $transportEntry->company->name ?? 'Transport Service' }}</h3>
                        @if($transportEntry->company)
                        <p class="mb-0 opacity-75">{{ $transportEntry->company->address }}</p>
                        @if($transportEntry->company->phone || $transportEntry->company->email)
                        <p class="mb-0 opacity-75">
                            @if($transportEntry->company->phone){{ $transportEntry->company->phone }}@endif
                            @if($transportEntry->company->phone && $transportEntry->company->email) | @endif
                            @if($transportEntry->company->email){{ $transportEntry->company->email }}@endif
                        </p>
                        @endif
                        @endif
                    </div>
                    
                    <div class="card-body p-4">
                        <div class="text-center mb-4">
                            <h4 class="text-success">
                                <i class="fas fa-shipping-fast me-2"></i>
                                Transport Details
                            </h4>
                            <hr class="w-25 mx-auto">
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-item">
                                    <div class="info-label">Transport Number</div>
                                    <div class="info-value">{{ $transportEntry->transport_number }}</div>
                                </div>
                                
                                <div class="info-item">
                                    <div class="info-label">Customer</div>
                                    <div class="info-value">{{ $transportEntry->customer->name ?? 'Not specified' }}</div>
                                </div>
                                
                                <div class="info-item">
                                    <div class="info-label">Transport Date</div>
                                    <div class="info-value">{{ $transportEntry->transport_date->format('d M Y') }}</div>
                                </div>
                                
                                @if($transportEntry->due_date)
                                <div class="info-item">
                                    <div class="info-label">Due Date</div>
                                    <div class="info-value">{{ $transportEntry->due_date->format('d M Y') }}</div>
                                </div>
                                @endif
                            </div>
                            
                            <div class="col-md-6">
                                @if($transportEntry->vehicle_number)
                                <div class="info-item">
                                    <div class="info-label">Vehicle Number</div>
                                    <div class="info-value">{{ $transportEntry->vehicle_number }}</div>
                                </div>
                                @endif
                                
                                @if($transportEntry->driver_name)
                                <div class="info-item">
                                    <div class="info-label">Driver Name</div>
                                    <div class="info-value">{{ $transportEntry->driver_name }}</div>
                                </div>
                                @endif
                                
                                @if($transportEntry->driver_phone)
                                <div class="info-item">
                                    <div class="info-label">Driver Phone</div>
                                    <div class="info-value">{{ $transportEntry->driver_phone }}</div>
                                </div>
                                @endif
                                
                                @if($transportEntry->total_amount)
                                <div class="info-item">
                                    <div class="info-label">Total Amount</div>
                                    <div class="info-value amount-highlight">₹{{ number_format($transportEntry->total_amount, 2) }}</div>
                                </div>
                                @endif
                            </div>
                        </div>
                        
                        <!-- Route Information -->
                        @if($transportEntry->from_location || $transportEntry->to_location)
                        <div class="route-info">
                            <h6 class="text-success mb-3">
                                <i class="fas fa-route me-2"></i>
                                Route Information
                            </h6>
                            <div class="row">
                                @if($transportEntry->from_location)
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-map-marker-alt text-success me-2"></i>
                                        <div>
                                            <small class="text-muted">From</small>
                                            <div class="fw-bold">{{ $transportEntry->from_location }}</div>
                                        </div>
                                    </div>
                                </div>
                                @endif
                                
                                @if($transportEntry->to_location)
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-flag-checkered text-success me-2"></i>
                                        <div>
                                            <small class="text-muted">To</small>
                                            <div class="fw-bold">{{ $transportEntry->to_location }}</div>
                                        </div>
                                    </div>
                                </div>
                                @endif
                            </div>
                        </div>
                        @endif
                        
                        <!-- Additional Details -->
                        @if($transportEntry->goods_description || $transportEntry->weight || $transportEntry->packages)
                        <div class="row mt-3">
                            @if($transportEntry->goods_description)
                            <div class="col-md-12">
                                <div class="info-item">
                                    <div class="info-label">Goods Description</div>
                                    <div class="info-value">{{ $transportEntry->goods_description }}</div>
                                </div>
                            </div>
                            @endif
                            
                            @if($transportEntry->weight || $transportEntry->packages)
                            <div class="col-md-6">
                                @if($transportEntry->weight)
                                <div class="info-item">
                                    <div class="info-label">Weight</div>
                                    <div class="info-value">{{ $transportEntry->weight }} kg</div>
                                </div>
                                @endif
                            </div>
                            
                            <div class="col-md-6">
                                @if($transportEntry->packages)
                                <div class="info-item">
                                    <div class="info-label">Packages</div>
                                    <div class="info-value">{{ $transportEntry->packages }}</div>
                                </div>
                                @endif
                            </div>
                            @endif
                        </div>
                        @endif
                        
                        <div class="text-center mt-4">
                            <a href="{{ route('entries.transport.print-pdf', $transportEntry->id) }}" 
                               class="btn-download" target="_blank">
                                <i class="fas fa-download me-2"></i>
                                Download PDF
                            </a>
                            
                            <a href="whatsapp://send?text={{ urlencode('🚛 Transport: ' . $transportEntry->transport_number . "\n📅 Date: " . $transportEntry->transport_date->format('d M Y') . "\n🔗 View: " . route('entries.transport.print', $transportEntry->id) . "\n📎 PDF: " . route('entries.transport.print-pdf', $transportEntry->id)) }}" 
                               class="btn-download">
                                <i class="fab fa-whatsapp me-2"></i>
                                Share via WhatsApp
                            </a>
                        </div>
                        
                        <div class="footer-text">
                            <i class="fas fa-truck me-1"></i>
                            Transport service by {{ $transportEntry->company->name ?? 'Transport Service Provider' }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
