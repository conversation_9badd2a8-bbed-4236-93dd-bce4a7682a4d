@extends('layouts.app')

@section('title', 'Edit Quatation Entry - JMD Traders')

@section('content')
<style>
    .compact-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 15px;
    }

    .header-card {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .header-card h3 {
        margin: 0;
        font-size: 1.4rem;
        font-weight: 700;
    }

    .form-card {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .form-section {
        border-bottom: 1px solid #e3e6f0;
        padding: 15px 20px;
    }

    .form-section:last-child {
        border-bottom: none;
    }

    .section-title {
        color: #4e73df;
        font-weight: 700;
        font-size: 0.9rem;
        margin-bottom: 12px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        display: flex;
        align-items: center;
        gap: 6px;
    }

    .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }

    .form-group {
        margin-bottom: 0;
    }

    .form-label {
        display: block;
        font-weight: 600;
        color: #5a5c69;
        font-size: 0.8rem;
        margin-bottom: 4px;
        text-transform: uppercase;
        letter-spacing: 0.3px;
    }

    .form-control, .form-select {
        width: 100%;
        padding: 8px 10px;
        border: 1px solid #e3e6f0;
        border-radius: 4px;
        font-size: 0.85rem;
        background: #f8f9fc;
        transition: all 0.2s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #4e73df;
        background: white;
        outline: none;
        box-shadow: 0 0 0 0.1rem rgba(78, 115, 223, 0.2);
    }

    .btn {
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        font-weight: 600;
        font-size: 0.8rem;
        cursor: pointer;
        transition: all 0.2s ease;
        display: inline-flex;
        align-items: center;
        gap: 6px;
    }

    .btn-primary {
        background: #4e73df;
        color: white;
    }

    .btn-primary:hover {
        background: #224abe;
    }

    .btn-success {
        background: #1cc88a;
        color: white;
    }

    .btn-success:hover {
        background: #17a673;
    }

    .btn-danger {
        background: #e74a3b;
        color: white;
        padding: 4px 8px;
        font-size: 0.7rem;
    }

    .btn-danger:hover {
        background: #c0392b;
    }

    .product-item {
        background: #f8f9fc;
        border: 1px solid #e3e6f0;
        border-radius: 6px;
        padding: 15px;
        margin-bottom: 10px;
        position: relative;
    }

    .product-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid #e3e6f0;
    }

    .product-number {
        background: #4e73df;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .product-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 10px;
        margin-bottom: 10px;
    }

    .tax-section {
        background: white;
        border: 1px solid #e3e6f0;
        border-radius: 4px;
        padding: 10px;
        margin-top: 10px;
    }

    .tax-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 8px;
    }

    .totals-card {
        background: linear-gradient(135deg, #f8f9fc 0%, #e3e6f0 100%);
        border: 2px solid #4e73df;
        border-radius: 8px;
        padding: 20px;
        margin-top: 20px;
    }

    .totals-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
    }

    .total-item {
        display: flex;
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px solid #e3e6f0;
    }

    .total-item:last-child {
        border-bottom: none;
        font-weight: 700;
        font-size: 1.1rem;
        color: #4e73df;
    }

    .action-buttons {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
        padding: 20px;
        background: #f8f9fc;
        border-top: 1px solid #e3e6f0;
    }

    .readonly {
        background: #e9ecef !important;
        color: #6c757d;
    }

    .product-row {
        background: white;
        border-bottom: 1px solid #e3e6f0;
        transition: background-color 0.2s ease;
    }

    .product-row:hover {
        background: #f8f9fc;
    }

    .product-row:nth-child(even) {
        background: #f8f9fc;
    }

    .product-row:nth-child(even):hover {
        background: #e3e6f0;
    }

    #products-table {
        font-size: 0.8rem;
        white-space: nowrap;
    }

    #products-table th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-weight: 600;
        text-align: center;
        padding: 12px 8px;
        border: 1px solid #5a6c7d;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    #products-table td {
        padding: 10px 8px;
        vertical-align: middle;
        border: 1px solid #e3e6f0;
    }

    .table-responsive {
        border: 1px solid #e3e6f0;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .btn-group .btn {
        margin: 0 2px;
    }

    #products-table th {
        text-align: center;
        font-weight: 600;
    }

    @media (max-width: 768px) {
        .form-grid {
            grid-template-columns: 1fr;
        }

        .product-grid {
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        }

        .tax-grid {
            grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
        }

        /* Modal responsive adjustments */
        .modal-container {
            width: 95%;
            max-height: 95vh;
            margin: 10px;
        }

        .modal-header, .modal-body, .modal-footer {
            padding: 15px;
        }

        .modal-footer {
            flex-direction: column;
            gap: 10px;
        }

        .modal-footer .btn {
            width: 100%;
        }
    }

    /* Chrome, Safari, Edge, Opera */
input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}

.table tbody td{
    padding:2px!important;
    vertical-align: middle;
    border-color: #e3e6f0;
    font-size: 0.9rem;
}



    .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }

    .form-group {
        margin-bottom: 0;
    }

    .form-label {
        display: block;
        font-weight: 600;
        color: #5a5c69;
        font-size: 0.8rem;
        margin-bottom: 4px;
        text-transform: uppercase;
        letter-spacing: 0.3px;
    }

    .form-control, .form-select {
        width: 100%;
        padding: 8px 10px;
        border: 1px solid #e3e6f0;
        border-radius: 4px;
        font-size: 0.85rem;
        background: #f8f9fc;
        transition: all 0.2s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #4e73df;
        box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
        outline: none;
    }

    .action-buttons {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
        padding: 20px;
        background: #f8f9fc;
        border-top: 1px solid #e3e6f0;
    }

    .btn-primary {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        font-weight: 600;
        font-size: 0.85rem;
    }

    .btn-secondary {
        background: #6c757d;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        font-weight: 600;
        font-size: 0.85rem;
    }

    .product-card {
        background: white;
        border: 1px solid #e3e6f0;
        border-radius: 8px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .product-card:hover {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        border-color: #4e73df;
    }

    .product-card-header {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        color: white;
        padding: 12px 20px;
        border-radius: 8px 8px 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .product-card-body {
        padding: 20px;
    }

    .product-number {
        font-weight: 700;
        font-size: 1rem;
        letter-spacing: 0.5px;
    }

    .product-card .btn-danger {
        background: #dc3545;
        border: none;
        padding: 6px 12px;
        border-radius: 4px;
        font-size: 0.8rem;
        transition: all 0.2s ease;
    }

    .product-card .btn-danger:hover {
        background: #c82333;
        transform: translateY(-1px);
    }

    .readonly {
        background: #f8f9fc !important;
        color: #6c757d;
        cursor: not-allowed;
        border-color: #e3e6f0 !important;
    }

    .btn-success {
        background: linear-gradient(135deg, #1cc88a 0%, #17a673 100%);
        border: none;
        padding: 10px 20px;
        border-radius: 6px;
        font-weight: 600;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }

    .btn-success:hover {
        background: linear-gradient(135deg, #17a673 0%, #13855c 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(28, 200, 138, 0.3);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .product-card-header {
            flex-direction: column;
            gap: 10px;
            text-align: center;
        }

        .product-card-body .row {
            margin-bottom: 15px;
        }

        .form-label {
            font-size: 0.75rem;
        }

        .form-control, .form-select {
            font-size: 0.8rem;
            padding: 8px 10px;
        }
    }

    @media (max-width: 768px) {
        .form-grid {
            grid-template-columns: 1fr;
        }
    }
</style>

<div class="container-fuild">
    <div class="header-card">
        <h3><i class="fas fa-edit"></i> Edit Quatation Entry</h3>
    </div>

    @if ($errors->any())
        <div class="alert alert-danger">
            <ul class="mb-0">
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <form action="{{ route('entries.sales.update', $salesEntry->id) }}" method="POST" id="editSalesForm">
        @csrf
        @method('PUT')

        <div class="form-card">
            <!-- Invoice Information -->
            <div class="form-section">
                <div class="section-title"> 
                    <i class="fas fa-file-alt"></i> Quatation Information & 
                </div>
                <div class="row">
                    <div class="form-group col-md-2">
                        <label class="form-label">Quatation No <span class="text-danger">*</span></label>
                        <input type="text" name="invoice_no" class="form-control"
                               value="{{ old('invoice_no', $salesEntry->invoice_number ?? $salesEntry->quotation_number) }}" required>
                    </div>
                    <div class="form-group col-md-2">
                        <label class="form-label">Quatation Date <span class="text-danger">*</span></label>
                        <div class="date-input-wrapper">
                            <input type="date" name="invoice_date" class="form-control datepicker"
                                   value="{{ old('invoice_date', $salesEntry->invoice_date ? $salesEntry->invoice_date->format('Y-m-d') : $salesEntry->quotation_date->format('Y-m-d')) }}"
                                   required placeholder="Select invoice date">
                            <i class="fas fa-calendar-alt calendar-icon"></i>
                        </div>
                    </div>
                   
               
           
                    <div class="form-group col-md-2">
                        <label class="form-label">Billed To <span class="text-danger">*</span>  <button type="button" class="btn btn-primary" onclick="openCustomerModal()" title="Add New Customer">
                                <i class="fas fa-plus"></i>
                            </button></label>
                        <select name="billed_to" id="billed_to" class="form-select customer-select" required data-placeholder="Search and select customer">
                            <option value="">Select Customer</option>
                            @foreach($customers as $customer)
                                <option value="{{ $customer->id }}"
                                        {{ old('billed_to', $salesEntry->customer_id) == $customer->id ? 'selected' : '' }}
                                        data-name="{{ $customer->name }}"
                                        data-mobile="{{ $customer->mobile }}"
                                        data-email="{{ $customer->email }}">
                                    {{ $customer->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="form-group col-md-2">
                        <label class="form-label">Party Name</label>
                        <input type="text" name="party_name" id="party_name" class="form-control"
                               value="{{ old('party_name', $salesEntry->party_name) }}">
                    </div>
                    <div class="form-group col-md-2">
                        <label class="form-label">Mobile</label>
                        <input type="text" name="mobile" id="mobile" class="form-control"
                               value="{{ old('mobile', $salesEntry->mobile) }}">
                    </div>
                    <div class="form-group col-md-2">
                        <label class="form-label">Email</label>
                        <input type="email" name="email" id="email" class="form-control"
                               value="{{ old('email', $salesEntry->email) }}">
                    </div>

                     <div class="form-group col-md-2">
                        <label class="form-label">Payment Terms</label>
                        <select name="payment_terms" class="form-select searchable-select" data-placeholder="Select payment terms">
                            <option value="cash" {{ old('payment_terms', $salesEntry->payment_terms) == 'cash' ? 'selected' : '' }}>Cash</option>
                            <option value="credit_7" {{ old('payment_terms', $salesEntry->payment_terms) == 'credit_7' ? 'selected' : '' }}>7 Days Credit</option>
                            <option value="credit_15" {{ old('payment_terms', $salesEntry->payment_terms) == 'credit_15' ? 'selected' : '' }}>15 Days Credit</option>
                            <option value="credit_30" {{ old('payment_terms', $salesEntry->payment_terms) == 'credit_30' ? 'selected' : '' }}>30 Days Credit</option>
                            <option value="credit_45" {{ old('payment_terms', $salesEntry->payment_terms) == 'credit_45' ? 'selected' : '' }}>45 Days Credit</option>
                            <option value="credit_60" {{ old('payment_terms', $salesEntry->payment_terms) == 'credit_60' ? 'selected' : '' }}>60 Days Credit</option>
                        </select>
                    </div>
                    <div class="form-group col-md-2">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select searchable-select" required data-placeholder="Select status">
                            <option value="draft" {{ old('status', $salesEntry->status) == 'draft' ? 'selected' : '' }}>Draft</option>
                            <option value="pending" {{ old('status', $salesEntry->status) == 'pending' ? 'selected' : '' }}>Pending</option>
                            <option value="approved" {{ old('status', $salesEntry->status) == 'approved' ? 'selected' : '' }}>Approved</option>
                            <option value="rejected" {{ old('status', $salesEntry->status) == 'rejected' ? 'selected' : '' }}>Rejected</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Product Entry Form -->
            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-boxes"></i> Product Entry
                </div>

               

                <table width="100%" class="table table-bordered" >
                    <thead>
                        <tr>
                            <th>Product <button type="button" class="btn btn-success btn-sm" onclick="openProductModal()" title="Add New Product" style="font-size: 14px; padding: 12px 24px; font-weight: 600; box-shadow: 0 2px 4px rgba(28, 200, 138, 0.3);">
                            <i class="fas fa-plus"></i>
                        </button></th>
                            <th>GST %</th>
                            <th>Unit</th>
                            <th>Pkgs</th>
                            <th>Rate</th>
                            <th>Loading</th>
                            <th>Gauge Diff.</th>
                            <th>Basic Rate</th>
                            <th>Amount</th>
                            <th>Taxable Value</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                 <select id="product_select" class="form-select myselect" onchange="loadProductDetails()" data-placeholder="Search and select product">
                                    <option value="">Select Product</option>
                                    @foreach($products as $product)
                                        <option value="{{ $product->id }}"
                                                data-name="{{ $product->name }}"
                                                data-hsn="{{ $product->hsn_code }}"
                                                data-gst="{{ $product->gst_percentage }}"
                                                data-unit="{{ $product->unit }}"
                                                data-rate="{{ $product->rate }}"
                                                data-gauge="{{ $product->gauge_diff }}">

                                            {{ $product->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </td>

                            <td>
                            <input type="number" id="gst_percent" class="form-control readonly" step="0.01" readonly>

                            </td>
                            <td>
                                <input type="text" id="unit" class="form-control readonly" readonly>
                            </td>
                            <td>
                                <input type="number" id="packages" class="form-control" value="1" step="1" min="0" onchange="calculateEntry()" disabled>
                            </td>
                            <td>
                                <input type="number" id="rate" class="form-control" step="0.01" min="0" onchange="calculateEntry()" disabled>
                            </td>
                            <td>
                                <input type="number" id="loading" class="form-control" step="0.01" min="0" value="263" onchange="calculateEntry()" disabled>
                            </td>
                            <td>
                                <input type="number" id="gauge_diff" class="form-control" step="0.01" min="0" onchange="calculateEntry()" value="0.00">
                            </td>
                            <td>
                                <input type="number" id="basic_rate" class="form-control readonly" step="0.01" readonly>
                            </td>
                            <td>
                                <input type="number" id="total" class="form-control readonly" step="0.01" readonly>
                            </td>
                            <td>
                                <input type="number" id="taxable_value" class="form-control readonly" step="0.01" readonly>
                            </td>
                            <td>
                                <button type="button" class="btn btn-success" onclick="addToTable()" style="width: 100%;">
                                    <i class="fas fa-plus"></i> Add
                                </button>
                            </td>


                        </tr>

                    </tbody>
                </table>

                <!-- Single Product Entry Form -->
                <div class="product-entry-form">


                    <div class="row" style="margin-top: 10px;">
                        <div class="col-md-4">
                            <label class="form-label">Description</label>
                            <input type="text" id="description" class="form-control" placeholder="Product description" disabled>
                        </div>
                         <div class="col-md-1">
                            <label class="form-label">HSN Code</label>
                            <input type="text" id="hsn_code" class="form-control readonly" readonly>
                        </div>
                          <div class="col-md-1">
                            <label class="form-label">Gauge AMT</label>
                           <input type="text" id="gauge" class="form-control readonly" readonly>

                        </div>
                        <div class="col-md-1">
                            <label class="form-label">CGST %</label>
                            <input type="number" id="cgst_percent" class="form-control readonly" step="0.01" readonly>
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">CGST Amount</label>
                            <input type="number" id="cgst_amount" class="form-control readonly" step="0.01" readonly>
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">SGST %</label>
                            <input type="number" id="sgst_percent" class="form-control readonly" step="0.01" readonly>
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">SGST Amount</label>
                            <input type="number" id="sgst_amount" class="form-control readonly" step="0.01" readonly>
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">IGST %</label>
                            <input type="number" id="igst_percent" class="form-control readonly" step="0.01" readonly>
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">IGST Amount</label>
                            <input type="number" id="igst_amount" class="form-control readonly" step="0.01" readonly>
                        </div>
                        <div class="col-md-1" style="display: flex; align-items: end;">

                        </div>
                    </div>
                </div>
            </div>

            <!-- Current Sales Entry Items -->
            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-list"></i> Current Sales Entry Items
                </div>

                <div class="table-responsive" style="max-height: 400px; overflow-x: auto;">
                    <table class="table table-bordered table-sm table-striped" style="min-width: 1800px;">
                        <thead class="table-primary sticky-top">
                            <tr>
                                <th style="width: 40px; min-width: 40px;">#</th>
                                <th style="width: 200px; min-width: 200px;">Product</th>
                                <th style="width: 100px; min-width: 100px;">HSN Code</th>
                                <th style="width: 80px; min-width: 80px;">GST%</th>
                                <th style="width: 80px; min-width: 80px;">Pkgs</th>
                                <th style="width: 80px; min-width: 80px;">Unit</th>
                                <th style="width: 100px; min-width: 100px;">Rate</th>
                                <th style="width: 100px; min-width: 100px;">Loading</th>
                                <th style="width: 120px; min-width: 120px;">Gauge Diff.</th>
                                <th style="width: 120px; min-width: 120px;">Basic Rate</th>
                                <th style="width: 120px; min-width: 120px;">Total</th>
                                <th style="width: 120px; min-width: 120px;">Taxable Value</th>
                                <th style="width: 80px; min-width: 80px;">CGST%</th>
                                <th style="width: 100px; min-width: 100px;">CGST Amt</th>
                                <th style="width: 80px; min-width: 80px;">SGST%</th>
                                <th style="width: 100px; min-width: 100px;">SGST Amt</th>
                                <th style="width: 80px; min-width: 80px;">IGST%</th>
                                <th style="width: 100px; min-width: 100px;">IGST Amt</th>
                                <th style="width: 150px; min-width: 150px;">Description</th>
                                <th style="width: 120px; min-width: 120px;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($salesEntry->items as $index => $item)
                            <tr class="product-row">
                                <td class="text-center">
                                    <span class="badge bg-primary">{{ $index + 1 }}</span>
                                </td>
                                <td>
                                    <strong>{{ $item->product->name ?? 'N/A' }}</strong>
                                    <input type="hidden" name="products[{{ $index }}][product_id]" value="{{ $item->product_id }}">
                                </td>
                                <td class="text-center">
                                    {{ $item->hsn_code }}
                                    <input type="hidden" name="products[{{ $index }}][hsn_code]" value="{{ $item->hsn_code }}">
                                </td>
                                <td class="text-center">
                                    {{ $item->gst_percent }}%
                                    <input type="hidden" name="products[{{ $index }}][gst_percent]" value="{{ $item->gst_percent }}">
                                </td>
                                <td class="text-center">
                                    <input type="number" name="products[{{ $index }}][packages]" value="{{ $item->packages ?? $item->quantity ?? 1 }}" class="form-control" style="width: 70px; padding: 4px; background: white !important; border: 1px solid #ddd !important; color: #333 !important; font-size: 14px;" onchange="calculateItemTotals({{ $index }})" min="0">
                                </td>
                                <td class="text-center">
                                    {{ $item->unit }}
                                    <input type="hidden" name="products[{{ $index }}][unit]" value="{{ $item->unit }}">
                                </td>
                                <td class="text-end">
                                    <input type="number" name="products[{{ $index }}][rate]" value="{{ $item->rate ?? $item->unit_price ?? 0 }}" step="0.01" class="form-control" style="width: 90px; padding: 4px; background: white !important; border: 1px solid #ddd !important; color: #333 !important; font-size: 14px;" onchange="calculateItemTotals({{ $index }})" min="0">
                                </td>
                                <td class="text-end">
                                    <input type="number" name="products[{{ $index }}][loading]" value="{{ $item->loading ?? 263 }}" step="0.01" class="form-control" style="width: 90px; padding: 4px; background: white !important; border: 1px solid #ddd !important; color: #333 !important; font-size: 14px;" onchange="calculateItemTotals({{ $index }})" min="0">
                                </td>
                                <td class="text-end">
                                    <input type="number" name="products[{{ $index }}][gauge_diff]" value="{{ $item->gauge_diff ?? 0 }}" step="0.01" class="form-control" style="width: 90px; padding: 4px; background: white !important; border: 1px solid #ddd !important; color: #333 !important; font-size: 14px;" onchange="calculateItemTotals({{ $index }})">
                                </td>
                                <td class="text-end">
                                    <span id="basic_rate_{{ $index }}">₹{{ number_format($item->basic_rate ?? (($item->rate ?? 0) + ($item->loading ?? 0)), 2) }}</span>
                                    <input type="hidden" name="products[{{ $index }}][basic_rate]" id="basic_rate_hidden_{{ $index }}" value="{{ $item->basic_rate ?? (($item->rate ?? 0) + ($item->loading ?? 0)) }}">
                                </td>
                                <td class="text-end">
                                    <span id="total_{{ $index }}">₹{{ number_format($item->total ?? $item->line_total ?? 0, 2) }}</span>
                                    <input type="hidden" name="products[{{ $index }}][total]" id="total_hidden_{{ $index }}" value="{{ $item->total ?? $item->line_total ?? 0 }}">
                                </td>
                                <td class="text-end">
                                    <span id="taxable_value_{{ $index }}">₹{{ number_format($item->taxable_value ?? 0, 2) }}</span>
                                    <input type="hidden" name="products[{{ $index }}][taxable_value]" id="taxable_value_hidden_{{ $index }}" value="{{ $item->taxable_value ?? 0 }}">
                                </td>
                                <td class="text-center">
                                    <span id="cgst_percent_{{ $index }}">{{ number_format($item->cgst_percent ?? 0, 2) }}%</span>
                                    <input type="hidden" name="products[{{ $index }}][cgst_percent]" id="cgst_percent_hidden_{{ $index }}" value="{{ $item->cgst_percent ?? 0 }}">
                                </td>
                                <td class="text-end">
                                    <span id="cgst_amount_{{ $index }}">₹{{ number_format($item->cgst_amount ?? 0, 2) }}</span>
                                    <input type="hidden" name="products[{{ $index }}][cgst_amount]" id="cgst_amount_hidden_{{ $index }}" value="{{ $item->cgst_amount ?? 0 }}">
                                </td>
                                <td class="text-center">
                                    <span id="sgst_percent_{{ $index }}">{{ number_format($item->sgst_percent ?? 0, 2) }}%</span>
                                    <input type="hidden" name="products[{{ $index }}][sgst_percent]" id="sgst_percent_hidden_{{ $index }}" value="{{ $item->sgst_percent ?? 0 }}">
                                </td>
                                <td class="text-end">
                                    <span id="sgst_amount_{{ $index }}">₹{{ number_format($item->sgst_amount ?? 0, 2) }}</span>
                                    <input type="hidden" name="products[{{ $index }}][sgst_amount]" id="sgst_amount_hidden_{{ $index }}" value="{{ $item->sgst_amount ?? 0 }}">
                                </td>
                                <td class="text-center">
                                    <span id="igst_percent_{{ $index }}">{{ number_format($item->igst_percent ?? 0, 2) }}%</span>
                                    <input type="hidden" name="products[{{ $index }}][igst_percent]" id="igst_percent_hidden_{{ $index }}" value="{{ $item->igst_percent ?? 0 }}">
                                </td>
                                <td class="text-end">
                                    <span id="igst_amount_{{ $index }}">₹{{ number_format($item->igst_amount ?? 0, 2) }}</span>
                                    <input type="hidden" name="products[{{ $index }}][igst_amount]" id="igst_amount_hidden_{{ $index }}" value="{{ $item->igst_amount ?? 0 }}">
                                </td>
                                <td class="text-truncate" style="max-width: 150px;" title="{{ $item->description ?? '-' }}">
                                    <input type="text" name="products[{{ $index }}][description]" value="{{ $item->description }}" class="form-control" style="width: 140px; padding: 4px;" placeholder="Description...">
                                </td>
                                <td class="text-center">
                                    <button type="button" class="btn btn-danger btn-sm" onclick="removeExistingItem({{ $index }})" title="Remove Item">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Products Table -->
            <div class="form-section" id="products-table-section" style="display: none;">
                <div class="section-title">
                    <i class="fas fa-list"></i> Added Products
                </div>

                <div class="table-responsive" style="max-height: 400px; overflow-x: auto;">
                    <table class="table table-bordered table-sm table-striped" id="products-table" style="min-width: 1800px;">
                        <thead class="table-primary sticky-top">
                            <tr>
                                <th style="width: 40px; min-width: 40px;">#</th>
                                <th style="width: 200px; min-width: 200px;">Product</th>
                                <th style="width: 100px; min-width: 100px;">HSN Code</th>
                                <th style="width: 80px; min-width: 80px;">GST%</th>
                                <th style="width: 80px; min-width: 80px;">Pkgs</th>
                                <th style="width: 80px; min-width: 80px;">Unit</th>
                                <th style="width: 100px; min-width: 100px;">Rate</th>
                                <th style="width: 100px; min-width: 100px;">Loading</th>
                                <th style="width: 120px; min-width: 120px;">Gauge Diff.</th>
                                <th style="width: 120px; min-width: 120px;">Basic Rate</th>
                                <th style="width: 120px; min-width: 120px;">Total</th>
                                <th style="width: 120px; min-width: 120px;">Taxable Value</th>
                                <th style="width: 80px; min-width: 80px;">CGST%</th>
                                <th style="width: 100px; min-width: 100px;">CGST Amt</th>
                                <th style="width: 80px; min-width: 80px;">SGST%</th>
                                <th style="width: 100px; min-width: 100px;">SGST Amt</th>
                                <th style="width: 80px; min-width: 80px;">IGST%</th>
                                <th style="width: 100px; min-width: 100px;">IGST Amt</th>
                                <th style="width: 150px; min-width: 150px;">Description</th>
                                <th style="width: 120px; min-width: 120px;">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="products-table-body">
                            <!-- Added products will appear here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Additional Totals Section -->
            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-calculator"></i> Additional Charges & Totals
                </div>

                <!-- First Row: Basic Totals -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <label class="form-label">Total</label>
                        <div class="form-control readonly" id="subtotal">₹0.00</div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Ins. P/MT</label>
                        <input type="number" class="form-control" id="ins_pmt" name="ins_pmt" step="0.01" value="{{ old('ins_pmt', $salesEntry->ins_pmt ?? 0) }}" onchange="calculateFinalTotals()">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Insurance</label>
                        <input type="number" class="form-control" id="insurance" name="insurance" step="0.01" value="{{ old('insurance', $salesEntry->insurance ?? 0) }}" onchange="calculateFinalTotals()">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Frt. Advance</label>
                        <input type="number" class="form-control" id="frt_advance" name="frt_advance" step="0.01" value="{{ old('frt_advance', $salesEntry->frt_advance ?? 0) }}" onchange="calculateFinalTotals()">
                    </div>
                </div>

                <!-- Second Row: Tax and Final Totals -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <label class="form-label">Grand Total</label>
                        <div class="form-control readonly" id="grand_total">₹0.00</div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">TCS %</label>
                        <input type="number" class="form-control" id="tcs_percent" name="tcs_percent" step="0.01" value="{{ old('tcs_percent', $salesEntry->tcs_percent ?? 0) }}" onchange="calculateFinalTotals()">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">TCS Amount</label>
                        <div class="form-control readonly" id="tcs_amount">₹0.00</div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Net Amount</label>
                        <div class="form-control readonly" id="net_amount" style="background: linear-gradient(135deg, #1cc88a 0%, #17a673 100%); color: white; font-weight: bold;">₹0.00</div>
                    </div>
                </div>

                <!-- Tax Details Section -->
                <div class="row">
                    <div class="col-md-4">
                        <label class="form-label">Total CGST</label>
                        <div class="form-control readonly" id="total_cgst">₹0.00</div>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Total SGST</label>
                        <div class="form-control readonly" id="total_sgst">₹0.00</div>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Total IGST</label>
                        <div class="form-control readonly" id="total_igst">₹0.00</div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <a href="{{ route('entries.sales.show', $salesEntry->id) }}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> Cancel
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Update Sales Entry
                </button>
            </div>
        </div>
    </form>
</div>

<script>
let productCounter = 0;
let addedProducts = [];
let editingIndex = -1;

function loadCustomerDetails(customerId) {
    const select = document.getElementById('billed_to');
    const option = select.options[select.selectedIndex];

    if (option && option.value) {
        document.getElementById('party_name').value = option.dataset.name || '';
        document.getElementById('mobile').value = option.dataset.mobile || '';
        document.getElementById('email').value = option.dataset.email || '';
    } else {
        document.getElementById('party_name').value = '';
        document.getElementById('mobile').value = '';
        document.getElementById('email').value = '';
    }
}

function loadProductDetails() {
    const select = document.getElementById('product_select');
    const option = select.options[select.selectedIndex];
console.log(select);
    console.log('Loading product details:', option);

    if (option && option.value) {
        // Debug: Log all data attributes
        console.log('Product data:', {
            hsn: option.dataset.hsn,
            gst: option.dataset.gst,
            unit: option.dataset.unit,
            rate: option.dataset.rate,
            gauge: option.dataset.gauge

        });

        // Auto-fill product details
        document.getElementById('hsn_code').value = option.dataset.hsn || '';
        document.getElementById('gst_percent').value = option.dataset.gst || '';
        document.getElementById('unit').value = option.dataset.unit || '';
        document.getElementById('rate').value = option.dataset.rate || '';
        document.getElementById('gauge').value = option.dataset.gauge || '0';

        // Enable editable fields
        document.getElementById('packages').disabled = false;
        document.getElementById('rate').disabled = false;
        document.getElementById('loading').disabled = false;
        document.getElementById('description').disabled = false;
        document.getElementById('gauge_diff').disabled = true;

        // Calculate immediately after loading
        calculateEntry();
    } else {
        clearEntryForm();

        // Disable editable fields when no product selected
        document.getElementById('packages').disabled = true;
        document.getElementById('rate').disabled = true;
        document.getElementById('loading').disabled = true;
        document.getElementById('description').disabled = true;
    }
}

function clearEntryForm() {
    document.getElementById('product_select').value = '';
    document.getElementById('hsn_code').value = '';
    document.getElementById('gst_percent').value = '';
    document.getElementById('unit').value = '';
    document.getElementById('packages').value = '1';
    document.getElementById('rate').value = '';
    document.getElementById('loading').value = '263';
    document.getElementById('gauge_diff').value = '0';
    document.getElementById('gauge').value = '0';
    document.getElementById('basic_rate').value = '';
    document.getElementById('total').value = '';
    document.getElementById('description').value = '';
    document.getElementById('taxable_value').value = '';
    document.getElementById('cgst_percent').value = '';
    document.getElementById('cgst_amount').value = '';
    document.getElementById('sgst_percent').value = '';
    document.getElementById('sgst_amount').value = '';
    document.getElementById('igst_percent').value = '';
    document.getElementById('igst_amount').value = '';
    editingIndex = -1;

    // Disable fields
    document.getElementById('packages').disabled = true;
    document.getElementById('rate').disabled = true;
    document.getElementById('loading').disabled = true;
    document.getElementById('description').disabled = true;
}

function calculateEntry() {
    const packages = parseFloat(document.getElementById('packages').value) || 0;
    const rate = parseFloat(document.getElementById('rate').value) || 0;
    const loading = parseFloat(document.getElementById('loading').value) || 0;
    const gstPercent = parseFloat(document.getElementById('gst_percent').value) || 0;
    const gaugeAmount = parseFloat(document.getElementById('gauge').value) || 0;
    const gaugeDiffInput = parseFloat(document.getElementById('gauge_diff').value) || 0;

    // Calculate basic values
    const basicRate = rate + loading;

    // Calculate gauge difference value (packages * gauge_diff input)
    const gaugeDiffValue = packages * gaugeAmount;

    // Calculate total: (packages * basic_rate) + gauge_diff_value
    const total = (packages * basicRate) + gaugeDiffValue;
    const taxableValue = total;

    console.log('Calculation Details:', {
        packages: packages,
        rate: rate,
        loading: loading,
        basicRate: basicRate,
        gaugeAmount: gaugeAmount,
        gaugeDiffInput: gaugeDiffInput,
        gaugeDiffValue: gaugeDiffValue,
        total: total
    });
    // Calculate GST breakdown (CGST + SGST for intra-state, IGST for inter-state)
    const cgstPercent = gstPercent / 2;
    const sgstPercent = gstPercent / 2;
    const igstPercent = gstPercent;
alert(gaugeDiffValue);
    // Calculate tax amounts
    const cgstAmount = (taxableValue * cgstPercent) / 100;
    const sgstAmount = (taxableValue * sgstPercent) / 100;
    const igstAmount = (taxableValue * igstPercent) / 100;

    // Update all calculated fields
    document.getElementById('basic_rate').value = basicRate > 0 ? basicRate.toFixed(2) : '';
    document.getElementById('total').value = total > 0 ? total.toFixed(2) : '';
    document.getElementById('taxable_value').value = taxableValue > 0 ? taxableValue.toFixed(2) : '';

    // Update GST fields - show even if 0
    document.getElementById('cgst_percent').value = cgstPercent.toFixed(2);
    document.getElementById('gauge_diff').value = gaugeDiffValue;

    document.getElementById('cgst_amount').value = cgstAmount.toFixed(2);
    document.getElementById('sgst_percent').value = sgstPercent.toFixed(2);
    document.getElementById('sgst_amount').value = sgstAmount.toFixed(2);
    document.getElementById('igst_percent').value = igstPercent.toFixed(2);
    document.getElementById('igst_amount').value = igstAmount.toFixed(2);

    console.log('GST Calculation:', {
        gstPercent: gstPercent,
        cgstPercent: cgstPercent,
        sgstPercent: sgstPercent,
        taxableValue: taxableValue,
        cgstAmount: cgstAmount,
        sgstAmount: sgstAmount
    });
}

function addToTable() {
    const productSelect = document.getElementById('product_select');
    const productOption = productSelect.options[productSelect.selectedIndex];

    if (!productOption || !productOption.value) {
        alert('Please select a product');
        return;
    }

    if (!document.getElementById('packages').value || !document.getElementById('rate').value) {
        alert('Please enter packages and rate');
        return;
    }

    const productData = {
        product_id: productOption.value,
        product_name: productOption.dataset.name,
        hsn_code: document.getElementById('hsn_code').value,
        gst_percent: document.getElementById('gst_percent').value,
        unit: document.getElementById('unit').value,
        packages: document.getElementById('packages').value,
        rate: document.getElementById('rate').value,
        loading: document.getElementById('loading').value,
        gauge_diff: document.getElementById('gauge_diff').value,
        gauge_amount: document.getElementById('gauge').value,
        basic_rate: document.getElementById('basic_rate').value,
        total: document.getElementById('total').value,
        description: document.getElementById('description').value,
        taxable_value: document.getElementById('taxable_value').value,
        cgst_percent: document.getElementById('cgst_percent').value,
        cgst_amount: document.getElementById('cgst_amount').value,
        sgst_percent: document.getElementById('sgst_percent').value,
        sgst_amount: document.getElementById('sgst_amount').value,
        igst_percent: document.getElementById('igst_percent').value,
        igst_amount: document.getElementById('igst_amount').value
    };

    if (editingIndex >= 0) {
        // Update existing product
        addedProducts[editingIndex] = productData;
        console.log('Product updated successfully');
    } else {
        // Add new product
        addedProducts.push(productData);
        console.log('New product added successfully');
    }

    updateProductsTable();
    clearEntryForm();

    // Update hidden inputs after adding product
    setTimeout(() => {
        updateHiddenInputs();
    }, 100);
}

function updateProductsTable() {
    const tableBody = document.getElementById('products-table-body');
    const tableSection = document.getElementById('products-table-section');

    if (addedProducts.length === 0) {
        tableSection.style.display = 'none';
        return;
    }

    tableSection.style.display = 'block';

    let html = '';
    addedProducts.forEach((product, index) => {
        html += `
            <tr class="product-row">
                <td class="text-center">
                    <span class="badge bg-primary">${index + 1}</span>
                </td>
                <td>
                    <strong>${product.product_name}</strong>
                </td>
                <td class="text-center">${product.hsn_code}</td>
                <td class="text-center">${product.gst_percent}%</td>
                <td class="text-center">${product.packages}</td>
                <td class="text-center">${product.unit}</td>
                <td class="text-end">₹${parseFloat(product.rate).toFixed(2)}</td>
                <td class="text-end">₹${parseFloat(product.loading).toFixed(2)}</td>
                <td class="text-end">
                    ${parseFloat(product.gauge_diff || 0).toFixed(2)}
                    ${product.gauge_amount ? `<br><small class="text-muted">Amt: ₹${(parseFloat(product.packages) * parseFloat(product.gauge_diff || 0)).toFixed(2)}</small>` : ''}
                </td>
                <td class="text-end">₹${parseFloat(product.basic_rate).toFixed(2)}</td>
                <td class="text-end">₹${parseFloat(product.total).toFixed(2)}</td>
                <td class="text-end">₹${parseFloat(product.taxable_value).toFixed(2)}</td>
                <td class="text-center">${parseFloat(product.cgst_percent).toFixed(2)}%</td>
                <td class="text-end">₹${parseFloat(product.cgst_amount).toFixed(2)}</td>
                <td class="text-center">${parseFloat(product.sgst_percent).toFixed(2)}%</td>
                <td class="text-end">₹${parseFloat(product.sgst_amount).toFixed(2)}</td>
                <td class="text-center">${parseFloat(product.igst_percent).toFixed(2)}%</td>
                <td class="text-end">₹${parseFloat(product.igst_amount).toFixed(2)}</td>
                <td class="text-truncate" style="max-width: 150px;" title="${product.description || '-'}">
                    ${product.description || '-'}
                </td>
                <td class="text-center">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-primary btn-sm" onclick="editProduct(${index})" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-danger btn-sm" onclick="deleteProduct(${index})" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });

    tableBody.innerHTML = html;
    calculateTotals();
    updateHiddenInputs();
}

function editProduct(index) {
    const product = addedProducts[index];
    editingIndex = index;

    // Populate form with product data
    document.getElementById('product_select').value = product.product_id;
    document.getElementById('hsn_code').value = product.hsn_code;
    document.getElementById('gst_percent').value = product.gst_percent;
    document.getElementById('unit').value = product.unit;
    document.getElementById('packages').value = product.packages;
    document.getElementById('rate').value = product.rate;
    document.getElementById('loading').value = product.loading;
    document.getElementById('gauge_diff').value = product.gauge_diff || 0;
    document.getElementById('gauge').value = product.gauge_amount || 0;
    document.getElementById('basic_rate').value = product.basic_rate;
    document.getElementById('total').value = product.total;
    document.getElementById('description').value = product.description;
    document.getElementById('taxable_value').value = product.taxable_value;
    document.getElementById('cgst_percent').value = product.cgst_percent;
    document.getElementById('cgst_amount').value = product.cgst_amount;
    document.getElementById('sgst_percent').value = product.sgst_percent;
    document.getElementById('sgst_amount').value = product.sgst_amount;
    document.getElementById('igst_percent').value = product.igst_percent;
    document.getElementById('igst_amount').value = product.igst_amount;

    // Enable fields for editing
    document.getElementById('packages').disabled = false;
    document.getElementById('rate').disabled = false;
    document.getElementById('loading').disabled = false;
    document.getElementById('description').disabled = false;
    document.getElementById('gauge_diff').disabled = true;

    // Scroll to form
    document.querySelector('.product-entry-form').scrollIntoView({ behavior: 'smooth' });
}

function deleteProduct(index) {
    if (confirm('Are you sure you want to delete this product?')) {
        addedProducts.splice(index, 1);
        updateProductsTable();
    }
}

function updateHiddenInputs() {
    // Remove only the dynamically added product inputs (not the existing ones from database)
    document.querySelectorAll('input[name^="new_products["]').forEach(input => input.remove());
    document.querySelectorAll('input[name="ins_pmt_hidden"]').forEach(input => input.remove());
    document.querySelectorAll('input[name="insurance_hidden"]').forEach(input => input.remove());
    document.querySelectorAll('input[name="frt_advance_hidden"]').forEach(input => input.remove());
    document.querySelectorAll('input[name="tcs_percent_hidden"]').forEach(input => input.remove());
    document.querySelectorAll('input[name="subtotal_hidden"]').forEach(input => input.remove());
    document.querySelectorAll('input[name="grand_total_hidden"]').forEach(input => input.remove());
    document.querySelectorAll('input[name="tcs_amount_hidden"]').forEach(input => input.remove());
    document.querySelectorAll('input[name="net_amount_hidden"]').forEach(input => input.remove());
    document.querySelectorAll('input[name="total_cgst_hidden"]').forEach(input => input.remove());
    document.querySelectorAll('input[name="total_sgst_hidden"]').forEach(input => input.remove());
    document.querySelectorAll('input[name="total_igst_hidden"]').forEach(input => input.remove());

    // Add hidden inputs for newly added products (use different name to avoid conflict)
    const form = document.getElementById('editSalesForm');
    addedProducts.forEach((product, index) => {
        Object.keys(product).forEach(key => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = `new_products[${index}][${key}]`;
            input.value = product[key];
            form.appendChild(input);
        });
    });

    // Add hidden inputs for additional charges and totals
    const additionalFields = [
        { name: 'ins_pmt_hidden', elementId: 'ins_pmt' },
        { name: 'insurance_hidden', elementId: 'insurance' },
        { name: 'frt_advance_hidden', elementId: 'frt_advance' },
        { name: 'tcs_percent_hidden', elementId: 'tcs_percent' },
        { name: 'subtotal_hidden', elementId: 'subtotal' },
        { name: 'grand_total_hidden', elementId: 'grand_total' },
        { name: 'tcs_amount_hidden', elementId: 'tcs_amount' },
        { name: 'net_amount_hidden', elementId: 'net_amount' },
        { name: 'total_cgst_hidden', elementId: 'total_cgst' },
        { name: 'total_sgst_hidden', elementId: 'total_sgst' },
        { name: 'total_igst_hidden', elementId: 'total_igst' }
    ];

    additionalFields.forEach(field => {
        const element = document.getElementById(field.elementId);
        if (element) {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = field.name;

            // For input elements, get value; for span elements, get textContent without ₹
            if (element.tagName === 'INPUT') {
                input.value = element.value || '0';
            } else {
                input.value = element.textContent.replace('₹', '').replace(',', '') || '0';
            }

            form.appendChild(input);
        }
    });

    console.log('Hidden inputs updated for form submission');
}

function calculateTotals() {
    let subtotal = 0;
    let totalCgst = 0;
    let totalSgst = 0;
    let totalIgst = 0;

    addedProducts.forEach(product => {
        subtotal += parseFloat(product.total) || 0;
        totalCgst += parseFloat(product.cgst_amount) || 0;
        totalSgst += parseFloat(product.sgst_amount) || 0;
        totalIgst += parseFloat(product.igst_amount) || 0;
    });

    // Get additional charges - with fallback for undefined elements
    const insPmtElement = document.getElementById('ins_pmt');
    const insuranceElement = document.getElementById('insurance');
    const frtAdvanceElement = document.getElementById('frt_advance');
    const tcsPercentElement = document.getElementById('tcs_percent');

    const insPmt = insPmtElement ? (parseFloat(insPmtElement.value) || 0) : 0;
    const insurance = insuranceElement ? (parseFloat(insuranceElement.value) || 0) : 0;
    const frtAdvance = frtAdvanceElement ? (parseFloat(frtAdvanceElement.value) || 0) : 0;
    const tcsPercent = tcsPercentElement ? (parseFloat(tcsPercentElement.value) || 0) : 0;

    // Calculate grand total (subtotal + taxes + additional charges)
    const grandTotal = subtotal + totalCgst + totalSgst + totalIgst + insPmt + insurance + frtAdvance;

    // Calculate TCS amount
    const tcsAmount = (grandTotal * tcsPercent) / 100;

    // Calculate net amount
    const netAmount = grandTotal + tcsAmount;

    // Update display - with element existence checks
    const subtotalElement = document.getElementById('subtotal');
    const totalCgstElement = document.getElementById('total_cgst');
    const totalSgstElement = document.getElementById('total_sgst');
    const totalIgstElement = document.getElementById('total_igst');
    const grandTotalElement = document.getElementById('grand_total');
    const tcsAmountElement = document.getElementById('tcs_amount');
    const netAmountElement = document.getElementById('net_amount');

    if (subtotalElement) subtotalElement.textContent = `₹${subtotal.toFixed(2)}`;
    if (totalCgstElement) totalCgstElement.textContent = `₹${totalCgst.toFixed(2)}`;
    if (totalSgstElement) totalSgstElement.textContent = `₹${totalSgst.toFixed(2)}`;
    if (totalIgstElement) totalIgstElement.textContent = `₹${totalIgst.toFixed(2)}`;
    if (grandTotalElement) grandTotalElement.textContent = `₹${grandTotal.toFixed(2)}`;
    if (tcsAmountElement) tcsAmountElement.textContent = `₹${tcsAmount.toFixed(2)}`;
    if (netAmountElement) netAmountElement.textContent = `₹${netAmount.toFixed(2)}`;

    // Debug console log
    console.log('Calculations updated:', {
        subtotal: subtotal,
        totalCgst: totalCgst,
        totalSgst: totalSgst,
        totalIgst: totalIgst,
        insPmt: insPmt,
        insurance: insurance,
        frtAdvance: frtAdvance,
        grandTotal: grandTotal,
        tcsPercent: tcsPercent,
        tcsAmount: tcsAmount,
        netAmount: netAmount
    });
}

// Function for additional charges calculation
function calculateFinalTotals() {
    calculateTotals(); // Call the main calculation function

    // Update hidden inputs after calculation
    setTimeout(() => {
        updateHiddenInputs();
    }, 50);
}

// Calculate totals for existing items
function calculateItemTotals(index) {
    // Get input elements with error checking
    const packagesEl = document.querySelector(`input[name="products[${index}][packages]"]`);
    const rateEl = document.querySelector(`input[name="products[${index}][rate]"]`);
    const loadingEl = document.querySelector(`input[name="products[${index}][loading]"]`);
    const gaugeDiffEl = document.querySelector(`input[name="products[${index}][gauge_diff]"]`);
    const gstPercentEl = document.querySelector(`input[name="products[${index}][gst_percent]"]`);

    if (!packagesEl || !rateEl || !loadingEl || !gaugeDiffEl) {
        console.error(`Missing input elements for item ${index}`);
        return;
    }

    const packages = parseFloat(packagesEl.value) || 0;
    const rate = parseFloat(rateEl.value) || 0;
    const loading = parseFloat(loadingEl.value) || 0;
    const gaugeDiff = parseFloat(gaugeDiffEl.value) || 0;
    const gstPercent = gstPercentEl ? parseFloat(gstPercentEl.value) || 0 : 0;

    console.log(`Item ${index} values:`, {
        packages: packages,
        rate: rate,
        loading: loading,
        gaugeDiff: gaugeDiff,
        gstPercent: gstPercent
    });

    // Calculate basic rate and total
    const basicRate = rate + loading;
    const gaugeDiffValue = packages * gaugeDiff;
    const total = packages * basicRate + gaugeDiffValue;
    const taxableValue = total;

    // Update displays with error checking
    const basicRateEl = document.getElementById(`basic_rate_${index}`);
    const basicRateHiddenEl = document.getElementById(`basic_rate_hidden_${index}`);
    const totalEl = document.getElementById(`total_${index}`);
    const totalHiddenEl = document.getElementById(`total_hidden_${index}`);
    const taxableValueEl = document.getElementById(`taxable_value_${index}`);
    const taxableValueHiddenEl = document.getElementById(`taxable_value_hidden_${index}`);

    if (basicRateEl) basicRateEl.textContent = `₹${basicRate.toFixed(2)}`;
    if (basicRateHiddenEl) basicRateHiddenEl.value = basicRate.toFixed(2);

    if (totalEl) totalEl.textContent = `₹${total.toFixed(2)}`;
    if (totalHiddenEl) totalHiddenEl.value = total.toFixed(2);

    if (taxableValueEl) taxableValueEl.textContent = `₹${taxableValue.toFixed(2)}`;
    if (taxableValueHiddenEl) taxableValueHiddenEl.value = taxableValue.toFixed(2);

    // Calculate tax amounts
    const cgstPercent = gstPercent / 2;
    const sgstPercent = gstPercent / 2;
    const cgstAmount = (taxableValue * cgstPercent) / 100;
    const sgstAmount = (taxableValue * sgstPercent) / 100;

    // Update tax displays
    document.getElementById(`cgst_percent_${index}`).textContent = `${cgstPercent.toFixed(2)}%`;
    document.getElementById(`cgst_percent_hidden_${index}`).value = cgstPercent.toFixed(2);
    
    document.getElementById(`cgst_amount_${index}`).textContent = `₹${cgstAmount.toFixed(2)}`;
    document.getElementById(`cgst_amount_hidden_${index}`).value = cgstAmount.toFixed(2);
    
    document.getElementById(`sgst_percent_${index}`).textContent = `${sgstPercent.toFixed(2)}%`;
    document.getElementById(`sgst_percent_hidden_${index}`).value = sgstPercent.toFixed(2);
    
    document.getElementById(`sgst_amount_${index}`).textContent = `₹${sgstAmount.toFixed(2)}`;
    document.getElementById(`sgst_amount_hidden_${index}`).value = sgstAmount.toFixed(2);

    // IGST is 0 for local sales
    document.getElementById(`igst_percent_${index}`).textContent = '0.00%';
    document.getElementById(`igst_percent_hidden_${index}`).value = '0.00';
    
    document.getElementById(`igst_amount_${index}`).textContent = '₹0.00';
    document.getElementById(`igst_amount_hidden_${index}`).value = '0.00';

    // Recalculate overall totals
    calculateOverallTotalsFromExisting();
}

// Remove existing item
function removeExistingItem(index) {
    if (confirm('Are you sure you want to remove this item?')) {
        const row = document.querySelector(`input[name="products[${index}][product_id]"]`).closest('tr');
        row.style.display = 'none';
        
        // Mark for deletion by setting product_id to empty
        document.querySelector(`input[name="products[${index}][product_id]"]`).value = '';
        
        calculateOverallTotalsFromExisting();
    }
}

// Calculate overall totals from existing items
function calculateOverallTotalsFromExisting() {
    let subtotal = 0;
    let totalCgst = 0;
    let totalSgst = 0;
    let totalIgst = 0;

    // Calculate from existing items
    document.querySelectorAll('input[name*="[total]"]').forEach(input => {
        if (input.type === 'hidden' && input.value && input.closest('tr').style.display !== 'none') {
            subtotal += parseFloat(input.value) || 0;
        }
    });

    document.querySelectorAll('input[name*="[cgst_amount]"]').forEach(input => {
        if (input.type === 'hidden' && input.value && input.closest('tr').style.display !== 'none') {
            totalCgst += parseFloat(input.value) || 0;
        }
    });

    document.querySelectorAll('input[name*="[sgst_amount]"]').forEach(input => {
        if (input.type === 'hidden' && input.value && input.closest('tr').style.display !== 'none') {
            totalSgst += parseFloat(input.value) || 0;
        }
    });

    document.querySelectorAll('input[name*="[igst_amount]"]').forEach(input => {
        if (input.type === 'hidden' && input.value && input.closest('tr').style.display !== 'none') {
            totalIgst += parseFloat(input.value) || 0;
        }
    });

    // Add from newly added products
    addedProducts.forEach(product => {
        subtotal += parseFloat(product.total) || 0;
        totalCgst += parseFloat(product.cgst_amount) || 0;
        totalSgst += parseFloat(product.sgst_amount) || 0;
        totalIgst += parseFloat(product.igst_amount) || 0;
    });

    // Update displays
    document.getElementById('subtotal').textContent = `₹${subtotal.toFixed(2)}`;
    document.getElementById('total_cgst').textContent = `₹${totalCgst.toFixed(2)}`;
    document.getElementById('total_sgst').textContent = `₹${totalSgst.toFixed(2)}`;
    document.getElementById('total_igst').textContent = `₹${totalIgst.toFixed(2)}`;

    calculateFinalTotals();
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Recalculate all existing items to ensure proper display
    const existingItems = document.querySelectorAll('input[name^="products["][name$="][packages]"]');
    existingItems.forEach((input) => {
        const match = input.name.match(/products\[(\d+)\]/);
        if (match) {
            const index = match[1];
            calculateItemTotals(index);
        }
    });

    calculateOverallTotalsFromExisting();

    // Add event listeners for additional charges fields
    document.getElementById('ins_pmt').addEventListener('input', calculateFinalTotals);
    document.getElementById('insurance').addEventListener('input', calculateFinalTotals);
    document.getElementById('frt_advance').addEventListener('input', calculateFinalTotals);
    document.getElementById('tcs_percent').addEventListener('input', calculateFinalTotals);
});

// Customer Modal Functions
function openCustomerModal() {
    document.getElementById('customerForm').reset();
    const modal = document.getElementById('customerModal');
    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';

    // Add click outside to close functionality
    setTimeout(() => {
        modal.onclick = function(e) {
            if (e.target === modal) {
                closeCustomerModal();
            }
        };
    }, 100);

    // Add ESC key support
    document.addEventListener('keydown', handleCustomerModalEsc);

    // Focus first input field
    setTimeout(() => {
        const firstInput = modal.querySelector('input, select, textarea');
        if (firstInput) firstInput.focus();
    }, 100);
}

function closeCustomerModal() {
    const modal = document.getElementById('customerModal');
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
    modal.onclick = null; // Remove event listener
    document.removeEventListener('keydown', handleCustomerModalEsc);
}

function handleCustomerModalEsc(e) {
    if (e.key === 'Escape') {
        closeCustomerModal();
    }
}

function submitCustomerForm() {
    const form = document.getElementById('customerForm');
    const formData = new FormData(form);

    // Add default status
    formData.append('status', 'active');

    fetch('{{ route("customers.store") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Add new customer to select dropdown
            const select = document.getElementById('billed_to');
            const option = document.createElement('option');
            option.value = data.customer.id;
            option.setAttribute('data-name', data.customer.name);
            option.setAttribute('data-mobile', data.customer.phone || '');
            option.setAttribute('data-email', data.customer.email || '');
            option.textContent = data.customer.name;
            select.appendChild(option);

            // Select the new customer
            select.value = data.customer.id;
            loadCustomerDetails(data.customer.id);

            closeCustomerModal();
            alert('Customer created successfully!');
        } else {
            alert('Error creating customer: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error creating customer');
    });
}

// Product Modal Functions
function openProductModal() {
    console.log('Opening product modal...');
    document.getElementById('productForm').reset();
    const modal = document.getElementById('productModal');
    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';

    // Add click outside to close functionality
    setTimeout(() => {
        modal.onclick = function(e) {
            if (e.target === modal) {
                closeProductModal();
            }
        };
    }, 100);

    // Add ESC key support
    document.addEventListener('keydown', handleProductModalEsc);

    // Focus first input field
    setTimeout(() => {
        const firstInput = modal.querySelector('input, select, textarea');
        if (firstInput) firstInput.focus();
    }, 100);
}

function closeProductModal() {
    const modal = document.getElementById('productModal');
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
    modal.onclick = null; // Remove event listener
    document.removeEventListener('keydown', handleProductModalEsc);
}

function handleProductModalEsc(e) {
    if (e.key === 'Escape') {
        closeProductModal();
    }
}

function submitProductForm() {
    const form = document.getElementById('productForm');
    const formData = new FormData(form);

    // Add default status
    formData.append('status', 'active');

    fetch('{{ route("products.store") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Add new product to select dropdown
            const select = document.getElementById('product_select');
            const option = document.createElement('option');
            option.value = data.product.id;
            option.setAttribute('data-name', data.product.name);
            option.setAttribute('data-hsn', data.product.hsn_code || '');
            option.setAttribute('data-gst', data.product.gst_percentage || '18');
            option.setAttribute('data-unit', data.product.unit || 'PCS');
            option.setAttribute('data-rate', data.product.rate || '0');
            option.textContent = data.product.name;
            select.appendChild(option);

            // Select the new product
            select.value = data.product.id;
            loadProductDetails();

            closeProductModal();
            alert('Product created successfully!');
        } else {
            alert('Error creating product: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error creating product');
    });
}

// Load cities based on state selection
function loadCities(stateId) {
    const citySelect = document.getElementById('city_id');
    citySelect.innerHTML = '<option value="">Select City</option>';

    if (stateId) {
        fetch(`/get-cities-by-state/${stateId}`)
            .then(response => response.json())
            .then(cities => {
                cities.forEach(city => {
                    const option = document.createElement('option');
                    option.value = city.id;
                    option.textContent = city.name;
                    citySelect.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Error loading cities:', error);
            });
    }
}
</script>

<!-- Customer Modal -->
<div id="customerModal" class="modal-overlay" style="display: none;">
    <div class="modal-container">
        <div class="modal-header">
            <h3>Add New Customer</h3>
            <button type="button" class="modal-close" onclick="closeCustomerModal()">&times;</button>
        </div>
        <div class="modal-body">
            <form id="customerForm">
                @csrf
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Customer Name <span class="text-danger">*</span></label>
                        <input type="text" name="name" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Email</label>
                        <input type="email" name="email" class="form-control">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Phone <span class="text-danger">*</span></label>
                        <input type="text" name="phone" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">GST Number</label>
                        <input type="text" name="gst_number" class="form-control">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Address <span class="text-danger">*</span></label>
                        <textarea name="address" class="form-control" rows="2" required></textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label">State <span class="text-danger">*</span></label>
                        <select name="state_id" id="state_id" class="form-select myselect" onchange="loadCities(this.value)" required>
                            <option value="">Select State</option>
                            @foreach($states as $state)
                                <option value="{{ $state->id }}">{{ $state->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">City <span class="text-danger">*</span></label>
                        <select name="city_id" id="city_id" class="form-select myselect" required>
                            <option value="">Select City</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Pincode <span class="text-danger">*</span></label>
                        <input type="text" name="pincode" class="form-control" required>
                    </div>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeCustomerModal()">Cancel</button>
            <button type="button" class="btn btn-primary" onclick="submitCustomerForm()">Save Customer</button>
        </div>
    </div>
</div>

<!-- Product Modal -->
<div id="productModal" class="modal-overlay" style="display: none;">
    <div class="modal-container">
        <div class="modal-header">
            <h3>Add New Product</h3>
            <button type="button" class="modal-close" onclick="closeProductModal()">&times;</button>
        </div>
        <div class="modal-body">
            <form id="productForm">
                @csrf
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Product Name <span class="text-danger">*</span></label>
                        <input type="text" name="name" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Category <span class="text-danger">*</span></label>
                        <select name="category_id" class="form-select myselect" required>
                            <option value="">Select Category</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}">{{ $category->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">HSN Code</label>
                        <input type="text" name="hsn_code" class="form-control">
                    </div>
                    <div class="form-group">
                        <label class="form-label">GST Percentage <span class="text-danger">*</span></label>
                        <input type="number" name="gst_percentage" class="form-control" step="0.01" min="0" max="100" value="18" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Unit <span class="text-danger">*</span></label>
                        <select name="unit" class="form-select myselect" required>
                            <option value="">Select Unit</option>
                            <option value="PCS">PCS</option>
                            <option value="KG">KG</option>
                            <option value="MT">MT</option>
                            <option value="LTR">LTR</option>
                            <option value="BOX">BOX</option>
                            <option value="SET">SET</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Rate (₹) <span class="text-danger">*</span></label>
                        <input type="number" name="rate" class="form-control" step="0.01" min="0" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Price (₹) <span class="text-danger">*</span></label>
                        <input type="number" name="price" class="form-control" step="0.01" min="0" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Stock Quantity <span class="text-danger">*</span></label>
                        <input type="number" name="stock_quantity" class="form-control" min="0" value="0" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Description</label>
                        <textarea name="description" class="form-control" rows="2"></textarea>
                    </div>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeProductModal()">Cancel</button>
            <button type="button" class="btn btn-primary" onclick="submitProductForm()">Save Product</button>
        </div>
    </div>
</div>

<style>
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1050;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    animation: fadeIn 0.3s ease-out;
}

.modal-container {
    background: white;
    border-radius: 8px;
    width: 100%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    transform: scale(1);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes modalSlideIn {
    from {
        transform: scale(0.9);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #e3e6f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #5a5c69;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #858796;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background-color: #f8f9fc;
    color: #5a5c69;
    transform: scale(1.1);
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #e3e6f0;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-label {
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 5px;
}

.form-control, .form-select {
    padding: 8px 12px;
    border: 1px solid #d1d3e2;
    border-radius: 4px;
    font-size: 14px;
}

.form-control:focus, .form-select:focus {
    outline: none;
    border-color: #4e73df;
    box-shadow: 0 0 0 2px rgba(78, 115, 223, 0.25);
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
}

.btn-primary {
    background-color: #4e73df;
    color: white;
}

.btn-secondary {
    background-color: #858796;
    color: white;
}

.btn:hover {
    opacity: 0.9;
}

.text-danger {
    color: #e74a3b;
}
</style>

@endsection
