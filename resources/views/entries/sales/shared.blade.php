<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quotation {{ $salesEntry->quotation_number }} - {{ $salesEntry->company->name }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .container {
            padding: 2rem 0;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 2rem;
        }
        
        .company-logo {
            width: 60px;
            height: 60px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
        }
        
        .btn-download {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .btn-download:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            color: white;
        }
        
        .info-item {
            padding: 0.75rem 0;
            border-bottom: 1px solid #eee;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #666;
            margin-bottom: 0.25rem;
        }
        
        .info-value {
            color: #333;
            font-size: 1.1rem;
        }
        
        .amount-highlight {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: bold;
            font-size: 1.5rem;
        }
        
        .footer-text {
            color: #666;
            font-size: 0.9rem;
            text-align: center;
            margin-top: 2rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card">
                    <div class="card-header text-center">
                        <div class="company-logo mx-auto">
                            <i class="fas fa-building fa-2x text-primary"></i>
                        </div>
                        <h3 class="mb-0">{{ $salesEntry->company->name }}</h3>
                        <p class="mb-0 opacity-75">{{ $salesEntry->company->address }}</p>
                    </div>
                    
                    <div class="card-body p-4">
                        <div class="text-center mb-4">
                            <h4 class="text-primary">Quotation Details</h4>
                            <hr class="w-25 mx-auto">
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">Quotation Number</div>
                            <div class="info-value">{{ $salesEntry->quotation_number }}</div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">Customer</div>
                            <div class="info-value">{{ $salesEntry->customer->name }}</div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">Date</div>
                            <div class="info-value">{{ $salesEntry->quotation_date->format('d M Y') }}</div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">Valid Until</div>
                            <div class="info-value">{{ $salesEntry->valid_until->format('d M Y') }}</div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">Total Amount</div>
                            <div class="info-value amount-highlight">{{ $salesEntry->formatted_total }}</div>
                        </div>
                        
                        <div class="text-center mt-4">
                            <a href="{{ url('/share/' . $salesEntry->share_token . '.pdf') }}"
                               class="btn btn-download btn-lg" target="_blank">
                                <i class="fas fa-download me-2"></i>
                                Download PDF
                            </a>
                        </div>
                        
                        <div class="footer-text">
                            <i class="fas fa-shield-alt me-1"></i>
                            This is a secure shared link that expires on 
                            {{ $salesEntry->share_expires_at->format('d M Y') }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
