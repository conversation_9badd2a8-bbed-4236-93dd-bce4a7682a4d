<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sales Entry - {{ $salesEntry->invoice_number ?? $salesEntry->quotation_number ?? 'SE-001' }} | {{ $salesEntry->company->name ?? 'Company' }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #000;
            background: #fff;
        }

        .container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 10mm;
            background: white;
            min-height: 297mm;
        }

        /* Header Section */
        .header {
            border: 2px solid #4e73df;
            margin-bottom: 20px;
            border-radius: 8px;
            overflow: hidden;
        }

        .header-top {
            display: flex;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #4e73df;
            background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
            color: white;
            position: relative;
        }

        .logo {
            width: 70px;
            height: 70px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            font-weight: bold;
            margin-right: 20px;
        }

        .company-info {
            flex: 1;
            text-align: center;
        }

        .company-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .company-address {
            font-size: 11px;
            line-height: 1.3;
            margin-bottom: 8px;
            opacity: 0.9;
        }

        .company-contact {
            font-size: 10px;
            line-height: 1.3;
            opacity: 0.9;
        }

        .document-title {
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 16px;
            font-weight: bold;
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 16px;
            border-radius: 20px;
        }

        .header-details {
            display: flex;
            padding: 15px;
            background: #f8f9fc;
        }

        .left-details, .right-details {
            flex: 1;
        }

        .detail-row {
            display: flex;
            margin-bottom: 8px;
            align-items: center;
        }

        .detail-label {
            width: 100px;
            font-weight: 600;
            color: #5a5c69;
            font-size: 11px;
        }

        .detail-value {
            font-weight: 500;
            color: #333;
            font-size: 11px;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
            gap: 4px;
        }

        .status-draft { background: #f8f9fa; color: #6c757d; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-approved { background: #d4edda; color: #155724; }
        .status-rejected { background: #f8d7da; color: #721c24; }
        .status-completed { background: #d4edda; color: #155724; }

        /* Information Sections */
        .info-section {
            margin: 20px 0;
        }

        .section-title {
            background: linear-gradient(135deg, #f8f9fc 0%, #e3e6f0 100%);
            color: #4e73df;
            font-size: 12px;
            font-weight: 700;
            padding: 10px 15px;
            margin-bottom: 0;
            border-left: 4px solid #4e73df;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-bottom: 1px solid #e3e6f0;
        }

        .info-content {
            border: 1px solid #e3e6f0;
            border-top: none;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0;
        }

        .info-grid.single {
            grid-template-columns: 1fr;
        }

        .info-item {
            padding: 12px 15px;
            border-right: 1px solid #f8f9fc;
            border-bottom: 1px solid #f8f9fc;
            display: flex;
            align-items: center;
        }

        .info-item:nth-child(even) {
            border-right: none;
        }

        .info-item:last-child,
        .info-item:nth-last-child(2) {
            border-bottom: none;
        }

        .info-label {
            font-size: 10px;
            color: #5a5c69;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.3px;
            min-width: 90px;
            margin-right: 10px;
        }

        .info-value {
            font-size: 11px;
            color: #333;
            font-weight: 500;
            flex: 1;
        }

        /* Customer Information Section */
        .customer-section {
            margin: 20px 0;
            border: 2px solid #2196f3;
            border-radius: 8px;
            overflow: hidden;
        }

        .customer-header {
            background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
            color: white;
            padding: 10px 15px;
            font-size: 12px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .customer-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            background: #f8fff9;
        }

        .customer-item {
            padding: 15px;
            border-right: 1px solid #e3e6f0;
        }

        .customer-item:last-child {
            border-right: none;
        }

        .customer-label {
            font-size: 10px;
            color: #5a5c69;
            font-weight: 600;
            text-transform: uppercase;
            margin-bottom: 8px;
            letter-spacing: 0.3px;
        }

        .customer-value {
            font-size: 11px;
            color: #333;
            font-weight: 500;
            line-height: 1.4;
        }

        /* Products Table */
        .products-section {
            margin: 20px 0;
        }

        .products-table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #e3e6f0;
            border-radius: 8px;
            overflow: hidden;
            background: white;
            margin-top: 10px;
        }

        .products-table thead th {
            background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
            color: white;
            padding: 12px 8px;
            text-align: center;
            font-weight: 600;
            font-size: 10px;
            text-transform: uppercase;
            letter-spacing: 0.3px;
            border-right: 1px solid rgba(255, 255, 255, 0.2);
        }

        .products-table thead th:last-child {
            border-right: none;
        }

        .products-table tbody td {
            padding: 10px 8px;
            border-bottom: 1px solid #f8f9fc;
            border-right: 1px solid #f8f9fc;
            font-size: 10px;
            text-align: center;
            vertical-align: middle;
        }

        .products-table tbody td:last-child {
            border-right: none;
        }

        .products-table tbody tr:last-child td {
            border-bottom: none;
        }

        .products-table tbody tr:nth-child(even) {
            background: #f8f9fc;
        }

        .products-table .description {
            text-align: left;
            max-width: 200px;
        }

        .product-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 2px;
        }

        .product-desc {
            color: #666;
            font-size: 9px;
            font-style: italic;
        }

        /* Financial Section */
        .financial-section {
            margin: 25px 0;
            border: 2px solid #4e73df;
            border-radius: 8px;
            overflow: hidden;
        }

        .financial-header {
            background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
            color: white;
            padding: 12px 15px;
            font-size: 14px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            text-align: center;
        }

        .financial-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        .financial-table tr:nth-child(even) {
            background: #f8f9fc;
        }

        .financial-table td {
            padding: 10px 15px;
            font-size: 11px;
            border-bottom: 1px solid #e3e6f0;
        }

        .financial-table td:first-child {
            font-weight: 600;
            color: #5a5c69;
        }

        .financial-table td:last-child {
            text-align: right;
            font-weight: 600;
            font-family: 'Courier New', monospace;
            color: #333;
        }

        .financial-table .total-row td {
            background: #1cc88a;
            color: white;
            font-weight: 700;
            font-size: 14px;
            padding: 12px 15px;
            border-bottom: none;
        }

        /* Terms and Conditions Section */
        .terms-section {
            margin: 20px 0;
        }

        .terms-content {
            background: #f8f9fc;
            padding: 15px;
            border: 1px solid #e3e6f0;
            border-top: none;
        }

        .terms-text {
            font-size: 10px;
            color: #5a5c69;
            line-height: 1.5;
            margin-bottom: 8px;
        }

        /* Totals Section */
        .totals-section {
            margin: 20px 0;
            border: 1px solid #e3e6f0;
            border-radius: 8px;
            overflow: hidden;
            background: white;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 15px;
            border-bottom: 1px solid #f8f9fc;
            font-size: 11px;
        }

        .total-row:last-child {
            border-bottom: none;
        }

        .total-row:nth-child(even) {
            background: #f8f9fc;
        }

        .total-label {
            font-weight: 600;
            color: #5a5c69;
        }

        .grand-total {
            background: #4e73df !important;
            color: white !important;
            font-weight: 700 !important;
            font-size: 14px !important;
        }

        .grand-total .total-label {
            color: white !important;
        }

        /* Footer */
        .footer {
            margin-top: 30px;
            border-top: 2px solid #4e73df;
            padding-top: 15px;
        }

        .footer-info {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 15px;
            text-align: center;
        }

        .footer-item {
            font-size: 10px;
            color: #5a5c69;
        }

        .footer-label {
            font-weight: 600;
            color: #333;
            display: block;
            margin-bottom: 3px;
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }

        .signature-section {
            display: flex;
            justify-content: space-between;
            margin: 40px 0 20px 0;
        }

        .signature-box {
            text-align: center;
            width: 150px;
        }

        .signature-line {
            border-top: 1px solid #333;
            margin-top: 40px;
            padding-top: 8px;
            font-size: 10px;
            font-weight: 600;
            color: #5a5c69;
        }

        .print-info {
            text-align: center;
            font-size: 9px;
            color: #999;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #e3e6f0;
        }



        @media print {
            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
            }

            body {
                margin: 0;
                padding: 0;
            }

            .container {
                box-shadow: none;
                padding: 5mm;
            }

            .no-print {
                display: none !important;
            }


        }
    </style>
</head>
<body>


    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-top">
                <div class="logo">
                    {{ strtoupper(substr($salesEntry->company->name ?? 'CO', 0, 3)) }}
                </div>
                <div class="company-info">
                    <div class="company-name">{{ $salesEntry->company->name ?? 'Company Name' }}</div>
                    <div class="company-address">
                        @if($salesEntry->company->address)
                            {{ $salesEntry->company->address }}<br>
                        @endif
                        @if($salesEntry->company->city || $salesEntry->company->state)
                            {{ $salesEntry->company->city->name ?? '' }}
                            @if($salesEntry->company->city && $salesEntry->company->state), @endif
                            {{ $salesEntry->company->state->name ?? '' }}
                            @if($salesEntry->company->pincode) - {{ $salesEntry->company->pincode }}@endif
                        @endif
                    </div>
                    <div class="company-contact">
                        @if($salesEntry->company->email)Email: {{ $salesEntry->company->email }}@endif
                        @if($salesEntry->company->email && $salesEntry->company->phone) | @endif
                        @if($salesEntry->company->phone)Phone: {{ $salesEntry->company->phone }}@endif
                    </div>
                </div>
                <div class="document-title">
                    {{ $salesEntry->invoice_number ? 'SALES INVOICE' : 'SALES QUOTATION' }}
                </div>
            </div>

            <div class="header-details">
                <div class="left-details">
                    <div class="detail-row">
                        <span class="detail-label">Entry No:</span>
                        <span class="detail-value">#{{ str_pad($salesEntry->id, 6, '0', STR_PAD_LEFT) }}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">{{ $salesEntry->invoice_number ? 'Invoice No:' : 'Quote No:' }}</span>
                        <span class="detail-value">{{ $salesEntry->invoice_number ?? $salesEntry->quotation_number ?? 'Not Set' }}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Date:</span>
                        <span class="detail-value">{{ ($salesEntry->invoice_date ?? $salesEntry->quotation_date) ? ($salesEntry->invoice_date ?? $salesEntry->quotation_date)->format('d M Y') : 'Not Set' }}</span>
                    </div>
                </div>
                <div class="right-details">
                    <div class="detail-row">
                        <span class="detail-label">Status:</span>
                        <span class="detail-value">
                            <span class="status-badge status-{{ $salesEntry->status }}">
                                {{ ucfirst($salesEntry->status ?? 'draft') }}
                            </span>
                        </span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Created By:</span>
                        <span class="detail-value">{{ $salesEntry->user->name ?? 'System' }}</span>
                    </div>
                    @if($salesEntry->valid_until)
                    <div class="detail-row">
                        <span class="detail-label">Valid Until:</span>
                        <span class="detail-value">{{ $salesEntry->valid_until->format('d M Y') }}</span>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Customer Information -->
        <div class="customer-section">
            <div class="customer-header">👤 Customer Information</div>
            <div class="customer-grid">
                <div class="customer-item">
                    <div class="customer-label">Customer Name:</div>
                    <div class="customer-value">{{ $salesEntry->customer->name ?? $salesEntry->party_name ?? 'Not Specified' }}</div>
                </div>
                <div class="customer-item">
                    <div class="customer-label">Contact:</div>
                    <div class="customer-value">
                        @if($salesEntry->customer->phone ?? $salesEntry->mobile)
                            Phone: {{ $salesEntry->customer->phone ?? $salesEntry->mobile }}<br>
                        @endif
                        @if($salesEntry->customer->email ?? $salesEntry->email)
                            Email: {{ $salesEntry->customer->email ?? $salesEntry->email }}
                        @endif
                    </div>
                    @if($salesEntry->valid_until)
                    <div class="detail-row">
                        <span class="detail-label">Valid Until:</span>
                        <span class="detail-value">{{ $salesEntry->valid_until->format('d M Y') }}</span>
                    </div>
                    @endif
                </div>
                <div class="customer-item">
                    <div class="customer-label">Billing Address:</div>
                    <div class="customer-value">
                        @if($salesEntry->customer->address)
                            {{ $salesEntry->customer->address }}<br>
                        @endif
                        @if($salesEntry->customer->city || $salesEntry->customer->state)
                            {{ $salesEntry->customer->city->name ?? '' }}
                            @if($salesEntry->customer->city && $salesEntry->customer->state), @endif
                            {{ $salesEntry->customer->state->name ?? '' }}
                            @if($salesEntry->customer->pincode) - {{ $salesEntry->customer->pincode }}@endif
                        @endif
                    </div>
                </div>
                <div class="customer-item">
                    <div class="customer-label">Payment Terms:</div>
                    <div class="customer-value">{{ $salesEntry->payment_terms ?? 'Not Specified' }}</div>
                </div>
            </div>
        </div>

        <!-- Products Section -->
        <div class="info-section">
            <div class="section-title">📦 Products & Services</div>
            <div class="info-content">
                <table class="products-table">
                    <thead>
                        <tr>
                            <th>S.No.</th>
                            <th class="description">Description of Goods</th>
                            <th class="hsn">HSN</th>
                            <th class="qty">Qty.</th>
                            <th class="rate">Basic Rate</th>
                            <th class="rate">Gauge Diff.</th>
                            <th class="rate">Ldg. Rate</th>
                            <th class="rate">Rate/ PMT</th>
                            <th class="amount">Value of Goods (Rs.)</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($salesEntry->items as $index => $item)
                        <tr>
                            <td>{{ $index + 1 }}</td>
                            <td class="description">{{ $item->product->name ?? 'N/A' }}</td>
                            <td class="hsn">{{ $item->hsn_code ?? $item->product->hsn_code ?? 'N/A' }}</td>
                            <td class="qty">{{ number_format($item->packages ?? $item->quantity ?? 0, 3) }}</td>
                            <td class="rate">{{ number_format($item->basic_rate ?? 0, 2) }}</td>
                            <td class="rate">{{ number_format($item->gauge_diff ?? 0, 2) }}</td>
                            <td class="rate">{{ number_format($item->loading_rate ?? 0, 2) }}</td>
                            <td class="rate">{{ number_format($item->rate ?? $item->unit_price ?? 0, 2) }}</td>
                            <td class="amount">{{ number_format($item->total ?? $item->line_total ?? 0, 2) }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Financial Summary -->
        <div class="financial-section">
            <div class="financial-header">💰 Financial Summary</div>
            <table class="financial-table">
                <tr>
                    <td>Subtotal</td>
                    <td>₹{{ number_format($salesEntry->subtotal ?? 0, 2) }}</td>
                </tr>
                @if(($salesEntry->total_cgst ?? 0) > 0)
                <tr>
                    <td>CGST</td>
                    <td>₹{{ number_format($salesEntry->total_cgst, 2) }}</td>
                </tr>
                @endif
                @if(($salesEntry->total_sgst ?? 0) > 0)
                <tr>
                    <td>SGST</td>
                    <td>₹{{ number_format($salesEntry->total_sgst, 2) }}</td>
                </tr>
                @endif
                @if(($salesEntry->total_igst ?? 0) > 0)
                <tr>
                    <td>IGST</td>
                    <td>₹{{ number_format($salesEntry->total_igst, 2) }}</td>
                </tr>
                @endif
                @if(($salesEntry->ins_pmt ?? 0) > 0)
                <tr>
                    <td>Insurance P/MT</td>
                    <td>₹{{ number_format($salesEntry->ins_pmt, 2) }}</td>
                </tr>
                @endif
                @if(($salesEntry->insurance ?? 0) > 0)
                <tr>
                    <td>Insurance</td>
                    <td>₹{{ number_format($salesEntry->insurance, 2) }}</td>
                </tr>
                @endif
                @if(($salesEntry->frt_advance ?? 0) > 0)
                <tr>
                    <td>Freight Advance</td>
                    <td>₹{{ number_format($salesEntry->frt_advance, 2) }}</td>
                </tr>
                @endif
                <tr>
                    <td><strong>Grand Total</strong></td>
                    <td><strong>₹{{ number_format($salesEntry->grand_total ?? $salesEntry->total_amount ?? 0, 2) }}</strong></td>
                </tr>
                @if(($salesEntry->tcs_percent ?? 0) > 0)
                <tr>
                    <td>TCS ({{ number_format($salesEntry->tcs_percent, 2) }}%)</td>
                    <td>₹{{ number_format($salesEntry->tcs_amount ?? 0, 2) }}</td>
                </tr>
                @endif
                <tr class="total-row">
                    <td><strong>NET AMOUNT</strong></td>
                    <td><strong>₹{{ number_format($salesEntry->calculated_net_amount ?? $salesEntry->grand_total ?? $salesEntry->total_amount ?? 0, 2) }}</strong></td>
                </tr>
            </table>
        </div>

        <!-- Additional Information -->
        @if($salesEntry->remarks || $salesEntry->delivery_terms || $salesEntry->payment_terms)
        <div class="info-section">
            <div class="section-title">ℹ️ Additional Information</div>
            <div class="info-content">
                <div class="info-grid single">
                    @if($salesEntry->delivery_terms)
                    <div class="info-item">
                        <span class="info-label">Delivery Terms:</span>
                        <span class="info-value">{{ $salesEntry->delivery_terms }}</span>
                    </div>
                    @endif

                    @if($salesEntry->payment_terms)
                    <div class="info-item">
                        <span class="info-label">Payment Terms:</span>
                        <span class="info-value">{{ $salesEntry->payment_terms }}</span>
                    </div>
                    @endif

                    @if($salesEntry->remarks)
                    <div class="info-item">
                        <span class="info-label">Remarks:</span>
                        <span class="info-value">{{ $salesEntry->remarks }}</span>
                    </div>
                    @endif
                </div>
            </div>
        </div>
        @endif

        <!-- Terms and Conditions -->
        @if($salesEntry->terms_conditions)
        <div class="terms-section">
            <div class="section-title">📋 Terms & Conditions</div>
            <div class="terms-content">
                <div class="terms-text">{{ $salesEntry->terms_conditions }}</div>
            </div>
        </div>
        @endif

        <!-- Signature Section -->
        <div class="signature-section">
            <div class="signature-box">
                <div class="signature-line">Prepared By</div>
            </div>
            <div class="signature-box">
                <div class="signature-line">Verified By</div>
            </div>
            <div class="signature-box">
                <div class="signature-line">Authorized Signatory</div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div class="footer-info">
                <div class="footer-item">
                    <span class="footer-label">Document Generated</span>
                    {{ now()->format('d M Y, h:i A') }}
                </div>
                <div class="footer-item">
                    <span class="footer-label">Generated By</span>
                    {{ auth()->user()->name ?? 'System' }}
                </div>
                <div class="footer-item">
                    <span class="footer-label">System Reference</span>
                    {{ strtoupper(substr($salesEntry->company->name ?? 'CO', 0, 3)) }}-SAL-{{ $salesEntry->id }}
                </div>
            </div>

            <div class="print-info">
                This is a system-generated document from {{ $salesEntry->company->name ?? 'Company' }} Sales Management System.<br>
                For any queries regarding this sales entry, please contact the sales department.
            </div>
        </div>
    </div>
</body>
</html>
