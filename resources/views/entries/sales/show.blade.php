@extends('layouts.app')

@section('title', 'Sales Entry Details - JMD Traders')
@section('page-title', 'Sales Entry Details')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Sales Entry Details</h1>
        <div>
            <a href="{{ route('entries.sales') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
            @permission('sales.print')
            <a href="{{ route('entries.sales.print', $salesEntry->id) }}" class="btn btn-info btn-sm" target="_blank" title="Print PDF with Company Header">
                <i class="fas fa-file-pdf"></i> Print PDF
            </a>
            @endpermission
            @permission('sales.edit')
            <a href="{{ route('entries.sales.edit', $salesEntry->id) }}" class="btn btn-primary btn-sm">
                <i class="fas fa-edit"></i> Edit
            </a>
            @endpermission
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Invoice Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Invoice Number:</strong> {{ $salesEntry->invoice_number ?? $salesEntry->quotation_number }}</p>
                            <p><strong>Invoice Date:</strong> {{ $salesEntry->invoice_date ? $salesEntry->invoice_date->format('d/m/Y') : $salesEntry->quotation_date->format('d/m/Y') }}</p>
                            <p><strong>Payment Terms:</strong> {{ $salesEntry->payment_terms ?? 'N/A' }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Status:</strong> 
                                <span class="badge badge-{{ $salesEntry->status == 'approved' ? 'success' : ($salesEntry->status == 'pending' ? 'warning' : ($salesEntry->status == 'draft' ? 'secondary' : 'danger')) }}">
                                    {{ ucfirst($salesEntry->status) }}
                                </span>
                            </p>
                            <p><strong>Created By:</strong> {{ $salesEntry->user->name ?? 'N/A' }}</p>
                            <p><strong>Created Date:</strong> {{ $salesEntry->created_at->format('d/m/Y H:i') }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Customer Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Customer:</strong> {{ $salesEntry->customer->name ?? 'N/A' }}</p>
                            <p><strong>Party Name:</strong> {{ $salesEntry->party_name ?? $salesEntry->customer->name ?? 'N/A' }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Mobile:</strong> {{ $salesEntry->mobile ?? $salesEntry->customer->mobile ?? 'N/A' }}</p>
                            <p><strong>Email:</strong> {{ $salesEntry->email ?? $salesEntry->customer->email ?? 'N/A' }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Product Details</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>HSN Code</th>
                                    <th>Qty/Pkg</th>
                                    <th>Unit</th>
                                    <th>Rate</th>
                                    <th>Loading</th>
                                    <th>Gauge Diff.</th>
                                    <th>Basic Rate</th>
                                    <th>Total</th>
                                    <th>GST%</th>
                                    <th>Tax Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($salesEntry->items as $item)
                                <tr>
                                    <td>
                                        <strong>{{ $item->product->name ?? 'N/A' }}</strong>
                                        @if($item->description)
                                            <br><small class="text-muted">{{ $item->description }}</small>
                                        @endif
                                    </td>
                                    <td class="text-center">{{ $item->hsn_code ?? 'N/A' }}</td>
                                    <td class="text-center">{{ $item->packages ?? $item->quantity ?? 0 }}</td>
                                    <td class="text-center">{{ $item->unit ?? 'PCS' }}</td>
                                    <td class="text-end">₹{{ number_format($item->rate ?? $item->unit_price ?? 0, 2) }}</td>
                                    <td class="text-end">₹{{ number_format($item->loading ?? 0, 2) }}</td>
                                    <td class="text-end">
                                        @if(($item->gauge_diff ?? 0) != 0)
                                            {{ number_format($item->gauge_diff, 2) }}
                                            <br><small class="text-muted">Amt: ₹{{ number_format(($item->packages ?? $item->quantity ?? 0) * ($item->gauge_diff ?? 0), 2) }}</small>
                                        @else
                                            -
                                        @endif
                                    </td>
                                    <td class="text-end">₹{{ number_format($item->basic_rate ?? (($item->rate ?? 0) + ($item->loading ?? 0)), 2) }}</td>
                                    <td class="text-end">₹{{ number_format($item->total ?? $item->line_total ?? 0, 2) }}</td>
                                    <td class="text-center">{{ $item->gst_percent ?? 0 }}%</td>
                                    <td class="text-end">₹{{ number_format(($item->cgst_amount ?? 0) + ($item->sgst_amount ?? 0) + ($item->igst_amount ?? 0), 2) }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Summary</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Subtotal:</span>
                        <span>₹{{ number_format($salesEntry->subtotal ?? 0, 2) }}</span>
                    </div>
                    @if($salesEntry->total_cgst > 0)
                    <div class="d-flex justify-content-between mb-2">
                        <span>CGST:</span>
                        <span>₹{{ number_format($salesEntry->total_cgst, 2) }}</span>
                    </div>
                    @endif
                    @if($salesEntry->total_sgst > 0)
                    <div class="d-flex justify-content-between mb-2">
                        <span>SGST:</span>
                        <span>₹{{ number_format($salesEntry->total_sgst, 2) }}</span>
                    </div>
                    @endif
                    @if($salesEntry->total_igst > 0)
                    <div class="d-flex justify-content-between mb-2">
                        <span>IGST:</span>
                        <span>₹{{ number_format($salesEntry->total_igst, 2) }}</span>
                    </div>
                    @endif
                    @if($salesEntry->tax_amount > 0)
                    <div class="d-flex justify-content-between mb-2">
                        <span>Tax ({{ $salesEntry->tax_rate ?? 0 }}%):</span>
                        <span>₹{{ number_format($salesEntry->tax_amount, 2) }}</span>
                    </div>
                    @endif
                    
                    <!-- Additional Charges Section -->
                    @if(($salesEntry->ins_pmt ?? 0) > 0 || ($salesEntry->insurance ?? 0) > 0 || ($salesEntry->frt_advance ?? 0) > 0)
                    <hr>
                    <small class="text-muted font-weight-bold">Additional Charges:</small>
                    @endif
                    
                    @if(($salesEntry->ins_pmt ?? 0) > 0)
                    <div class="d-flex justify-content-between mb-2">
                        <span>Insurance P/MT:</span>
                        <span>₹{{ number_format($salesEntry->ins_pmt, 2) }}</span>
                    </div>
                    @endif
                    
                    @if(($salesEntry->insurance ?? 0) > 0)
                    <div class="d-flex justify-content-between mb-2">
                        <span>Insurance:</span>
                        <span>₹{{ number_format($salesEntry->insurance, 2) }}</span>
                    </div>
                    @endif
                    
                    @if(($salesEntry->frt_advance ?? 0) > 0)
                    <div class="d-flex justify-content-between mb-2">
                        <span>Freight Advance:</span>
                        <span>₹{{ number_format($salesEntry->frt_advance, 2) }}</span>
                    </div>
                    @endif
                    
                    <hr>
                    <div class="d-flex justify-content-between mb-2">
                        <strong>Grand Total:</strong>
                        <strong>₹{{ number_format($salesEntry->grand_total ?? $salesEntry->total_amount ?? 0, 2) }}</strong>
                    </div>
                    
                    @if(($salesEntry->tcs_percent ?? 0) > 0)
                    <div class="d-flex justify-content-between mb-2">
                        <span>TCS ({{ number_format($salesEntry->tcs_percent, 2) }}%):</span>
                        <span>₹{{ number_format($salesEntry->tcs_amount ?? 0, 2) }}</span>
                    </div>
                    @endif
                    
                    <hr>
                    <div class="d-flex justify-content-between">
                        <strong class="text-primary">Net Amount:</strong>
                        <strong class="text-primary">₹{{ number_format($salesEntry->calculated_net_amount, 2) }}</strong>
                    </div>
                </div>
            </div>

            <!-- Additional Charges & Totals Details Card -->
            @if(($salesEntry->ins_pmt ?? 0) > 0 || ($salesEntry->insurance ?? 0) > 0 || ($salesEntry->frt_advance ?? 0) > 0 || ($salesEntry->tcs_percent ?? 0) > 0)
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-calculator"></i> Additional Charges & Totals
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted">Charges:</h6>
                            <p><strong>Insurance P/MT:</strong><br>₹{{ number_format($salesEntry->ins_pmt ?? 0, 2) }}</p>
                            <p><strong>Insurance:</strong><br>₹{{ number_format($salesEntry->insurance ?? 0, 2) }}</p>
                            <p><strong>Freight Advance:</strong><br>₹{{ number_format($salesEntry->frt_advance ?? 0, 2) }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">Tax & Totals:</h6>
                            <p><strong>TCS %:</strong><br>{{ number_format($salesEntry->tcs_percent ?? 0, 2) }}%</p>
                            <p><strong>TCS Amount:</strong><br>₹{{ number_format($salesEntry->tcs_amount ?? 0, 2) }}</p>
                            <p><strong>Net Amount:</strong><br><span class="text-primary font-weight-bold">₹{{ number_format($salesEntry->calculated_net_amount, 2) }}</span></p>
                        </div>
                    </div>
                </div>
            </div>
            @endif

            @if($salesEntry->notes)
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Notes</h6>
                </div>
                <div class="card-body">
                    <p>{{ $salesEntry->notes }}</p>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection
