@extends('layouts.app')

@section('title', 'Quatation Entry')

@push('styles')
<style>
    .page-header {
        background: #224abe;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        margin-bottom: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .page-header h1 {
        font-size: 1.5rem;
        margin-bottom: 5px;
    }

    .page-header p {
        font-size: 0.85rem;
        margin-bottom: 0;
    }

    .form-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .form-header {
        background: var(--primary-color);
        color: white;
        padding: 12px 20px;
        font-weight: 600;
        font-size: 0.95rem;
    }

    .form-body {
        padding: 20px;
    }

    .section-title {
        background: #f8f9fc;
        color: var(--dark-color);
        padding: 8px 15px;
        margin: 15px -20px;
        font-weight: 600;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        border-left: 4px solid var(--primary-color);
    }

    .form-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }

    .form-group {
        margin-bottom: 18px;
    }

    .form-label {
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 5px;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.3px;
    }

    .form-control {
        border: 1px solid #e3e6f0;
        border-radius: 6px;
        padding: 8px 12px;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        background: #f8f9fc;
        width: 100%;
    }

    .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.15rem rgba(78, 115, 223, 0.2);
        background: white;
        outline: none;
    }

    .form-select {
        border: 1px solid #e3e6f0;
        border-radius: 6px;
        padding: 8px 12px;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        background: #f8f9fc;
        width: 100%;
    }

    .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.15rem rgba(78, 115, 223, 0.2);
        background: white;
        outline: none;
    }

    .required {
        color: var(--danger-color);
    }

    .breadcrumb-container {
        background: white;
        border-radius: 6px;
        padding: 10px 15px;
        margin-bottom: 15px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .breadcrumb {
        margin: 0;
        background: none;
        padding: 0;
        font-size: 0.85rem;
    }

    .breadcrumb-item a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
    }

    .breadcrumb-item a:hover {
        text-decoration: underline;
    }

    .breadcrumb-item.active {
        color: var(--secondary-color);
    }

    /* Product Table Styles */
    .products-section {
        margin-top: 20px;
    }

    .products-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 0.85rem;
        margin-top: 10px;
        table-layout: fixed;
    }

    .products-container {
        width: 100%;
        overflow: visible;
        margin-top: 10px;
        border: 1px solid #e3e6f0;
        border-radius: 6px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .products-table th {
        background: var(--primary-color);
        color: white;
        padding: 8px 4px;
        text-align: center;
        font-weight: 600;
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        border: 1px solid #224abe;
        white-space: nowrap;
        vertical-align: middle;
    }

    .products-table thead tr:nth-child(2) th {
        background: #f8f9fc;
        color: var(--primary-color);
        border-top: 1px solid #e3e6f0;
    }

    .products-table td {
        padding: 6px;
        border: 1px solid #e3e6f0;
        vertical-align: middle;
    }

    .products-table input,
    .products-table select {
        width: 100%;
        border: 1px solid #e3e6f0;
        border-radius: 4px;
        padding: 6px 8px;
        font-size: 0.85rem;
        background: white;
        min-width: 60px;
        text-align: center;
    }

    .products-table input[type="text"],
    .products-table textarea {
        text-align: left;
    }

    .products-table select {
        text-align: left;
        min-width: 120px;
    }

    .products-table input[type="number"] {
        text-align: right;
        min-width: 80px;
    }

    .products-table input:focus,
    .products-table select:focus {
        border-color: var(--primary-color);
        outline: none;
        box-shadow: 0 0 0 0.1rem rgba(78, 115, 223, 0.2);
        background: #f8f9fc;
    }

    .products-table input[readonly] {
        background: #f8f9fc;
        color: #6c757d;
        font-weight: 600;
    }

    .btn-add-row {
        background: var(--success-color);
        border: none;
        color: white;
        padding: 6px 12px;
        border-radius: 4px;
        font-weight: 600;
        font-size: 0.8rem;
        margin-top: 10px;
        transition: all 0.3s ease;
    }

    .btn-add-row:hover {
        background: #1cc88a;
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(28, 200, 138, 0.4);
    }

    .btn-remove-row {
        background: var(--danger-color);
        border: none;
        color: white;
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 0.7rem;
        transition: all 0.3s ease;
    }

    .btn-remove-row:hover {
        background: #c82333;
        transform: translateY(-1px);
    }

    .action-buttons {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
        margin-top: 20px;
        padding-top: 20px;
        border-top: 2px solid #e3e6f0;
    }

    .btn-submit {
        background: var(--primary-color);
        border: none;
        color: white;
        padding: 10px 25px;
        border-radius: 6px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        transition: all 0.3s ease;
        font-size: 0.9rem;
    }

    .btn-submit:hover {
        background: #224abe;
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(78, 115, 223, 0.4);
        color: white;
    }

    .btn-cancel {
        background: var(--secondary-color);
        border: none;
        color: white;
        padding: 10px 25px;
        border-radius: 6px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        transition: all 0.3s ease;
        font-size: 0.9rem;
    }

    .btn-cancel:hover {
        background: #6c757d;
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(133, 135, 150, 0.4);
        color: white;
    }

    .btn-draft {
        background: var(--warning-color);
        border: none;
        color: white;
        padding: 10px 25px;
        border-radius: 6px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        transition: all 0.3s ease;
        font-size: 0.9rem;
    }

    .btn-draft:hover {
        background: #dda20a;
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(255, 193, 7, 0.4);
        color: white;
    }

    .totals-section {
        background: #f8f9fc;
        padding: 15px;
        border-radius: 6px;
        margin-top: 15px;
        border: 2px solid #e3e6f0;
    }

    .totals-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        font-size: 0.9rem;
    }

    .totals-row.grand-total {
        font-weight: 700;
        font-size: 1.1rem;
        color: var(--primary-color);
        border-top: 2px solid var(--primary-color);
        padding-top: 8px;
        margin-top: 10px;
    }

    .totals-label {
        font-weight: 600;
        color: var(--dark-color);
    }

    .totals-value {
        font-weight: 600;
        color: var(--primary-color);
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .form-row {
            grid-template-columns: 1fr;
        }

        .products-table {
            font-size: 0.75rem;
            min-width: 1600px;
        }

        .products-table th,
        .products-table td {
            padding: 6px 4px;
        }

        .products-table input,
        .products-table select {
            padding: 4px 6px;
            font-size: 0.75rem;
        }
    }

    /* Table row styling */

    .products-table tr:nth-child(even) {
        background: #f8f9fc;
    }

    .products-table tr:nth-child(odd) {
        background: white;
    }

    /* Improve totals section */
    .totals-section {
        background: linear-gradient(135deg, #f8f9fc 0%, #e3e6f0 100%);
        padding: 20px;
        border-radius: 8px;
        margin-top: 20px;
        border: 2px solid var(--primary-color);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .totals-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 15px;
        margin-bottom: 15px;
        padding: 15px;
        background: white;
        border-radius: 6px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .totals-row {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 10px;
        border-radius: 6px;
        background: #f8f9fc;
        border: 1px solid #e3e6f0;
    }

    .totals-label {
        font-weight: 600;
        color: var(--dark-color);
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        margin-bottom: 8px;
    }

    .totals-value {
        font-weight: 700;
        color: var(--primary-color);
        font-size: 1rem;
    }

    .totals-input {
        width: 100%;
        max-width: 120px;
        text-align: center;
        font-weight: 600;
        border: 1px solid #e3e6f0;
        border-radius: 4px;
        padding: 6px 8px;
        font-size: 0.9rem;
        background: white;
    }

    .totals-input:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.1rem rgba(78, 115, 223, 0.2);
        outline: none;
    }

    .totals-value.grand-total {
        font-weight: 700;
        font-size: 1.2rem;
        color: var(--success-color);
        background: linear-gradient(135deg, #1cc88a 0%, #17a673 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .tax-details-section {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 15px;
        padding: 15px;
        background: #f8f9fc;
        border-radius: 6px;
        border: 1px solid #e3e6f0;
    }

    .tax-details-section .totals-row {
        background: white;
        border: 1px solid #e3e6f0;
    }
</style>
@endpush

@section('content')
<!-- Breadcrumb -->
<div class="breadcrumb-container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="#">Entries</a></li>
            <li class="breadcrumb-item active" aria-current="page">Quatation Entry</li>
        </ol>
    </nav>
</div>

<!-- Page Header -->
<div class="page-header">
    <h1><i class="fas fa-file-invoice-dollar"></i> Quatation Entry</h1>
    <p>Create new Quatation invoice with product details and tax calculations</p>
</div>

<!-- Form Container -->
<div class="form-container">
    <div class="form-header">
        <i class="fas fa-plus-circle"></i> New Quatation Invoice
    </div>

    <div class="form-body">
        <form id="salesForm" method="POST" action="{{ route('entries.sales.store') }}">
            @csrf

            <!-- Invoice Information Section -->
            <div class="section-title">
                <i class="fas fa-info-circle"></i> Quatations Information
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="invoice_no" class="form-label">Quatations No <span class="required">*</span></label>
                    <input type="text" class="form-control" id="invoice_no" value="{{ $nextQuotationNumber ?? 'Auto-generated' }}" readonly>
                    <small class="form-text text-muted">Auto-generated quotation number (will be assigned when saved)</small>
                </div>

                <div class="form-group">
                    <label for="invoice_date" class="form-label">Quatations Date <span class="required">*</span></label>
                    <input type="date" class="form-control" id="invoice_date" name="invoice_date" required value="{{ date('Y-m-d') }}">
                </div>

                <div class="form-group">
                    <label for="payment_terms" class="form-label">Payment Terms <span class="required">*</span></label>
                    <select class="form-select" id="payment_terms" name="payment_terms" required>
                        <option value="cash">Cash</option>
                        <option value="credit_7">Credit - 7 Days</option>
                        <option value="credit_15">Credit - 15 Days</option>
                        <option value="credit_30">Credit - 30 Days</option>
                        <option value="credit_45">Credit - 45 Days</option>
                        <option value="credit_60">Credit - 60 Days</option>
                    </select>
                </div>
            </div>

            <!-- Customer Information Section -->
            <div class="section-title">
                <i class="fas fa-user"></i> Customer Information
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="billed_to" class="form-label">Billed To <span class="required">*</span></label>
                    <select class="form-select" id="billed_to" name="billed_to" required onchange="loadCustomerDetails(this.value)">
                        <option value="">Select Customer</option>
                        @foreach($customers as $customer)
                            <option value="{{ $customer->id }}"
                                    data-name="{{ $customer->name }}"
                                    data-mobile="{{ $customer->phone }}"
                                    data-email="{{ $customer->email }}">
                                {{ $customer->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div class="form-group">
                    <label for="party_name" class="form-label">Party Name <span class="required">*</span></label>
                    <input type="text" class="form-control" id="party_name" name="party_name" required>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="mobile" class="form-label">Mobile <span class="required">*</span></label>
                    <input type="tel" class="form-control" id="mobile" name="mobile" required pattern="[0-9]{10}">
                </div>

                <div class="form-group">
                    <label for="email" class="form-label">Email</label>
                    <input type="email" class="form-control" id="email" name="email">
                </div>
            </div>
            <!-- Products Section -->
            <div class="section-title">
                <i class="fas fa-boxes"></i> Product Details
            </div>

            <div class="products-section">
                <button type="button" class="btn btn-add-row" onclick="addProductRow()">
                    <i class="fas fa-plus"></i> Add Product Row
                </button>

                <div class="products-container">
                    <table class="products-table" id="productsTable">
                        <thead>
                            <tr>
                                <th style="width: 15%;">Product</th>
                                <th style="width: 8%;">HSN Code</th>
                                <th style="width: 6%;">GST %</th>
                                <th style="width: 6%;">Pkgs</th>
                                <th style="width: 6%;">Unit</th>
                                <th style="width: 8%;">Rate</th>
                                <th style="width: 8%;">Loading</th>
                                <th style="width: 9%;">Basic Rate</th>
                                <th style="width: 9%;">Total</th>
                                <th style="width: 6%;">Action</th>
                            </tr>
                            <tr>
                                <th style="width: 15%;">Description</th>
                                <th style="width: 8%;">Taxable Value</th>
                                <th style="width: 6%;">CGST %</th>
                                <th style="width: 6%;">CGST Amt</th>
                                <th style="width: 6%;">SGST %</th>
                                <th style="width: 8%;">SGST Amt</th>
                                <th style="width: 8%;">IGST %</th>
                                <th style="width: 9%;">IGST Amt</th>
                                <th style="width: 9%;">Tax Details</th>
                                <th style="width: 6%;"></th>
                            </tr>
                        </thead>
                        <tbody id="productsTableBody">
                            <!-- Product rows will be added here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Totals Section -->
            <div class="totals-section" style="background: red; padding: 20px; margin: 20px 0; border: 3px solid blue;">
                <h3 style="color: white; background: black; padding: 10px;">ADDITIONAL FIELDS SECTION</h3>
                <!-- First Row: Basic Totals -->
                <div class="totals-grid">
                    <div class="totals-row">
                        <span class="totals-label">Total:</span>
                        <span class="totals-value" id="subtotal">₹0.00</span>
                    </div>
                    <div class="totals-row">
                        <span class="totals-label">Ins. P/MT:</span>
                        <input type="number" class="form-control totals-input" id="ins_pmt" name="ins_pmt" step="0.01" value="0" onchange="calculateFinalTotals()">
                    </div>
                    <div class="totals-row">
                        <span class="totals-label">Insurance:</span>
                        <input type="number" class="form-control totals-input" id="insurance" name="insurance" step="0.01" value="0" onchange="calculateFinalTotals()">
                    </div>
                    <div class="totals-row">
                        <span class="totals-label">Frt. Advance:</span>
                        <input type="number" class="form-control totals-input" id="frt_advance" name="frt_advance" step="0.01" value="0" onchange="calculateFinalTotals()">
                    </div>
                </div>

                <!-- Second Row: Tax and Final Totals -->
                <div class="totals-grid">
                    <div class="totals-row">
                        <span class="totals-label">Grand Total:</span>
                        <span class="totals-value" id="grand_total">₹0.00</span>
                    </div>
                    <div class="totals-row">
                        <span class="totals-label">TCS %:</span>
                        <input type="number" class="form-control totals-input" id="tcs_percent" name="tcs_percent" step="0.01" value="0" onchange="calculateFinalTotals()">
                    </div>
                    <div class="totals-row">
                        <span class="totals-label">TCS Amt.:</span>
                        <span class="totals-value" id="tcs_amount">₹0.00</span>
                    </div>
                    <div class="totals-row">
                        <span class="totals-label">Net Amt.:</span>
                        <span class="totals-value grand-total" id="net_amount">₹0.00</span>
                    </div>
                </div>

                <!-- Tax Details Section -->
                <div class="tax-details-section">
                    <div class="totals-row">
                        <span class="totals-label">Total CGST:</span>
                        <span class="totals-value" id="total_cgst">₹0.00</span>
                    </div>
                    <div class="totals-row">
                        <span class="totals-label">Total SGST:</span>
                        <span class="totals-value" id="total_sgst">₹0.00</span>
                    </div>
                    <div class="totals-row">
                        <span class="totals-label">Total IGST:</span>
                        <span class="totals-value" id="total_igst">₹0.00</span>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <button type="button" class="btn btn-cancel" onclick="window.history.back()">
                    <i class="fas fa-times"></i> Cancel
                </button>
                <button type="button" class="btn btn-draft" onclick="saveDraft()">
                    <i class="fas fa-save"></i> Save as Draft
                </button>
                <button type="submit" class="btn btn-submit">
                    <i class="fas fa-check"></i> Save Invoice
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script>
    let rowCounter = 0;
    const products = @json($products);

    // Load customer details when customer is selected
    function loadCustomerDetails(customerId) {
        if (customerId) {
            const select = document.getElementById('billed_to');
            const selectedOption = select.options[select.selectedIndex];

            document.getElementById('party_name').value = selectedOption.getAttribute('data-name') || '';
            document.getElementById('mobile').value = selectedOption.getAttribute('data-mobile') || '';
            document.getElementById('email').value = selectedOption.getAttribute('data-email') || '';
        } else {
            document.getElementById('party_name').value = '';
            document.getElementById('mobile').value = '';
            document.getElementById('email').value = '';
        }
    }

    // Add product row (two-row layout)
    function addProductRow() {
        rowCounter++;
        const tbody = document.getElementById('productsTableBody');

        let productOptions = '<option value="">Select Product</option>';
        products.forEach(product => {
            productOptions += `<option value="${product.id}"
                                      data-hsn="${product.hsn_code || ''}"
                                      data-gst="${product.gst_percent || 18}"
                                      data-unit="${product.unit || 'PCS'}"
                                      data-rate="${product.rate || 0}">
                                ${product.name}
                              </option>`;
        });

        // First row - Basic product info
        const row1 = document.createElement('tr');
        row1.id = `row_${rowCounter}`;
        row1.innerHTML = `
            <td>
                <select name="products[${rowCounter}][product_id]" class="form-select" onchange="loadProductDetails(this, ${rowCounter})">
                    ${productOptions}
                </select>
            </td>
            <td><input type="text" name="products[${rowCounter}][hsn_code]" class="form-control" readonly></td>
            <td><input type="number" name="products[${rowCounter}][gst_percent]" class="form-control" step="0.01" readonly></td>
            <td><input type="number" name="products[${rowCounter}][packages]" class="form-control" step="1" min="0" onchange="calculateRow(${rowCounter})"></td>
            <td><input type="text" name="products[${rowCounter}][unit]" class="form-control" readonly></td>
            <td><input type="number" name="products[${rowCounter}][rate]" class="form-control" step="0.01" min="0" onchange="calculateRow(${rowCounter})"></td>
            <td><input type="number" name="products[${rowCounter}][loading]" class="form-control" step="0.01" min="0" value="0" onchange="calculateRow(${rowCounter})"></td>
            <td><input type="number" name="products[${rowCounter}][basic_rate]" class="form-control" step="0.01" readonly></td>
            <td><input type="number" name="products[${rowCounter}][total]" class="form-control" step="0.01" readonly></td>
            <td>
                <button type="button" class="btn btn-remove-row" onclick="removeRow(${rowCounter})">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;

        // Second row - Tax details and description
        const row2 = document.createElement('tr');
        row2.id = `row_${rowCounter}_details`;
        row2.innerHTML = `
            <td><input type="text" name="products[${rowCounter}][description]" class="form-control" placeholder="Description"></td>
            <td><input type="number" name="products[${rowCounter}][taxable_value]" class="form-control" step="0.01" readonly></td>
            <td><input type="number" name="products[${rowCounter}][cgst_percent]" class="form-control" step="0.01" readonly></td>
            <td><input type="number" name="products[${rowCounter}][cgst_amount]" class="form-control" step="0.01" readonly></td>
            <td><input type="number" name="products[${rowCounter}][sgst_percent]" class="form-control" step="0.01" readonly></td>
            <td><input type="number" name="products[${rowCounter}][sgst_amount]" class="form-control" step="0.01" readonly></td>
            <td><input type="number" name="products[${rowCounter}][igst_percent]" class="form-control" step="0.01" readonly></td>
            <td><input type="number" name="products[${rowCounter}][igst_amount]" class="form-control" step="0.01" readonly></td>
            <td style="background: #f8f9fc; text-align: center; font-size: 0.75rem; color: #666;">Tax Details</td>
            <td></td>
        `;

        tbody.appendChild(row1);
        tbody.appendChild(row2);
    }

    // Remove product row (both main and details rows)
    function removeRow(rowId) {
        const row1 = document.getElementById(`row_${rowId}`);
        const row2 = document.getElementById(`row_${rowId}_details`);
        if (row1) {
            row1.remove();
        }
        if (row2) {
            row2.remove();
        }
        calculateTotals();
    }

    // Load product details when product is selected
    function loadProductDetails(select, rowId) {
        const productId = select.value;
        if (productId) {
            const selectedOption = select.options[select.selectedIndex];
            const row = document.getElementById(`row_${rowId}`);
            const inputs = row.querySelectorAll('input');

            // Populate product data from selected option
            inputs[0].value = selectedOption.getAttribute('data-hsn') || ''; // HSN Code
            const gstPercent = parseFloat(selectedOption.getAttribute('data-gst')) || 18;
            inputs[1].value = gstPercent; // GST %
            inputs[4].value = selectedOption.getAttribute('data-unit') || 'PCS'; // Unit
            inputs[5].value = selectedOption.getAttribute('data-rate') || 0; // Rate

            // Calculate CGST and SGST (split GST equally for intra-state)
            const cgstPercent = gstPercent / 2;
            const sgstPercent = gstPercent / 2;
            const igstPercent = 0; // For inter-state transactions

            inputs[11].value = cgstPercent; // CGST %
            inputs[13].value = sgstPercent; // SGST %
            inputs[15].value = igstPercent; // IGST %

            // Trigger calculation
            calculateRow(rowId);
        } else {
            // Clear all fields if no product selected
            const row = document.getElementById(`row_${rowId}`);
            const inputs = row.querySelectorAll('input');
            inputs.forEach(input => {
                if (!input.readOnly) {
                    input.value = '';
                }
            });
        }
    }

    // Calculate row totals
    function calculateRow(rowId) {
        const row = document.getElementById(`row_${rowId}`);
        const inputs = row.querySelectorAll('input');

        const packages = parseFloat(inputs[2].value) || 0;
        const rate = parseFloat(inputs[4].value) || 0;
        const loading = parseFloat(inputs[5].value) || 0;
        const gstPercent = parseFloat(inputs[1].value) || 0;
        const cgstPercent = parseFloat(inputs[11].value) || 0;
        const sgstPercent = parseFloat(inputs[13].value) || 0;
        const igstPercent = parseFloat(inputs[15].value) || 0;

        // Calculate basic rate
        const basicRate = rate + loading;
        inputs[6].value = basicRate.toFixed(2);

        // Calculate total
        const total = packages * basicRate;
        inputs[7].value = total.toFixed(2);

        // Calculate taxable value (same as total for now)
        const taxableValue = total;
        inputs[8].value = taxableValue.toFixed(2);

        // Calculate tax amounts
        const cgstAmount = (taxableValue * cgstPercent) / 100;
        const sgstAmount = (taxableValue * sgstPercent) / 100;
        const igstAmount = (taxableValue * igstPercent) / 100;

        inputs[12].value = cgstAmount.toFixed(2);
        inputs[14].value = sgstAmount.toFixed(2);
        inputs[16].value = igstAmount.toFixed(2);

        calculateTotals();
    }

    // Calculate grand totals
    function calculateTotals() {
        let subtotal = 0;
        let totalCgst = 0;
        let totalSgst = 0;
        let totalIgst = 0;

        const rows = document.querySelectorAll('#productsTableBody tr');
        rows.forEach(row => {
            const inputs = row.querySelectorAll('input');
            if (inputs.length > 8) { // Make sure it's a main row, not details row
                subtotal += parseFloat(inputs[8]?.value) || 0; // Taxable value
                totalCgst += parseFloat(inputs[12]?.value) || 0; // CGST amount
                totalSgst += parseFloat(inputs[14]?.value) || 0; // SGST amount
                totalIgst += parseFloat(inputs[16]?.value) || 0; // IGST amount
            }
        });

        document.getElementById('subtotal').textContent = `₹${subtotal.toFixed(2)}`;
        document.getElementById('total_cgst').textContent = `₹${totalCgst.toFixed(2)}`;
        document.getElementById('total_sgst').textContent = `₹${totalSgst.toFixed(2)}`;
        document.getElementById('total_igst').textContent = `₹${totalIgst.toFixed(2)}`;

        // Calculate final totals
        calculateFinalTotals();
    }

    // Calculate final totals with additional charges
    function calculateFinalTotals() {
        const subtotal = parseFloat(document.getElementById('subtotal').textContent.replace('₹', '').replace(',', '')) || 0;
        const totalCgst = parseFloat(document.getElementById('total_cgst').textContent.replace('₹', '').replace(',', '')) || 0;
        const totalSgst = parseFloat(document.getElementById('total_sgst').textContent.replace('₹', '').replace(',', '')) || 0;
        const totalIgst = parseFloat(document.getElementById('total_igst').textContent.replace('₹', '').replace(',', '')) || 0;

        // Get additional charges
        const insPmt = parseFloat(document.getElementById('ins_pmt').value) || 0;
        const insurance = parseFloat(document.getElementById('insurance').value) || 0;
        const frtAdvance = parseFloat(document.getElementById('frt_advance').value) || 0;
        const tcsPercent = parseFloat(document.getElementById('tcs_percent').value) || 0;

        // Calculate grand total (before TCS)
        const grandTotal = subtotal + totalCgst + totalSgst + totalIgst + insPmt + insurance + frtAdvance;

        // Calculate TCS amount
        const tcsAmount = (grandTotal * tcsPercent) / 100;

        // Calculate net amount (final total)
        const netAmount = grandTotal + tcsAmount;

        // Update display
        document.getElementById('grand_total').textContent = `₹${grandTotal.toFixed(2)}`;
        document.getElementById('tcs_amount').textContent = `₹${tcsAmount.toFixed(2)}`;
        document.getElementById('net_amount').textContent = `₹${netAmount.toFixed(2)}`;
    }

    // Save as draft
    function saveDraft() {
        const form = document.getElementById('salesForm');
        const draftInput = document.createElement('input');
        draftInput.type = 'hidden';
        draftInput.name = 'status';
        draftInput.value = 'draft';
        form.appendChild(draftInput);
        form.submit();
    }

    // Initialize with one product row
    document.addEventListener('DOMContentLoaded', function() {
        addProductRow();
    });

    // Auto-generate invoice number
    function generateInvoiceNumber() {
        const year = new Date().getFullYear();
        const random = Math.floor(Math.random() * 9999) + 1;
        return `INV-${year}-${String(random).padStart(4, '0')}`;
    }
</script>
@endpush
