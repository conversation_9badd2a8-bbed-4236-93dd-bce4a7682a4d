<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quotation {{ $salesEntry->quotation_number }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #000;
            background: #fff;
            padding: 20px;
        }
        
        .container {
            max-width: 100%;
            margin: 0 auto;
        }
        
        /* Header */
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #000;
            padding-bottom: 15px;
        }
        
        .company-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
            text-transform: uppercase;
        }
        
        .company-details {
            font-size: 11px;
            margin-bottom: 10px;
        }
        
        .document-title {
            font-size: 18px;
            font-weight: bold;
            margin-top: 15px;
            text-decoration: underline;
        }
        
        /* Details Section */
        .details-section {
            display: table;
            width: 100%;
            margin-bottom: 25px;
        }
        
        .details-left, .details-right {
            display: table-cell;
            width: 50%;
            vertical-align: top;
            padding: 10px;
        }
        
        .details-left {
            border-right: 1px solid #ccc;
        }
        
        .detail-group {
            margin-bottom: 15px;
        }
        
        .detail-title {
            font-weight: bold;
            font-size: 13px;
            margin-bottom: 8px;
            text-decoration: underline;
        }
        
        .detail-row {
            margin-bottom: 4px;
        }
        
        .detail-label {
            font-weight: bold;
            display: inline-block;
            width: 80px;
        }
        
        .detail-value {
            display: inline-block;
        }
        
        /* Items Table */
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            border: 2px solid #000;
        }
        
        .items-table th {
            background: #f0f0f0;
            border: 1px solid #000;
            padding: 8px 6px;
            text-align: left;
            font-weight: bold;
            font-size: 11px;
        }
        
        .items-table td {
            border: 1px solid #000;
            padding: 6px;
            font-size: 11px;
            vertical-align: top;
        }
        
        .items-table tr:nth-child(even) {
            background: #f9f9f9;
        }
        
        .text-right {
            text-align: right;
        }
        
        .text-center {
            text-align: center;
        }
        
        /* Totals Section */
        .totals-section {
            float: right;
            width: 300px;
            margin-top: 15px;
            border: 1px solid #000;
        }
        
        .totals-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .totals-table td {
            padding: 6px 10px;
            border-bottom: 1px solid #ccc;
            font-size: 11px;
        }
        
        .totals-table .total-label {
            font-weight: bold;
            text-align: left;
        }
        
        .totals-table .total-value {
            text-align: right;
            font-weight: bold;
        }
        
        .grand-total {
            background: #f0f0f0;
            font-weight: bold;
            font-size: 12px;
            border-top: 2px solid #000;
        }
        
        /* Footer */
        .footer {
            clear: both;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ccc;
            font-size: 10px;
            text-align: center;
        }
        
        .terms {
            margin-top: 30px;
            font-size: 10px;
            text-align: left;
        }
        
        .terms-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        /* Watermark */
        .watermark {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            opacity: 0.1;
            z-index: -1;
            pointer-events: none;
        }

        /* Print Styles */
        @media print {
            body {
                padding: 0;
            }

            .container {
                max-width: none;
            }
        }
    </style>
</head>
<body>
    @if(isset($company) && $company && $company->watermark && (isset($pdfSettings) && ($pdfSettings['show_watermark'] ?? false)))
    <div class="watermark">
        <img src="{{ public_path('storage/' . $company->watermark) }}" alt="Watermark" style="opacity: {{ $pdfSettings['watermark_opacity'] ?? 0.1 }}; width: 300px; height: auto;">
    </div>
    @endif

    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="company-name">{{ $salesEntry->company->name ?? 'Company Name' }}</div>
            @if($salesEntry->company)
            <div class="company-details">
                @if($salesEntry->company->address){{ $salesEntry->company->address }}<br>@endif
                @if($salesEntry->company->city || $salesEntry->company->state)
                    @if($salesEntry->company->city){{ $salesEntry->company->city->name }}@endif
                    @if($salesEntry->company->city && $salesEntry->company->state), @endif
                    @if($salesEntry->company->state){{ $salesEntry->company->state->name }}@endif
                    @if($salesEntry->company->pincode) - {{ $salesEntry->company->pincode }}@endif
                    <br>
                @endif
                @if($salesEntry->company->phone)Phone: {{ $salesEntry->company->phone }}@endif
                @if($salesEntry->company->phone && $salesEntry->company->email) | @endif
                @if($salesEntry->company->email)Email: {{ $salesEntry->company->email }}@endif
                @if($salesEntry->company->gst_number)<br>GST No: {{ $salesEntry->company->gst_number }}@endif
            </div>
            @endif
            <div class="document-title">QUOTATION</div>
        </div>

        <!-- Details Section -->
        <div class="details-section">
            <div class="details-left">
                <div class="detail-group">
                    <div class="detail-title">Bill To:</div>
                    <div class="detail-row">
                        <strong>{{ $salesEntry->customer->name ?? 'Customer Name' }}</strong>
                    </div>
                    @if($salesEntry->customer && $salesEntry->customer->address)
                    <div class="detail-row">{{ $salesEntry->customer->address }}</div>
                    @endif
                    @if($salesEntry->customer && ($salesEntry->customer->city || $salesEntry->customer->state))
                    <div class="detail-row">
                        @if($salesEntry->customer->city){{ $salesEntry->customer->city->name }}@endif
                        @if($salesEntry->customer->city && $salesEntry->customer->state), @endif
                        @if($salesEntry->customer->state){{ $salesEntry->customer->state->name }}@endif
                        @if($salesEntry->customer->pincode) - {{ $salesEntry->customer->pincode }}@endif
                    </div>
                    @endif
                    @if($salesEntry->customer && $salesEntry->customer->phone)
                    <div class="detail-row">Phone: {{ $salesEntry->customer->phone }}</div>
                    @endif
                    @if($salesEntry->customer && $salesEntry->customer->email)
                    <div class="detail-row">Email: {{ $salesEntry->customer->email }}</div>
                    @endif
                    @if($salesEntry->customer && $salesEntry->customer->gst_number)
                    <div class="detail-row">GST No: {{ $salesEntry->customer->gst_number }}</div>
                    @endif
                </div>
            </div>
            
            <div class="details-right">
                <div class="detail-group">
                    <div class="detail-row">
                        <span class="detail-label">Quote No:</span>
                        <span class="detail-value">{{ $salesEntry->quotation_number }}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Date:</span>
                        <span class="detail-value">{{ $salesEntry->quotation_date->format('d/m/Y') }}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Valid Until:</span>
                        <span class="detail-value">{{ $salesEntry->valid_until->format('d/m/Y') }}</span>
                    </div>
                    @if($salesEntry->reference_number)
                    <div class="detail-row">
                        <span class="detail-label">Reference:</span>
                        <span class="detail-value">{{ $salesEntry->reference_number }}</span>
                    </div>
                    @endif
                    @if($salesEntry->payment_terms)
                    <div class="detail-row">
                        <span class="detail-label">Payment:</span>
                        <span class="detail-value">{{ ucfirst(str_replace('_', ' ', $salesEntry->payment_terms)) }}</span>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Items Table -->
        @if($salesEntry->items && $salesEntry->items->count() > 0)
        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 5%;">Sr.</th>
                    <th style="width: 45%;">Description</th>
                    <th style="width: 10%;">HSN</th>
                    <th style="width: 8%;">Qty</th>
                    <th style="width: 8%;">Unit</th>
                    <th style="width: 12%;">Rate</th>
                    <th style="width: 12%;">Amount</th>
                </tr>
            </thead>
            <tbody>
                @foreach($salesEntry->items as $index => $item)
                <tr>
                    <td class="text-center">{{ $index + 1 }}</td>
                    <td>
                        <strong>{{ $item->product->name ?? $item->description }}</strong>
                        @if($item->description && $item->product)
                        <br><small>{{ $item->description }}</small>
                        @endif
                    </td>
                    <td class="text-center">{{ $item->product->hsn_code ?? '-' }}</td>
                    <td class="text-center">{{ $item->packages ?? $item->quantity }}</td>
                    <td class="text-center">{{ $item->product->unit ?? $item->unit ?? 'Nos' }}</td>
                    <td class="text-right">₹{{ number_format($item->rate ?? $item->unit_price, 2) }}</td>
                    <td class="text-right">₹{{ number_format($item->total ?? $item->line_total, 2) }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
        @endif

        <!-- Totals Section -->
        <div class="totals-section">
            <table class="totals-table">
                <tr>
                    <td class="total-label">Subtotal:</td>
                    <td class="total-value">₹{{ number_format($salesEntry->subtotal ?? 0, 2) }}</td>
                </tr>
                @if($salesEntry->discount_amount > 0)
                <tr>
                    <td class="total-label">Discount:</td>
                    <td class="total-value">-₹{{ number_format($salesEntry->discount_amount, 2) }}</td>
                </tr>
                @endif
                @if($salesEntry->cgst_amount > 0)
                <tr>
                    <td class="total-label">CGST:</td>
                    <td class="total-value">₹{{ number_format($salesEntry->cgst_amount, 2) }}</td>
                </tr>
                @endif
                @if($salesEntry->sgst_amount > 0)
                <tr>
                    <td class="total-label">SGST:</td>
                    <td class="total-value">₹{{ number_format($salesEntry->sgst_amount, 2) }}</td>
                </tr>
                @endif
                @if($salesEntry->igst_amount > 0)
                <tr>
                    <td class="total-label">IGST:</td>
                    <td class="total-value">₹{{ number_format($salesEntry->igst_amount, 2) }}</td>
                </tr>
                @endif
                <tr class="grand-total">
                    <td class="total-label">TOTAL:</td>
                    <td class="total-value">₹{{ number_format($salesEntry->calculated_total, 2) }}</td>
                </tr>
            </table>
        </div>

        <!-- Terms and Conditions -->
        <div class="terms">
            <div class="terms-title">Terms & Conditions:</div>
            <div>1. Payment terms as agreed.</div>
            <div>2. Goods once sold will not be taken back.</div>
            <div>3. Subject to local jurisdiction only.</div>
            @if($salesEntry->notes)
            <div style="margin-top: 10px;"><strong>Notes:</strong> {{ $salesEntry->notes }}</div>
            @endif
        </div>

        <!-- Footer -->
        <div class="footer">
            <div>Thank you for your business!</div>
            <div style="margin-top: 5px;">This is a computer generated quotation.</div>
        </div>
    </div>
</body>
</html>
