@extends('layouts.app')

@section('title', 'Sales Entry - JMD Traders')
@section('page-title', 'Sales Entry - Quotation Management')

@section('content')

<!-- Search and Filters -->

<!-- Statistics Cards -->
<div class="row mt-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Quotations
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total'] }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-file-invoice fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Pending
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['pending'] }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Approved
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['approved'] }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Total Value
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">₹{{ number_format($stats['total_value'], 2) }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-rupee-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Sales Entries / Quotations</h6>
                <div class="btn-group">
                    @permission('sales.create')
                    <a href="{{ route('entries.sales.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> New Quotation
                    </a>
                    @endpermission

                    @permission('sales.view')
                    <button type="button" class="btn btn-success" onclick="exportSales()">
                        <i class="fas fa-file-excel"></i> Export Excel
                    </button>
                    <button type="button" class="btn btn-info ml-2" onclick="exportSalesCSV()">
                        <i class="fas fa-file-csv"></i> Export CSV
                    </button>
                    @endpermission
                </div>
            </div>
            <div class="card-body">
                <!-- Filter Section -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <select class="form-select myselect" id="statusFilter" data-placeholder="Filter by status" data-allow-clear="true">
                            <option value="">All Status</option>
                            <option value="draft">Draft</option>
                            <option value="pending">Pending</option>
                            <option value="approved">Approved</option>
                            <option value="rejected">Rejected</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select myselect" id="customerFilter" data-placeholder="Filter by customer" data-allow-clear="true">
                            <option value="">All Customers</option>
                            @foreach($customers as $customer)
                                <option value="{{ $customer->id }}">{{ $customer->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-3">
                        <div class="date-input-wrapper">
                            <input type="date" class="form-control datepicker" id="dateFilter" placeholder="Filter by date">
                            <i class="fas fa-calendar-alt calendar-icon"></i>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-outline-secondary" onclick="clearFilters()">
                            <i class="fas fa-times"></i> Clear Filters
                        </button>


                    </div>
                </div>

                <!-- Results Summary -->
                @if(request()->hasAny(['search', 'customer_id', 'status', 'date_from', 'date_to', 'company_id']))
                <div class="alert alert-info mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>{{ $salesEntries->total() }}</strong> results found
                            @if(request('search'))
                                for "<strong>{{ request('search') }}</strong>"
                            @endif
                            @if(request()->hasAny(['customer_id', 'status', 'date_from', 'date_to', 'company_id']))
                                with applied filters
                            @endif
                        </div>
                        <a href="{{ route('entries.sales') }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-times me-1"></i>
                            Clear All
                        </a>
                    </div>
                </div>
                @endif

                <!-- Sales Entries Table -->
                <div class="table-responsive">
                    <table class="table table-bordered" id="salesEntriesTable">
                        <thead>
                            <tr>
                                <th>Quotation #</th>
                                <th>Customer</th>
                                <th>Date</th>
                                <th>Valid Until</th>
                                <th>Total Amount</th>
                                <th>Status</th>
                                <th>Actions</th>
                                <th>Share</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($salesEntries as $entry)
                            <tr>
                                <td>{{ $entry->quotation_number }}</td>
                                <td>{{ $entry->customer->name }}</td>
                                <td>{{ $entry->quotation_date->format('d/m/Y') }}</td>
                                <td>
                                    {{ $entry->valid_until->format('d/m/Y') }}
                                    @if($entry->is_expired)
                                        <span class="badge bg-danger ms-1">Expired</span>
                                    @endif
                                </td>
                                <td>{{ $entry->formatted_total }}</td>
                                <td>
                                    <span class="badge bg-{{ $entry->status_badge }}">
                                        {{ ucfirst($entry->status) }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        @permission('sales.view')
                                        <a href="{{ route('entries.sales.show', $entry) }}" class="btn btn-sm btn-outline-primary" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @endpermission
                                            
                                        @permission('sales.edit')
                                        <a href="{{ route('entries.sales.edit', $entry) }}" class="btn btn-sm btn-outline-warning" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        @endpermission
                                           
                                        <button class="btn btn-sm btn-outline-info" onclick="printQuotation({{ $entry->id }})" title="Print PDF with Company Header">
                                            <i class="fas fa-file-pdf"></i>
                                        </button>
                                        @if($entry->status === 'approved')
                                        <a href="{{ route('entries.transport.create', ['sales_entry' => $entry->id]) }}" class="btn btn-sm btn-outline-success d-none" title="Create Transport">
                                            <i class="fas fa-truck"></i>
                                        </a>
                                        @endif
                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteEntry({{ $entry->id }})" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-sm btn-outline-success" onclick="openWhatsAppModal({{ $entry->id }}, '{{ addslashes($entry->customer->name) }}', '{{ $entry->quotation_number }}', '{{ $entry->formatted_total }}', '{{ $entry->customer->phone ?? '' }}')" title="Share PDF Invoice via WhatsApp">
                                            <i class="fab fa-whatsapp"></i>
                                            <i class="fas fa-file-pdf" style="font-size: 0.7em; margin-left: 2px;"></i>
                                        </button>
                                       
                                        <button class="btn btn-sm btn-outline-danger d-none" onclick="openGmailModal({{ $entry->id }}, '{{ addslashes($entry->customer->name) }}', '{{ $entry->quotation_number }}', '{{ $entry->formatted_total }}', '{{ $entry->customer->email ?? '' }}')" title="Share via Gmail">
                                            <i class="fas fa-envelope"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="8" class="text-center py-5">
                                    <div class="text-muted">
                                        <i class="fas fa-inbox fa-3x mb-3"></i>
                                        <h5>No sales entries found</h5>
                                        @if(request()->hasAny(['search', 'customer_id', 'status', 'date_from', 'date_to', 'company_id']))
                                            <p>Try adjusting your search criteria or <a href="{{ route('entries.sales') }}">clear all filters</a></p>
                                        @else
                                            <p>Get started by <a href="{{ route('entries.sales.create') }}">creating your first sales entry</a></p>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Custom Pagination -->
                @include('components.pagination', ['paginator' => $salesEntries])
            </div>
        </div>
    </div>
</div>

<!-- WhatsApp Share Modal -->
<div class="modal fade" id="whatsappModal" tabindex="-1" aria-labelledby="whatsappModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="whatsappModalLabel">
                    <i class="fab fa-whatsapp me-2"></i>Share Quotation via WhatsApp
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Phone Number -->
                <div class="mb-4">
                    <label class="form-label fw-bold">Phone Number:</label>
                    <div class="input-group">
                        <span class="input-group-text">+91</span>
                        <input type="tel" class="form-control" id="phoneNumber" placeholder="Enter phone number">
                    </div>
                    <small class="text-muted">Enter 10-digit mobile number</small>
                </div>

                <!-- Message Preview -->
                <div class="mb-4">
                    <label class="form-label fw-bold">Message Preview:</label>
                    <div class="card">
                        <div class="card-body bg-light">
                            <div class="d-flex align-items-start">
                                <div class="bg-success text-white rounded-circle p-2 me-3">
                                    <i class="fab fa-whatsapp"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <pre id="messagePreview" class="mb-0" style="white-space: pre-wrap; font-family: inherit; font-size: 0.9rem;"></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Custom Message -->
                <div class="mb-3">
                    <label class="form-label fw-bold">Customize Message (Optional):</label>
                    <textarea class="form-control" id="customMessage" rows="4" placeholder="Add your custom message here..."></textarea>
                </div>

                
            </div>
            <div class="modal-footer">
               
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
                <button type="button" class="btn btn-success" onclick="sendWhatsAppMessage()">
                    <i class="fab fa-whatsapp me-1"></i>Send via WhatsApp
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Gmail Share Modal -->
<div class="modal fade" id="gmailModal" tabindex="-1" aria-labelledby="gmailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="gmailModalLabel">
                    <i class="fas fa-envelope me-2"></i>Share Quotation via Email
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Email Address -->
                <div class="mb-4">
                    <label class="form-label fw-bold">Email Address:</label>
                    <input type="email" class="form-control" id="emailAddress" placeholder="Enter email address">
                    <small class="text-muted">Enter recipient's email address</small>
                </div>

                <!-- Subject -->
                <div class="mb-4">
                    <label class="form-label fw-bold">Subject:</label>
                    <input type="text" class="form-control" id="emailSubject" placeholder="Email subject">
                </div>

                <!-- Message Preview -->
                <div class="mb-4">
                    <label class="form-label fw-bold">Message Preview:</label>
                    <div class="card">
                        <div class="card-body bg-light">
                            <div class="d-flex align-items-start">
                                <div class="bg-danger text-white rounded-circle p-2 me-3">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <pre id="emailMessagePreview" class="mb-0" style="white-space: pre-wrap; font-family: inherit; font-size: 0.9rem;"></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Custom Message -->
                <div class="mb-3">
                    <label class="form-label fw-bold">Customize Message (Optional):</label>
                    <textarea class="form-control" id="emailCustomMessage" rows="4" placeholder="Add your custom message here..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
                <button type="button" class="btn btn-danger" onclick="sendGmailMessage()">
                    <i class="fas fa-envelope me-1"></i>Send via Gmail
                </button>
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
    function clearFilters() {
        document.getElementById('statusFilter').value = '';
        document.getElementById('customerFilter').value = '';
        document.getElementById('dateFilter').value = '';
        // Reload the page to clear filters
        window.location.href = '{{ route("entries.sales") }}';
    }

    function printQuotation(id) {
        // Generate PDF with company-specific header
        window.open(`/entries/sales/${id}/print`, '_blank');
    }

    // Export Sales Function
    function exportSales() {
        // Get current filter values
        const filters = {
            status: document.getElementById('statusFilter')?.value || '',
            customer_id: document.getElementById('customerFilter')?.value || '',
            date_from: document.getElementById('dateFromFilter')?.value || '',
            date_to: document.getElementById('dateToFilter')?.value || ''
        };

        // Build query string
        const queryParams = new URLSearchParams();
        Object.keys(filters).forEach(key => {
            if (filters[key]) {
                queryParams.append(key, filters[key]);
            }
        });

        // Show loading state
        const exportButton = event.target;
        const originalText = exportButton.innerHTML;
        exportButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Exporting...';
        exportButton.disabled = true;

        // Create export URL
        const exportUrl = `{{ route('entries.sales.export') }}?${queryParams.toString()}`;

        // Use fetch to handle the download properly
        fetch(exportUrl, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // Check if response is JSON (error) or blob (file)
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return response.json().then(data => {
                    throw new Error(data.error || 'Export failed');
                });
            }

            return response.blob();
        })
        .then(blob => {
            // Create download link for the blob
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `sales_entries_${new Date().toISOString().slice(0,19).replace(/:/g, '-')}.xlsx`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);

            // Show success message
            alert('✅ Excel export completed!\n\nThe Excel file has been downloaded with current filter settings.');
        })
        .catch(error => {
            console.error('Export error:', error);
            alert('❌ Export failed!\n\nError: ' + error.message + '\n\nPlease try again or contact support.');
        })
        .finally(() => {
            // Reset button
            exportButton.innerHTML = originalText;
            exportButton.disabled = false;
        });
    }

    // Export Sales to CSV Function (Backup)
    function exportSalesCSV() {
        // Get current filter values
        const filters = {
            status: document.getElementById('statusFilter')?.value || '',
            customer_id: document.getElementById('customerFilter')?.value || '',
            date_from: document.getElementById('dateFromFilter')?.value || '',
            date_to: document.getElementById('dateToFilter')?.value || ''
        };

        // Build query string
        const queryParams = new URLSearchParams();
        Object.keys(filters).forEach(key => {
            if (filters[key]) {
                queryParams.append(key, filters[key]);
            }
        });

        // Show loading state
        const exportButton = event.target;
        const originalText = exportButton.innerHTML;
        exportButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Exporting...';
        exportButton.disabled = true;

        // Create export URL
        const exportUrl = `{{ route('entries.sales.export.csv') }}?${queryParams.toString()}`;

        // Create download link
        const link = document.createElement('a');
        link.href = exportUrl;
        link.download = '';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Reset button after a delay
        setTimeout(() => {
            exportButton.innerHTML = originalText;
            exportButton.disabled = false;
        }, 3000);

        // Show success message
        setTimeout(() => {
            alert('✅ CSV export completed!\n\nThe CSV file has been downloaded with current filter settings.');
        }, 1000);
    }

    // Global variables for WhatsApp modal
    let currentQuotationData = {};

    function openWhatsAppModal(id, customerName, quotationNumber, totalAmount, customerPhone) {
        currentQuotationData = {
            id: id,
            customerName: customerName,
            quotationNumber: quotationNumber,
            totalAmount: totalAmount,
            customerPhone: customerPhone
        };

        // Set customer phone if available
        if (customerPhone) {
            document.getElementById('phoneNumber').value = customerPhone;
        }

        // Generate message preview
        updateMessagePreview();

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('whatsappModal'));
        modal.show();
    }

    function updateMessagePreview() {
        const message = `🏢 *JMD Traders*

Hi ${currentQuotationData.customerName},

📄 *Quotation:* ${currentQuotationData.quotationNumber}
💰 *Amount:* ${currentQuotationData.totalAmount}

🔗 *View Online:* ${window.location.origin}/entries/sales/${currentQuotationData.id}/print

Thank you! 🙏
*JMD Traders*
📞 07714007253`;

        document.getElementById('messagePreview').textContent = message;
    }

    function generateShareLink(salesEntryId) {
        const viewUrl = `${window.location.origin}/entries/sales/${salesEntryId}/print-dynamic`;
        const pdfUrl = `${window.location.origin}/entries/sales/${salesEntryId}/print-pdf`;

        // Copy view URL to clipboard
        navigator.clipboard.writeText(viewUrl).then(() => {
            alert(`✅ Share links generated and copied to clipboard!

🔗 View URL: ${viewUrl}
📄 PDF URL: ${pdfUrl}

💡 You can now:
1. Paste these links anywhere to share
2. Recipients can view quotation details online
3. Direct PDF download available
4. Links are always accessible`);
        }).catch(() => {
            alert(`✅ Share links generated!

🔗 View URL: ${viewUrl}
📄 PDF URL: ${pdfUrl}

Please copy the URLs manually.`);
        });
    }

    function downloadPdfFirst() {
        // Open PDF in new tab for download
        const pdfUrl = `${window.location.origin}/entries/sales/${currentQuotationData.id}/pdf`;
        window.open(pdfUrl, '_blank');
    }

    function testWhatsAppUrl() {
        let phoneNumber = document.getElementById('phoneNumber').value.trim();

        if (!phoneNumber) {
            alert('Please enter a phone number first');
            return;
        }

        // Clean phone number
        phoneNumber = phoneNumber.replace(/\D/g, '');

        if (phoneNumber.length === 10) {
            phoneNumber = '91' + phoneNumber;
        }

        const message = document.getElementById('messagePreview').textContent;
        const encodedMessage = encodeURIComponent(message);
        const waUrl = `https://wa.me/${phoneNumber}?text=${encodedMessage}`;
        const webWhatsappUrl = `https://web.whatsapp.com/send?phone=${phoneNumber}&text=${encodedMessage}`;

        alert(`Debug Information:

Phone Number: ${phoneNumber}
Message Length: ${message.length} characters

URLs to test:
1. wa.me: ${waUrl}
2. web.whatsapp.com: ${webWhatsappUrl}

Click OK to test wa.me URL`);

        window.open(waUrl, '_blank');
    }



    function sendWhatsAppMessage() {
        let phoneNumber = document.getElementById('phoneNumber').value.trim();
        const customMessage = document.getElementById('customMessage').value.trim();

        if (!phoneNumber) {
            alert('Please enter a phone number');
            return;
        }

        // Clean phone number - remove any non-digits
        phoneNumber = phoneNumber.replace(/\D/g, '');

        // Handle different phone number formats
        if (phoneNumber.length === 10) {
            // Indian 10-digit number, add country code
            phoneNumber = '91' + phoneNumber;
        } else if (phoneNumber.length === 11 && phoneNumber.startsWith('0')) {
            // Remove leading 0 and add country code
            phoneNumber = '91' + phoneNumber.substring(1);
        } else if (phoneNumber.length === 12 && phoneNumber.startsWith('91')) {
            // Already has country code
            phoneNumber = phoneNumber;
        } else if (phoneNumber.length === 13 && phoneNumber.startsWith('+91')) {
            // Remove + sign
            phoneNumber = phoneNumber.substring(1);
        } else {
            alert('Please enter a valid phone number (10 digits)');
            return;
        }

        let finalMessage = document.getElementById('messagePreview').textContent;

        if (customMessage) {
            finalMessage += '\n\n' + customMessage;
        }

        const encodedMessage = encodeURIComponent(finalMessage);

        // Try wa.me first (works better on mobile)
        const waUrl = `https://wa.me/${phoneNumber}?text=${encodedMessage}`;

        console.log('WhatsApp URL:', waUrl); // Debug log

        // Try to open WhatsApp
        let whatsappWindow = window.open(waUrl, '_blank');

        if (!whatsappWindow) {
            // Fallback to web.whatsapp.com
            const webWhatsappUrl = `https://web.whatsapp.com/send?phone=${phoneNumber}&text=${encodedMessage}`;
            whatsappWindow = window.open(webWhatsappUrl, '_blank');

            if (!whatsappWindow) {
                alert('Please allow popups for this site to open WhatsApp.\n\nAlternatively, copy this number and message manually:\nPhone: +' + phoneNumber + '\nMessage: ' + finalMessage);
                return;
            }
        }

        // Close modal after a short delay
        setTimeout(() => {
            const modal = bootstrap.Modal.getInstance(document.getElementById('whatsappModal'));
            if (modal) {
                modal.hide();
            }
        }, 1000);
    }

    // Gmail Modal Functions
    function openGmailModal(id, customerName, quotationNumber, totalAmount, customerEmail) {
        currentQuotationData = {
            id: id,
            customerName: customerName,
            quotationNumber: quotationNumber,
            totalAmount: totalAmount,
            customerEmail: customerEmail
        };

        // Set customer email if available
        if (customerEmail) {
            document.getElementById('emailAddress').value = customerEmail;
        }

        // Set default subject
        document.getElementById('emailSubject').value = `Quotation ${quotationNumber} - JMD Traders`;

        // Generate email message preview
        updateEmailMessagePreview();

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('gmailModal'));
        modal.show();
    }

    function updateEmailMessagePreview() {
        const message = `Dear ${currentQuotationData.customerName},

Greetings from JMD Traders!

We are pleased to share the quotation details with you:

Quotation Details:
• Quotation Number: ${currentQuotationData.quotationNumber}
• Customer: ${currentQuotationData.customerName}
• Total Amount: ${currentQuotationData.totalAmount}

You can view the complete quotation by clicking the link below:
${window.location.origin}/entries/sales/${currentQuotationData.id}

Please feel free to contact us if you have any questions or require any clarifications.

Thank you for considering JMD Traders for your business needs.

Best Regards,
JMD Traders Team
Your trusted business partner`;

        document.getElementById('emailMessagePreview').textContent = message;
    }

    function sendGmailMessage() {
        const emailAddress = document.getElementById('emailAddress').value.trim();
        const emailSubject = document.getElementById('emailSubject').value.trim();
        const customMessage = document.getElementById('emailCustomMessage').value.trim();

        if (!emailAddress) {
            alert('Please enter an email address');
            return;
        }

        if (!emailSubject) {
            alert('Please enter email subject');
            return;
        }

        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(emailAddress)) {
            alert('Please enter a valid email address');
            return;
        }

        let finalMessage = document.getElementById('emailMessagePreview').textContent;

        if (customMessage) {
            finalMessage += '\n\n' + customMessage;
        }

        // Create Gmail compose URL
        const gmailUrl = `https://mail.google.com/mail/?view=cm&fs=1&to=${encodeURIComponent(emailAddress)}&subject=${encodeURIComponent(emailSubject)}&body=${encodeURIComponent(finalMessage)}`;

        window.open(gmailUrl, '_blank');

        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('gmailModal'));
        modal.hide();
    }





    function deleteEntry(id) {
        if (confirm('Are you sure you want to delete this sales entry?')) {
            fetch(`/entries/sales/${id}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Sales entry deleted successfully', 'success');
                    location.reload();
                } else {
                    showNotification('Error deleting sales entry', 'error');
                }
            })
            .catch(error => {
                showNotification('Error deleting sales entry', 'error');
            });
        }
    }

    // Filter functionality
    document.getElementById('statusFilter').addEventListener('change', applyFilters);
    document.getElementById('customerFilter').addEventListener('change', applyFilters);
    document.getElementById('dateFilter').addEventListener('change', applyFilters);

    function applyFilters() {
        const status = document.getElementById('statusFilter').value;
        const customer = document.getElementById('customerFilter').value;
        const date = document.getElementById('dateFilter').value;
        
        const params = new URLSearchParams();
        if (status) params.append('status', status);
        if (customer) params.append('customer', customer);
        if (date) params.append('date', date);
        
        const url = '{{ route("entries.sales") }}' + (params.toString() ? '?' + params.toString() : '');
        window.location.href = url;
    }


</script>
@endpush
