<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sales Entry - {{ $salesEntry->invoice_number ?? $salesEntry->quotation_number ?? 'SE-001' }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #000;
            background: #fff;
            padding: 20px;
        }
        
        .container {
            max-width: 100%;
            margin: 0 auto;
        }
        
        /* Header */
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #000;
            padding-bottom: 15px;
        }
        
        .company-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
            text-transform: uppercase;
        }
        
        .company-details {
            font-size: 11px;
            margin-bottom: 10px;
        }
        
        .document-title {
            font-size: 18px;
            font-weight: bold;
            margin-top: 10px;
            text-decoration: underline;
        }
        
        /* Document Info */
        .doc-info {
            width: 100%;
            margin-bottom: 20px;
        }
        
        .doc-info table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .doc-info td {
            padding: 5px;
            border: 1px solid #000;
            font-size: 11px;
        }
        
        .doc-info .label {
            font-weight: bold;
            background-color: #f0f0f0;
            width: 25%;
        }
        
        /* Customer Info */
        .customer-info {
            margin-bottom: 20px;
        }
        
        .customer-info h3 {
            background-color: #333;
            color: white;
            padding: 8px;
            margin: 0;
            font-size: 14px;
        }
        
        .customer-info table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .customer-info td {
            padding: 8px;
            border: 1px solid #000;
            font-size: 11px;
        }
        
        .customer-info .label {
            font-weight: bold;
            background-color: #f0f0f0;
            width: 30%;
        }
        
        /* Products Table */
        .products-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .products-table th {
            background-color: #333;
            color: white;
            padding: 8px;
            text-align: center;
            font-weight: bold;
            font-size: 10px;
            border: 1px solid #000;
        }
        
        .products-table td {
            padding: 6px;
            border: 1px solid #000;
            font-size: 10px;
            text-align: center;
        }
        
        .products-table .description {
            text-align: left;
            width: 30%;
        }
        
        .product-name {
            font-weight: bold;
        }
        
        .product-desc {
            font-style: italic;
            color: #666;
            font-size: 9px;
        }
        
        /* Totals */
        .totals {
            width: 50%;
            margin-left: auto;
            margin-bottom: 20px;
        }
        
        .totals table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .totals td {
            padding: 6px 10px;
            border: 1px solid #000;
            font-size: 11px;
        }
        
        .totals .label {
            font-weight: bold;
            background-color: #f0f0f0;
        }
        
        .totals .grand-total {
            background-color: #333;
            color: white;
            font-weight: bold;
            font-size: 12px;
        }
        
        /* Footer */
        .footer {
            margin-top: 40px;
            border-top: 1px solid #000;
            padding-top: 15px;
        }
        
        .signatures {
            width: 100%;
            margin-top: 30px;
        }
        
        .signatures table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .signatures td {
            padding: 40px 10px 10px 10px;
            text-align: center;
            border-bottom: 1px solid #000;
            font-size: 11px;
            font-weight: bold;
            width: 33.33%;
        }
        
        .print-info {
            text-align: center;
            font-size: 9px;
            color: #666;
            margin-top: 20px;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 10px;
            }
            
            .no-print {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="company-name">{{ $salesEntry->company->name ?? 'Company Name' }}</div>
            <div class="company-details">
                @if($salesEntry->company && $salesEntry->company->address)
                    {{ $salesEntry->company->address }}<br>
                @endif
                @if($salesEntry->company && ($salesEntry->company->city || $salesEntry->company->state))
                    {{ $salesEntry->company->city->name ?? '' }}
                    @if($salesEntry->company->city && $salesEntry->company->state), @endif
                    {{ $salesEntry->company->state->name ?? '' }}
                    @if($salesEntry->company && $salesEntry->company->pincode) - {{ $salesEntry->company->pincode }}@endif
                    <br>
                @endif
                @if($salesEntry->company && $salesEntry->company->phone)
                    Phone: {{ $salesEntry->company->phone }}
                @endif
                @if($salesEntry->company && $salesEntry->company->email)
                    | Email: {{ $salesEntry->company->email }}
                @endif
            </div>
            <div class="document-title">
                {{ $salesEntry->invoice_number ? 'SALES INVOICE' : 'SALES QUOTATION' }}
            </div>
        </div>

        <!-- Document Information -->
        <div class="doc-info">
            <table>
                <tr>
                    <td class="label">Entry No:</td>
                    <td>#{{ str_pad($salesEntry->id, 6, '0', STR_PAD_LEFT) }}</td>
                    <td class="label">{{ $salesEntry->invoice_number ? 'Invoice No:' : 'Quote No:' }}</td>
                    <td>{{ $salesEntry->invoice_number ?? $salesEntry->quotation_number ?? 'Not Set' }}</td>
                </tr>
                <tr>
                    <td class="label">Date:</td>
                    <td>{{ ($salesEntry->invoice_date ?? $salesEntry->quotation_date) ? ($salesEntry->invoice_date ?? $salesEntry->quotation_date)->format('d M Y') : 'Not Set' }}</td>
                    <td class="label">Status:</td>
                    <td>{{ ucfirst($salesEntry->status ?? 'draft') }}</td>
                </tr>
                <tr>
                    <td class="label">Payment Terms:</td>
                    <td>{{ $salesEntry->payment_terms ?? 'Not Specified' }}</td>
                    <td class="label">Created By:</td>
                    <td>{{ $salesEntry->user->name ?? 'System' }}</td>
                </tr>
            </table>
        </div>

        <!-- Customer Information -->
        <div class="customer-info">
            <h3>Customer Information</h3>
            <table>
                <tr>
                    <td class="label">Customer Name:</td>
                    <td>{{ $salesEntry->customer->name ?? $salesEntry->party_name ?? 'Not Specified' }}</td>
                </tr>
                <tr>
                    <td class="label">Contact:</td>
                    <td>
                        @if($salesEntry->customer->phone ?? $salesEntry->mobile)
                            Phone: {{ $salesEntry->customer->phone ?? $salesEntry->mobile }}
                        @endif
                        @if($salesEntry->customer->email ?? $salesEntry->email)
                            @if($salesEntry->customer->phone ?? $salesEntry->mobile) | @endif
                            Email: {{ $salesEntry->customer->email ?? $salesEntry->email }}
                        @endif
                    </td>
                </tr>
                <tr>
                    <td class="label">Address:</td>
                    <td>
                        @if($salesEntry->customer && $salesEntry->customer->address)
                            {{ $salesEntry->customer->address }}
                            @if($salesEntry->customer->city || $salesEntry->customer->state)<br>@endif
                        @endif
                        @if($salesEntry->customer && ($salesEntry->customer->city || $salesEntry->customer->state))
                            {{ $salesEntry->customer->city->name ?? '' }}
                            @if($salesEntry->customer->city && $salesEntry->customer->state), @endif
                            {{ $salesEntry->customer->state->name ?? '' }}
                            @if($salesEntry->customer->pincode) - {{ $salesEntry->customer->pincode }}@endif
                        @endif
                    </td>
                </tr>
            </table>
        </div>

        <!-- Products Table -->
        <table class="products-table">
            <thead>
                <tr>
                    <th>S.No.</th>
                    <th class="description">Description of Goods</th>
                    <th>HSN</th>
                    <th>Qty.</th>
                    <th>Basic Rate</th>
                    <th>Loading</th>
                    <th>Rate/PMT</th>
                    <th>Amount (₹)</th>
                </tr>
            </thead>
            <tbody>
                @foreach($salesEntry->items as $index => $item)
                <tr>
                    <td>{{ $index + 1 }}</td>
                    <td class="description">
                        <div class="product-name">{{ $item->product->name ?? 'N/A' }}</div>
                        @if($item->description)
                        <div class="product-desc">{{ $item->description }}</div>
                        @endif
                    </td>
                    <td>{{ $item->hsn_code ?? $item->product->hsn_code ?? 'N/A' }}</td>
                    <td>{{ number_format($item->packages ?? $item->quantity ?? 0, 0) }}</td>
                    <td>{{ number_format($item->basic_rate ?? 0, 2) }}</td>
                    <td>{{ number_format($item->loading ?? 0, 2) }}</td>
                    <td>{{ number_format($item->rate ?? $item->unit_price ?? 0, 2) }}</td>
                    <td>{{ number_format($item->total ?? $item->line_total ?? 0, 2) }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>

        <!-- Totals -->
        <div class="totals">
            <table>
                <tr>
                    <td class="label">Subtotal:</td>
                    <td>₹{{ number_format($salesEntry->subtotal ?? 0, 2) }}</td>
                </tr>
                @if(($salesEntry->total_cgst ?? 0) > 0)
                <tr>
                    <td class="label">CGST:</td>
                    <td>₹{{ number_format($salesEntry->total_cgst, 2) }}</td>
                </tr>
                @endif
                @if(($salesEntry->total_sgst ?? 0) > 0)
                <tr>
                    <td class="label">SGST:</td>
                    <td>₹{{ number_format($salesEntry->total_sgst, 2) }}</td>
                </tr>
                @endif
                @if(($salesEntry->total_igst ?? 0) > 0)
                <tr>
                    <td class="label">IGST:</td>
                    <td>₹{{ number_format($salesEntry->total_igst, 2) }}</td>
                </tr>
                @endif
                <tr class="grand-total">
                    <td>GRAND TOTAL:</td>
                    <td>₹{{ number_format($salesEntry->grand_total ?? $salesEntry->total_amount ?? 0, 2) }}</td>
                </tr>
                @if(($salesEntry->ins_pmt ?? 0) > 0)
                <tr>
                    <td class="label">Insurance P/MT:</td>
                    <td>₹{{ number_format($salesEntry->ins_pmt, 2) }}</td>
                </tr>
                @endif
                @if(($salesEntry->insurance ?? 0) > 0)
                <tr>
                    <td class="label">Insurance:</td>
                    <td>₹{{ number_format($salesEntry->insurance, 2) }}</td>
                </tr>
                @endif
                @if(($salesEntry->frt_advance ?? 0) > 0)
                <tr>
                    <td class="label">Freight Advance:</td>
                    <td>₹{{ number_format($salesEntry->frt_advance, 2) }}</td>
                </tr>
                @endif
                @if(($salesEntry->tcs_percent ?? 0) > 0)
                <tr>
                    <td class="label">TCS ({{ number_format($salesEntry->tcs_percent, 2) }}%):</td>
                    <td>₹{{ number_format($salesEntry->tcs_amount ?? 0, 2) }}</td>
                </tr>
                @endif
                <tr class="grand-total">
                    <td>NET AMOUNT:</td>
                    <td>₹{{ number_format($salesEntry->calculated_net_amount ?? $salesEntry->grand_total ?? $salesEntry->total_amount ?? 0, 2) }}</td>
                </tr>
            </table>
        </div>

        <!-- Terms and Conditions -->
        @if($salesEntry->notes)
        <div style="margin: 20px 0; border: 1px solid #000; padding: 10px;">
            <h4 style="margin-bottom: 10px;">Terms & Conditions / Notes:</h4>
            <p style="font-size: 11px;">{{ $salesEntry->notes }}</p>
        </div>
        @endif

        <!-- Signatures -->
        <div class="signatures">
            <table>
                <tr>
                    <td>Prepared By</td>
                    <td>Verified By</td>
                    <td>Authorized Signatory</td>
                </tr>
            </table>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div class="print-info">
                This is a system-generated document from {{ $salesEntry->company->name ?? 'Company' }} Sales Management System.<br>
                Generated on {{ now()->format('d M Y, h:i A') }} by {{ auth()->user()->name ?? 'System' }}<br>
                For any queries regarding this {{ $salesEntry->invoice_number ? 'invoice' : 'quotation' }}, please contact the sales department.
            </div>
        </div>
    </div>
</body>
</html>
