<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $pdfSettings['filename_prefix'] }} - {{ $salesEntry->quotation_number }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: {{ $pdfSettings['font_family'] }}, sans-serif;
            font-size: {{ $pdfSettings['font_size'] }};
            line-height: 1.6;
            color: #333;
            background: #fff;
        }
        
        .container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 15mm;
            background: white;
            min-height: 297mm;
            position: relative;
        }
        
        /* Header Styles */
        .header {
            border: 2px solid {{ $pdfSettings['header_color'] ?? '#4e73df' }};
            margin-bottom: 25px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(78, 115, 223, 0.15);
        }
        
        .header-top {
            display: flex;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid {{ $pdfSettings['header_color'] ?? '#4e73df' }};
            background: linear-gradient(135deg, {{ $pdfSettings['header_color'] ?? '#4e73df' }} 0%, {{ $pdfSettings['accent_color'] ?? '#224abe' }} 100%);
            color: white;
            position: relative;
            min-height: 100px;
        }
        
        .logo {
            width: 70px;
            height: 70px;
            background: rgba(255, 255, 255, 0.25);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            font-weight: bold;
            margin-right: 20px;
        }
        
        .company-info {
            flex: 1;
            text-align: center;
        }

        .company-name {
            font-size: 26px;
            font-weight: bold;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            letter-spacing: 0.5px;
        }

        .company-address {
            font-size: 11px;
            line-height: 1.3;
            margin-bottom: 8px;
            opacity: 0.9;
        }

        .company-contact {
            font-size: 10px;
            line-height: 1.3;
            opacity: 0.9;
        }

        .document-title {
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 18px;
            font-weight: bold;
            background: rgba(255, 255, 255, 0.25);
            padding: 10px 20px;
            border-radius: 25px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            letter-spacing: 1px;
        }

        .header-details {
            display: flex;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fc 0%, #e3e6f0 100%);
            border-top: 1px solid #e3e6f0;
        }

        .left-details, .right-details {
            flex: 1;
        }

        .detail-row {
            display: flex;
            margin-bottom: 8px;
            align-items: center;
        }

        .detail-label {
            width: 100px;
            font-weight: 600;
            color: #5a5c69;
            font-size: 11px;
        }

        .detail-value {
            font-weight: 500;
            color: #333;
            font-size: 11px;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
            gap: 4px;
        }

        .status-pending { background: #fff3cd; color: #856404; }
        .status-approved { background: #d4edda; color: #155724; }
        .status-rejected { background: #f8d7da; color: #721c24; }
        .status-draft { background: #d1ecf1; color: #0c5460; }
        
        /* Information Sections */
        .info-section {
            margin: 25px 0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }

        .section-title {
            background: linear-gradient(135deg, {{ $pdfSettings['header_color'] ?? '#4e73df' }} 0%, {{ $pdfSettings['accent_color'] ?? '#224abe' }} 100%);
            color: white;
            font-size: 13px;
            font-weight: 700;
            padding: 12px 20px;
            margin-bottom: 0;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            border-bottom: 1px solid #e3e6f0;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .info-content {
            border: 1px solid #e3e6f0;
            border-top: none;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0;
        }

        .info-grid.single {
            grid-template-columns: 1fr;
        }

        .info-item {
            padding: 15px 20px;
            border-right: 1px solid #f8f9fc;
            border-bottom: 1px solid #f8f9fc;
            display: flex;
            align-items: center;
            transition: background-color 0.2s ease;
        }

        .info-item:hover {
            background-color: rgba(78, 115, 223, 0.02);
        }

        .info-item:nth-child(even) {
            border-right: none;
        }

        .info-item:last-child,
        .info-item:nth-last-child(2) {
            border-bottom: none;
        }

        .info-label {
            font-size: 10px;
            color: #5a5c69;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.3px;
            min-width: 90px;
            margin-right: 10px;
        }

        .info-value {
            font-size: 11px;
            color: #333;
            font-weight: 500;
            flex: 1;
        }
        
        .logo-section {
            text-align: {{ $pdfSettings['logo_position'] ?? 'left' }};
            @if(($pdfSettings['logo_position'] ?? 'left') == 'right')
            margin-left: 30px;
            @else
            margin-right: 30px;
            @endif
        }
        
        .company-logo {
            @if(isset($pdfSettings['logo_size']) && $pdfSettings['logo_size'] == 'small')
            width: 60px;
            height: 60px;
            @elseif(isset($pdfSettings['logo_size']) && $pdfSettings['logo_size'] == 'large')
            width: 120px;
            height: 120px;
            @else
            width: 80px;
            height: 80px;
            @endif
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            border: 3px solid rgba(255,255,255,0.3);
        }
        
        /* Document Title */
        .document-title {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            border-left: 5px solid {{ $pdfSettings['accent_color'] }};
        }
        
        .document-title h1 {
            color: {{ $pdfSettings['header_color'] }};
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .document-subtitle {
            color: #666;
            font-size: 14px;
        }
        
        /* Customer and Invoice Details */
        .details-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            gap: 30px;
        }
        
        .customer-details, .invoice-details {
            flex: 1;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-top: 4px solid {{ $pdfSettings['accent_color'] }};
        }
        
        .section-title {
            /* color: {{ $pdfSettings['header_color'] }}; */
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 15px;
            padding-bottom: 5px;
            border-bottom: 2px solid {{ $pdfSettings['accent_color'] }};
        }
        
        .detail-row {
            margin-bottom: 8px;
            display: flex;
        }
        
        .detail-label {
            font-weight: bold;
            color: #555;
            width: 120px;
            flex-shrink: 0;
        }
        
        .detail-value {
            color: #333;
            flex: 1;
        }
        
        /* Items Table */
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .items-table th {
            background: linear-gradient(135deg, {{ $pdfSettings['header_color'] }}, {{ $pdfSettings['accent_color'] }});
            color: white;
            padding: 15px 10px;
            text-align: left;
            font-weight: bold;
            font-size: 14px;
        }
        
        .items-table td {
            padding: 12px 10px;
            border-bottom: 1px solid #eee;
            vertical-align: top;
        }
        
        .items-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .items-table tr:hover {
            background: #e3f2fd;
        }
        
        .text-right {
            text-align: right;
        }
        
        .text-center {
            text-align: center;
        }
        
        /* Totals Section */
        .totals-section {
            margin-top: 30px;
            display: flex;
            justify-content: flex-end;
        }
        
        .totals-table {
            width: 400px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .totals-table td {
            padding: 12px 20px;
            border-bottom: 1px solid #eee;
        }
        
        .totals-table .total-label {
            font-weight: bold;
            color: #555;
            text-align: right;
        }
        
        .totals-table .total-value {
            text-align: right;
            font-weight: bold;
        }
        
        .grand-total {
            background: linear-gradient(135deg, {{ $pdfSettings['header_color'] }}, {{ $pdfSettings['accent_color'] }});
            color: white !important;
            font-size: 16px;
        }
        
        .grand-total td {
            border-bottom: none;
        }
        
        /* Footer */
        .footer {
            margin-top: 50px;
            padding: 30px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            text-align: center;
        }
        
        .terms-conditions {
            margin-bottom: 20px;
            color: #555;
            font-style: italic;
        }
        
        .footer-text {
            color: #777;
            font-size: 11px;
        }
        
        /* Watermark */
        .watermark {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            opacity: 0.1;
            z-index: -1;
            font-size: 72px;
            font-weight: bold;
            color: #ddd;
            pointer-events: none;
        }
        
        /* Currency Symbol */
        .currency {
            font-weight: bold;
            color: {{ $pdfSettings['accent_color'] }};
        }
        
        /* Responsive adjustments for PDF */
        @media print {
            .container {
                padding: 10px;
            }
            
            .header {
                margin-bottom: 20px;
                padding: 20px;
            }
            
            .details-section {
                margin-bottom: 20px;
            }
        }

        .signature-section {
            text-align:right;
            display: flex;
           justify-content: flex-end;
            margin: 40px 0 20px 0;
        }

        .signature-box {
            text-align: center;
            width: 150px;
        }

        .signature-line {
            border-top: 1px solid #333;
            margin-top: 40px;
            padding-top: 8px;
            font-size: 10px;
            font-weight: 600;
            color: #5a5c69;
        }
       
        .watermarkk {
            position: absolute;
            inset: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 0;           /* behind content */
            pointer-events: none;
            user-select: none;
            opacity: 0.1;
            font-size:60px;
            transform: translate(-50%, -50%);
            z-index: 0;
            transform: rotate(320deg);
            }
           
    </style>
    
</head>
<body>
    <div class="container">

 
        @if($company && $company->watermark && ($pdfSettings['show_watermark'] ?? false))
            @if($company->watermark)
                <!-- <img src="{{ asset('storage/' .$company->watermark) }}" class="watermarkk" alt="Watermark"> -->

            @else
                <!-- Watermark file not found or invalid path -->
            @endif
        @endif


        @if($company && $company->watermarkName && ($pdfSettings['show_watermark'] ?? false))
            @if($company->watermarkName)
               <h1 class="watermarkk">{{ $company->watermarkName }}</h1>
            @else
                <!-- Watermark file not found or invalid path -->
            @endif
        @endif

        <!-- Header Section -->
        <div class="header">
           
                

                <div class="header-top">
                <div class="">
                     @if($company->name)
                        <img src="{{ asset('storage/' . $company->logo) }}" 
                                                     alt="{{ $company->name }}" 
                                                     class="img-thumbnail" 
                                                     style="width: 80px; height: 80px;">
                     @endif
                </div>
               
                <div class="company-info">
                    <div class="company-name">{{ $company->name }}</div>
                    @if($pdfSettings['show_company_details'] ?? true)
                    <div class="company-details">
                        @if($company->address)
                        <div>{{ $company->address }}</div>
                        @endif
                        @if($company->city || $company->state)
                        <div>
                            @if($company->city){{ $company->city->name ?? '' }}@endif
                            @if($company->city && $company->state), @endif
                            @if($company->state){{ $company->state->name   ?? '' }}@endif
                            @if($company->pincode) - {{ $company->pincode  ?? '' }}@endif
                        </div>
                        @endif
                        @if($company->phone || $company->email)
                        <div>
                            @if($company->phone)Phone: {{ $company->phone  ?? '' }}@endif
                            @if($company->phone && $company->email) | @endif
                            @if($company->email)Email: {{ $company->email  ?? '' }}@endif
                        </div>
                        @endif
                        @if(($pdfSettings['show_gst_details'] ?? true) && ($company->gst_number || $company->pan_number))
                        <div>
                            @if($company->gst_number)GST: {{ $company->gst_number   ?? '' }}@endif
                            @if($company->gst_number && $company->pan_number) | @endif
                            @if($company->pan_number)PAN: {{ $company->pan_number   ?? '' }}@endif
                        </div>
                        @endif
                        @if($company->website)
                        <div>Website: {{ $company->website }}</div>
                        @endif
                    </div>
                    @endif
                </div>
                
                @if(($pdfSettings['logo_position'] ?? 'left') == 'right')
                <div class="logo-section">
                    
                    @if(($pdfSettings['show_logo'] ?? true) && $company->logo)
                    <img src="{{ public_path('storage/' . $company->logo) }}" alt="{{ $company->name }}" class="company-logo">
                    @else
                    <div class="company-logo">
                        {{ strtoupper(substr($company->name ?? 'CO', 0, 2)) }}
                    </div>
                    @endif
                </div>
                @endif
            </div>
        </div>

       

        <!-- Customer and Invoice Details -->
        <div class="details-section">
            <div class="customer-details">
                <div class="section-title">Bill To</div>
                <div class="detail-row">
                    <span class="detail-label" >Customer:</span>
                    <span class="detail-value">{{ $salesEntry->customer->name }}</span>
                </div>
                @if($salesEntry->customer->address)
                <div class="detail-row">
                    <span class="detail-label">Address:</span>
                    <span class="detail-value">{{ $salesEntry->customer->address }}</span>
                </div>
                @endif
                @if($salesEntry->customer->city || $salesEntry->customer->state)
                <div class="detail-row">
                    <span class="detail-label">City/State:</span>
                    <span class="detail-value">
                        @if($salesEntry->customer->city){{ $salesEntry->customer->city->name }}@endif
                        @if($salesEntry->customer->city && $salesEntry->customer->state), @endif
                        @if($salesEntry->customer->state){{ $salesEntry->customer->state->name }}@endif
                        @if($salesEntry->customer->pincode) - {{ $salesEntry->customer->pincode }}@endif
                    </span>
                </div>
                @endif
                @if($salesEntry->customer->phone)
                <div class="detail-row">
                    <span class="detail-label">Phone:</span>
                    <span class="detail-value">{{ $salesEntry->customer->phone }}</span>
                </div>
                @endif
                @if($salesEntry->customer->email)
                <div class="detail-row">
                    <span class="detail-label">Email:</span>
                    <span class="detail-value">{{ $salesEntry->customer->email }}</span>
                </div>
                @endif
                @if($salesEntry->customer->gst_number)
                <div class="detail-row">
                    <span class="detail-label">GST No:</span>
                    <span class="detail-value">{{ $salesEntry->customer->gst_number }}</span>
                </div>
                @endif
            </div>

            <div class="invoice-details">
                <div class="section-title">Quotation Details</div>
                <div class="detail-row">
                    <span class="detail-label">Quote No:</span>
                    <span class="detail-value">{{ $salesEntry->quotation_number }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Date:</span>
                    <span class="detail-value">{{ $salesEntry->quotation_date->format($pdfSettings['date_format'] ?? 'd M Y') }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Valid Until:</span>
                    <span class="detail-value">{{ $salesEntry->valid_until->format($pdfSettings['date_format'] ?? 'd M Y') }}</span>
                </div>
                @if($salesEntry->invoice_number)
                <div class="detail-row">
                    <span class="detail-label">Invoice No:</span>
                    <span class="detail-value">{{ $salesEntry->invoice_number }}</span>
                </div>
                @endif
                @if($salesEntry->payment_terms)
                <div class="detail-row">
                    <span class="detail-label">Payment Terms:</span>
                    <span class="detail-value">{{ $salesEntry->payment_terms }}</span>
                </div>
                @endif
                <div class="detail-row">
                    <span class="detail-label">Status:</span>
                    <span class="detail-value" style="color: {{ $salesEntry->status == 'approved' ? ($pdfSettings['accent_color'] ?? '#1d4ed8') : '#f39c12' }}; font-weight: bold;">
                        {{ ucfirst($salesEntry->status) }}
                    </span>
                </div>
            </div>
        </div>

        <!-- Items Table -->
        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 5%;">#</th>
                    <th style="width: 35%;">Description</th>
                    <th style="width: 10%;">HSN</th>
                    <th style="width: 10%;">Qty</th>
                    <th style="width: 10%;">Unit</th>
                    <th style="width: 15%;">Rate</th>
                    <th style="width: 15%;">Amount</th>
                </tr>
            </thead>
            <tbody>
                @foreach($salesEntry->items as $index => $item)
                <tr>
                    <td class="text-center">{{ $index + 1 }}</td>
                    <td>
                        <strong>{{ $item->product->name ?? $item->description }}</strong>
                        @if($item->description && $item->product)
                        <br><small style="color: #666;">{{ $item->description }}</small>
                        @endif
                    </td>
                    <td class="text-center">{{ $item->hsn_code }}</td>
                    <td class="text-center">{{ $item->packages ?? $item->quantity }}</td>
                    <td class="text-center">{{ $item->unit }}</td>
                    <td class="text-right">
                        <span class="currency">{{ $pdfSettings['currency_symbol'] ?? '₹' }}</span>{{ number_format($item->rate ?? $item->unit_price, 2) }}
                    </td>
                    <td class="text-right">
                        <span class="currency">{{ $pdfSettings['currency_symbol'] ?? '₹' }}</span>{{ number_format($item->total ?? $item->line_total, 2) }}
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>

        <!-- Totals Section -->
        <div class="totals-section">
            <table class="totals-table">
                <tr>
                    <td class="total-label">Subtotal:</td>
                    <td class="total-value">
                        <span class="currency">{{ $pdfSettings['currency_symbol'] ?? '₹' }}</span>{{ number_format($salesEntry->subtotal, 2) }}
                    </td>
                </tr>
                @if($salesEntry->total_cgst > 0)
                <tr>
                    <td class="total-label">CGST:</td>
                    <td class="total-value">
                        <span class="currency">{{ $pdfSettings['currency_symbol'] ?? '₹' }}</span>{{ number_format($salesEntry->total_cgst, 2) }}
                    </td>
                </tr>
                @endif
                @if($salesEntry->total_sgst > 0)
                <tr>
                    <td class="total-label">SGST:</td>
                    <td class="total-value">
                        <span class="currency">{{ $pdfSettings['currency_symbol'] ?? '₹' }}</span>{{ number_format($salesEntry->total_sgst, 2) }}
                    </td>
                </tr>
                @endif
                @if($salesEntry->total_igst > 0)
                <tr>
                    <td class="total-label">IGST:</td>
                    <td class="total-value">
                        <span class="currency">{{ $pdfSettings['currency_symbol'] ?? '₹' }}</span>{{ number_format($salesEntry->total_igst, 2) }}
                    </td>
                </tr>
                @endif
                @if($salesEntry->ins_pmt > 0)
                <tr>
                    <td class="total-label">Insurance Premium:</td>
                    <td class="total-value">
                        <span class="currency">{{ $pdfSettings['currency_symbol'] ?? '₹' }}</span>{{ number_format($salesEntry->ins_pmt, 2) }}
                    </td>
                </tr>
                @endif
                @if($salesEntry->insurance > 0)
                <tr>
                    <td class="total-label">Insurance:</td>
                    <td class="total-value">
                        <span class="currency">{{ $pdfSettings['currency_symbol'] ?? '₹' }}</span>{{ number_format($salesEntry->insurance, 2) }}
                    </td>
                </tr>
                @endif
                @if($salesEntry->frt_advance > 0)
                <tr>
                    <td class="total-label">Freight Advance:</td>
                    <td class="total-value">
                        <span class="currency">{{ $pdfSettings['currency_symbol'] ?? '₹' }}</span>{{ number_format($salesEntry->frt_advance, 2) }}
                    </td>
                </tr>
                @endif
                @if($salesEntry->tcs_amount > 0)
                <tr>
                    <td class="total-label">TCS ({{ $salesEntry->tcs_percent }}%):</td>
                    <td class="total-value">
                        <span class="currency">{{ $pdfSettings['currency_symbol'] ?? '₹' }}</span>{{ number_format($salesEntry->tcs_amount, 2) }}
                    </td>
                </tr>
                @endif
                <tr class="grand-total">
                    <td class="total-label" style="color:white;">Grand Total:</td>
                    <td class="total-value">
                        <span class="currency">{{ $pdfSettings['currency_symbol'] ?? '₹' }}</span>{{ number_format($salesEntry->calculated_net_amount, 2) }}
                    </td>
                </tr>
            </table>
        </div>

       

         <!-- Signature Section -->
        <div class="signature-section">
           
            <div class="signature-box" >
                <div class="signature-line">Authorized Signatory</div>
            </div>
        </div>

         <!-- Notes Section -->
        @if($salesEntry->notes)
        <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 10px; border-left: 5px solid {{ $pdfSettings['accent_color'] ?? '#1d4ed8' }};">
            <div style="color: {{ $pdfSettings['header_color'] ?? '#2563eb' }}; font-weight: bold; margin-bottom: 10px;">Notes:</div>
            <div style="color: #555; line-height: 1.6;">{{ $salesEntry->notes }}</div>
        </div>
        @endif

        <!-- Footer -->
        <div class="footer">
            <div class="terms-conditions">
                {{ $pdfSettings['terms_and_conditions'] ?? 'Thank you for your business.' }}
            </div>
            <div class="footer-text">
                {{ $pdfSettings['footer_text'] ?? 'This is a system-generated quotation.' }} | Generated on {{ now()->format($pdfSettings['date_format'] ?? 'd M Y') }}
            </div>
        </div>
    </div>

    <script>
        // Auto print when page loads
        window.onload = function() {
            window.print();
        }
    </script>
</body>
</html>
