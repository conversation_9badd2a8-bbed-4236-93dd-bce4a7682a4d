<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Sales Entry PDF</title>
    <style>
        body {
            font-family: Deja<PERSON>u Sans, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #000;
            background: #fff;
        }

        .container {
            width: 100%;
            padding: 10px;
        }

        /* Header */
        .header {
            /* border: 1px solid #000; */
            margin-bottom: 15px;
        }

        .header-top {
            background: #1cc88a;
            color: #fff;
            padding: 10px;
            text-align: center;
        }

        .header-top h2 {
            margin: 0;
            font-size: 18px;
        }

        .header-details {
            width: 100%;
            border-collapse: collapse;
            
        }

        .header-details td {
            padding: 5px;
            font-size: 11px;
            border: 1px solid #ccc;
        }

        /* Customer Info */
        .customer-section {
             width: 100%;
            /* border: 1px solid #000; */
            margin: 10px 0;
        }

        .customer-section th {
            background: #1cc88a;
            color: #fff;
            padding: 6px;
            font-size: 12px;
            text-align: left;
        }

        .customer-section td {
            padding: 6px;
            font-size: 11px;
            border: 1px solid #ccc;
        }

        /* Products Table */
        .products-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }

        .products-table th {
            background: #1cc88a;
            color: #fff;
            font-size: 10px;
            padding: 6px;
            border: 1px solid #ddd;
        }

        .products-table td {
            border: 1px solid #ddd;
            padding: 6px;
            font-size: 10px;
        }

        /* Financial Table */
        .financial-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .financial-table td {
            border: 1px solid #ccc;
            padding: 6px;
            font-size: 11px;
        }

        .financial-table tr.total td {
            background: #1cc88a;
            color: #fff;
            font-weight: bold;
        }

        /* Terms */
        .terms {
            border: 1px solid #ccc;
            padding: 8px;
            margin: 10px 0;
            font-size: 10px;
        }

        /* Footer */
        .footer {
            /* border-top: 1px solid #000; */
            padding-top: 8px;
            margin-top: 15px;
            font-size: 10px;
        }

        /* Signature */
        .signature {
            text-align: right;
            margin-top: 40px;
            font-size: 11px;
        }

        .watermark {
            position: fixed;
            top: 40%;
            left: 20%;
            font-size: 60px;
            color: #000;
            opacity: 0.1;
            transform: rotate(-30deg);
            z-index: -1;
        }
 .section {
           
            margin-top: 12px;
        }
         .title {
            font-size: 14px;
            font-weight: bold;
            background: #1cc88a;
            padding: 5px;
            color:white;
            /* border-bottom: 1px solid #000; */
        }
        /* Prevent table breaking */
        table { page-break-inside: avoid; }
         table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
<div class="container">
        @php
            $company = $salesEntry->company ?? null;
            $logoFile = $company->logo ?? null;
            $logoPath = $logoFile ? public_path('storage/'.$logoFile) : null;
            $hasLogo = $logoPath && file_exists($logoPath);
            $ext = $hasLogo ? strtolower(pathinfo($logoPath, PATHINFO_EXTENSION)) : 'png';
            $mime = in_array($ext, ['jpg','jpeg']) ? 'image/jpeg' : ($ext === 'webp' ? 'image/webp' : 'image/png');
            $logoB64 = $hasLogo ? base64_encode(file_get_contents($logoPath)) : null;
        @endphp
    @if($company && $company->watermarkName && ($pdfSettings['show_watermark'] ?? false))
        <div class="watermark">{{ $company->watermarkName }}</div>
    @endif

    <!-- Header -->
    <div class="header">
         <table width="100%" cellspacing="0" cellpadding="8" style="border-collapse:collapse;">
        <tr style="background:#1cc88a; color:#fff;">
            <!-- 30%: Logo -->
            <td style="width:20%; vertical-align:middle; text-align:center;">
                @if($logoB64)
                    <img src="data:{{ $mime }};base64,{{ $logoB64 }}"
                         alt="{{ $company->name ?? 'Logo' }}"
                         style="width:80px; height:80px; border-radius:10%">
                @endif
            </td>

            <!-- 70%: Company info -->
            <td style="width:80%; vertical-align:middle; text-align:center;">
                <h2 style="margin:0; font-size:25px;">{{ $company->name ?? 'Company Name' }}</h2>
                <b><div> {{ $company->email ?? '' }} |  {{ $company->phone ?? '' }} | {{ $company->mobile ?? '' }}</div>

                <!-- <div>
                     <strong>GSTIN: {{ $company->gst_number ?? '' }} |
                   PAN: {{ $company->pan_number ?? '' }} |
                    CIN:{{ $company->cin ?? '' }} </strong>
                </div> -->
                <div>
                    {{ $company->address ?? '' }}<br>
                    {{ $company->city ?? '' }} {{ $company->state ?? '' }} - {{ $company->pincode ?? '' }}
                </div>
                <div>Website: {{ $company->website ?? '' }}</div></b>
            </td>
        </tr>
    </table>

        <table class="header-details">
            <tr>
                <td>GSTIN: {{ $salesEntry->company->gst_number ?? '' }}</td>
                <td>PAN: {{ $salesEntry->company->pan_number ?? '' }}</td>
                <td>CIN: {{ $salesEntry->company->cin ?? '' }}</td>
            </tr>
            <tr>
                <td>Quatation No: {{ $salesEntry->invoice_number ?? $salesEntry->quotation_number ?? 'Not Set' }}</td>
                <td>Date: {{ ($salesEntry->invoice_date ?? $salesEntry->quotation_date) ? ($salesEntry->invoice_date ?? $salesEntry->quotation_date)->format('d M Y') : 'Not Set' }}</td>
                <td>State: {{ $salesEntry->company->state ?? '' }}</td>
            </tr>
        </table>
    </div>

    <!-- Customer Info -->
    <table class="customer-section" width="100%">
        <tr>
            <th>Buyer</th>
            <th>Consignee</th>
        </tr>
        <tr>
            <td>
                {{ $salesEntry->customer->name ?? $salesEntry->party_name ?? '' }} <br>
                Phone: {{ $salesEntry->customer->phone ?? $salesEntry->mobile ?? '' }} <br>
                Email: {{ $salesEntry->customer->email ?? $salesEntry->email ?? '' }}
            </td>
            <td>
                {{ $salesEntry->customer->name ?? $salesEntry->party_name ?? '' }} <br>
                Phone: {{ $salesEntry->customer->phone ?? $salesEntry->mobile ?? '' }} <br>
                Email: {{ $salesEntry->customer->email ?? $salesEntry->email ?? '' }}
            </td>
        </tr>
    </table>

    <!-- Products -->
    <table class="products-table">
        <thead>
        <tr>
            <th>S.No.</th>
            <th colspan="2">Description</th>
            <th>HSN</th>
            <th>Qty</th>
            <th>Rate</th>
           
            <th>Loading</th>
            <th>Rate/PMT</th>
            <th>Value</th>
        </tr>
        </thead>
        <tbody>
        @foreach($salesEntry->items as $index => $item)
            <tr>
                <td>{{ $index+1 }}</td>
                <td colspan="2">{{ $item->product->name ?? '' }}</td>
                <td>{{ $item->hsn_code ?? '' }}</td>
                <td>{{ number_format($item->quantity ?? 0, 2) }}</td>
                <td>{{ number_format($item->basic_rate ?? 0, 2) }}</td>
                <td>{{ number_format($item->loading_rate ?? 0, 2) }}</td>
                <td>{{ number_format($item->rate ?? 0, 2) }}</td>
                <td>{{ number_format($item->total ?? 0, 2) }}</td>
            </tr>
        @endforeach
        <tr>
            <td colspan="8" align="right"><b>Total</b></td>
            <td><b>{{ number_format($salesEntry->subtotal ?? 0, 2) }}</b></td>
        </tr>
        </tbody>
    </table>

    <!-- Financials -->
    <table class="financial-table" >
        <tr><td align="right">Subtotal</td><td align="right">₹{{ number_format($salesEntry->subtotal ?? 0, 2) }}</td></tr>
        @if($salesEntry->total_cgst > 0)<tr><td align="right">CGST</td><td align="right">₹{{ number_format($salesEntry->total_cgst, 2) }}</td></tr>@endif
        @if($salesEntry->total_sgst > 0)<tr><td align="right">SGST</td><td align="right">₹{{ number_format($salesEntry->total_sgst, 2) }}</td></tr>@endif
        @if($salesEntry->total_igst > 0)<tr><td align="right">IGST</td><td align="right">₹{{ number_format($salesEntry->total_igst, 2) }}</td></tr>@endif
        <tr class="total"><td>Grand Total</td align="right"><td align="right">₹{{ number_format($salesEntry->grand_total ?? 0, 2) }}</td></tr>
        <tr class="total"><td>Net Amount</td align="right"><td align="right">₹{{ number_format($salesEntry->calculated_net_amount ?? 0, 2) }}</td></tr>
    </table>

    <!-- Terms -->
    @if($salesEntry->terms_conditions)
        <div class="terms">{{ $salesEntry->terms_conditions }}</div>
    @endif

    <!-- Bank -->
        <div class="section">
            <div class="title">Bank Details</div>
            <table>
                <tr>
                    <td><b>Bank Name</b></td>
                    <td>{{ $salesEntry->company->bankname ?? '' }}</td>
                    <td><b>A/C No</b></td>
                    <td>{{ $salesEntry->company->bankaccount ?? '' }}</td>
                </tr>
                <tr>
                    <td><b>Branch</b></td>
                    <td>{{ $salesEntry->company->branch ?? '' }}</td>
                    <td><b>IFSC</b></td>
                    <td>{{ $salesEntry->company->ifsccode ?? '' }}</td>
                </tr>
            </table>
        </div>

    <!-- Footer -->
    <div class="footer">
       
        <br><br>
        <b> Terms & Conditions:</b></br>
               1. Payment on Tax invoice .</br>
               2. Booking amount 20% .</br>
               3. Prices are not valid before booking.</br>
                4. Below 5 Ton in each size 500 extra will be charge on per ton. </br>
                5. Below 5 Ton the loading charge will be 765 per ton </br>
                6. For the customized size , the rate will be extra </br>
                7. Subject to Raipur Jurisdiction.
    </div>

    <!-- Signature -->
    <div class="signature">
       <b> For, {{ $salesEntry->company->name ?? '' }} <br><br>
        Authorized Signatory</b>
    </div>

</div>
</body>
</html>
