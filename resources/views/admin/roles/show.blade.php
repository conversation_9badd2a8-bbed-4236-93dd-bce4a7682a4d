@extends('layouts.app')

@section('title', 'View Role - JMD Traders')
@section('page-title', 'View Role')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Role Details: {{ $role->name }}</h1>
        <div>
            <a href="{{ route('admin.roles.edit', $role) }}" class="btn btn-warning btn-sm">
                <i class="fas fa-edit"></i> Edit Role
            </a>
            <a href="{{ route('admin.roles.index') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Back to Roles
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Role Information -->
        <div class="col-md-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Role Information</h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Name:</strong></td>
                            <td>{{ $role->name }}</td>
                        </tr>
                        <tr>
                            <td><strong>Slug:</strong></td>
                            <td><code>{{ $role->slug }}</code></td>
                        </tr>

                        <tr>
                            <td><strong>Status:</strong></td>
                            <td>
                                @if($role->status === 'active')
                                    <span class="badge badge-success">Active</span>
                                @else
                                    <span class="badge badge-secondary">Inactive</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Description:</strong></td>
                            <td>{{ $role->description ?? 'No description provided' }}</td>
                        </tr>
                        <tr>
                            <td><strong>Created:</strong></td>
                            <td>{{ $role->created_at->format('M d, Y H:i') }}</td>
                        </tr>
                        <tr>
                            <td><strong>Updated:</strong></td>
                            <td>{{ $role->updated_at->format('M d, Y H:i') }}</td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Role Statistics -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-right">
                                <h4 class="text-primary">{{ $role->permissions->count() }}</h4>
                                <small class="text-muted">Permissions</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">{{ \App\Models\User::where('role_id', $role->id)->count() }}</h4>
                            <small class="text-muted">Users</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.roles.edit', $role) }}" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit"></i> Edit Role
                        </a>
                        
                        @if($role->slug !== 'super-admin')
                        <form action="{{ route('admin.roles.toggle-status', $role) }}" method="POST" class="d-inline">
                            @csrf
                            @method('PATCH')
                            <button type="submit" class="btn btn-{{ $role->status === 'active' ? 'secondary' : 'success' }} btn-sm w-100">
                                <i class="fas fa-toggle-{{ $role->status === 'active' ? 'off' : 'on' }}"></i>
                                {{ $role->status === 'active' ? 'Deactivate' : 'Activate' }}
                            </button>
                        </form>
                        
                        <form action="{{ route('admin.roles.destroy', $role) }}" method="POST" 
                              onsubmit="return confirm('Are you sure you want to delete this role?')" class="d-inline">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger btn-sm w-100">
                                <i class="fas fa-trash"></i> Delete Role
                            </button>
                        </form>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Permissions -->
        <div class="col-md-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        Assigned Permissions ({{ $role->permissions->count() }})
                    </h6>
                </div>
                <div class="card-body">
                    @if($role->permissions->count() > 0)
                        <div class="permissions-display">
                            @foreach($role->permissions->groupBy('module') as $module => $modulePermissions)
                            <div class="permission-module mb-4">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-cube"></i> {{ ucfirst($module) }} Module
                                    <span class="badge badge-info ml-2">{{ $modulePermissions->count() }} permissions</span>
                                </h6>
                                <div class="permission-grid">
                                    @foreach($modulePermissions as $permission)
                                    <div class="permission-item">
                                        <div class="permission-card">
                                            <div class="permission-name">
                                                <i class="fas fa-key text-success"></i>
                                                {{ $permission->name }}
                                            </div>
                                            @if($permission->description)
                                            <div class="permission-description">
                                                {{ $permission->description }}
                                            </div>
                                            @endif
                                            <div class="permission-meta">
                                                <small class="text-muted">
                                                    <code>{{ $permission->slug }}</code>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                    @endforeach
                                </div>
                            </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-key fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Permissions Assigned</h5>
                            <p class="text-muted">This role doesn't have any permissions assigned yet.</p>
                            <a href="{{ route('admin.roles.edit', $role) }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Assign Permissions
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Users with this Role -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-success">
                Users with this Role ({{ \App\Models\User::where('role_id', $role->id)->count() }})
            </h6>
        </div>
        <div class="card-body">
            @php
                $users = \App\Models\User::where('role_id', $role->id)->with('companies')->get();
            @endphp
            
            @if($users->count() > 0)
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Companies</th>
                                <th>Status</th>
                                <th>Created</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($users as $user)
                            <tr>
                                <td>
                                    <strong>{{ $user->name }}</strong>
                                </td>
                                <td>{{ $user->email }}</td>
                                <td>
                                    @foreach($user->companies as $company)
                                        <span class="badge badge-info mr-1">{{ $company->name }}</span>
                                    @endforeach
                                </td>
                                <td>
                                    <span class="badge badge-{{ $user->status === 'active' ? 'success' : 'secondary' }}">
                                        {{ ucfirst($user->status) }}
                                    </span>
                                </td>
                                <td>{{ $user->created_at->format('M d, Y') }}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-4">
                    <i class="fas fa-users fa-2x text-muted mb-3"></i>
                    <h6 class="text-muted">No Users Assigned</h6>
                    <p class="text-muted">No users have been assigned this role yet.</p>
                </div>
            @endif
        </div>
    </div>
</div>

@push('styles')
<style>
.permission-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
}

.permission-card {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 6px;
    padding: 12px;
    transition: all 0.3s ease;
}

.permission-card:hover {
    background: #e2e6ea;
    border-color: #d1d3e2;
}

.permission-name {
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 5px;
}

.permission-description {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 8px;
}

.permission-meta {
    border-top: 1px solid #e3e6f0;
    padding-top: 5px;
}

.d-grid {
    display: grid;
}

.gap-2 {
    gap: 0.5rem;
}
</style>
@endpush
@endsection
