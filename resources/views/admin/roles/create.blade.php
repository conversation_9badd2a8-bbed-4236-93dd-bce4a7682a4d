@extends('layouts.app')

@section('title', 'Create Role - JMD Traders')
@section('page-title', 'Create Role')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Create New Role</h1>
        <div>
            <a href="{{ route('admin.roles.index') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Back to Roles
            </a>
        </div>
    </div>

    @if($errors->any())
        <div class="alert alert-danger">
            <ul class="mb-0">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Role Information</h6>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.roles.store') }}" method="POST">
                @csrf
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="name">Role Name *</label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   id="name" name="name" value="{{ old('name') }}" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    

                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="status">Status *</label>
                            <select class="form-control @error('status') is-invalid @enderror" id="status" name="status" required>
                                <option value="active" {{ old('status', 'active') == 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="description">Description</label>
                    <textarea class="form-control @error('description') is-invalid @enderror" 
                              id="description" name="description" rows="3">{{ old('description') }}</textarea>
                    @error('description')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="form-group">
                    <label>Permissions</label>
                    <div class="permissions-container">
                        @foreach($groupedPermissions as $module => $modulePermissions)
                        <div class="permission-module">
                            <div class="module-header">
                                <h6 class="text-primary">
                                    <input type="checkbox" class="module-checkbox" data-module="{{ $module }}">
                                    {{ ucfirst($module) }} Module
                                </h6>
                            </div>
                            <div class="permission-list">
                                @foreach($modulePermissions as $permission)
                                <div class="form-check">
                                    <input class="form-check-input permission-checkbox" 
                                           type="checkbox" 
                                           name="permissions[]" 
                                           value="{{ $permission->id }}" 
                                           id="permission_{{ $permission->id }}"
                                           data-module="{{ $module }}"
                                           {{ in_array($permission->id, old('permissions', [])) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="permission_{{ $permission->id }}">
                                        {{ $permission->name }}
                                        @if($permission->description)
                                            <small class="text-muted d-block">{{ $permission->description }}</small>
                                        @endif
                                    </label>
                                </div>
                                @endforeach
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Create Role
                    </button>
                    <a href="{{ route('admin.roles.index') }}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

@push('styles')
<style>
.permissions-container {
    border: 1px solid #e3e6f0;
    border-radius: 8px;
    padding: 15px;
    background-color: #f8f9fc;
    max-height: 400px;
    overflow-y: auto;
}

.permission-module {
    margin-bottom: 20px;
    padding: 15px;
    background-color: white;
    border-radius: 6px;
    border: 1px solid #e3e6f0;
}

.permission-module:last-child {
    margin-bottom: 0;
}

.module-header {
    margin-bottom: 10px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e3e6f0;
}

.module-header h6 {
    margin: 0;
    font-weight: 600;
}

.permission-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 8px;
}

.form-check {
    padding: 5px 0;
}

.form-check-label {
    font-weight: 500;
    color: #5a5c69;
}

.module-checkbox {
    margin-right: 8px;
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // Module checkbox functionality
    $('.module-checkbox').change(function() {
        const module = $(this).data('module');
        const isChecked = $(this).is(':checked');
        
        $(`.permission-checkbox[data-module="${module}"]`).prop('checked', isChecked);
    });
    
    // Update module checkbox when individual permissions change
    $('.permission-checkbox').change(function() {
        const module = $(this).data('module');
        const totalPermissions = $(`.permission-checkbox[data-module="${module}"]`).length;
        const checkedPermissions = $(`.permission-checkbox[data-module="${module}"]:checked`).length;
        
        const moduleCheckbox = $(`.module-checkbox[data-module="${module}"]`);
        
        if (checkedPermissions === 0) {
            moduleCheckbox.prop('checked', false).prop('indeterminate', false);
        } else if (checkedPermissions === totalPermissions) {
            moduleCheckbox.prop('checked', true).prop('indeterminate', false);
        } else {
            moduleCheckbox.prop('checked', false).prop('indeterminate', true);
        }
    });
    
    // Initialize module checkboxes state
    $('.module-checkbox').each(function() {
        $(this).trigger('change');
    });
});
</script>
@endpush
@endsection
