@extends('layouts.app')

@section('title', 'Edit Role - JMD Traders')
@section('page-title', 'Edit Role')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Edit Role: {{ $role->name }}</h1>
        <div>
            <a href="{{ route('admin.roles.show', $role) }}" class="btn btn-info btn-sm">
                <i class="fas fa-eye"></i> View Role
            </a>
            <a href="{{ route('admin.roles.index') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Back to Roles
            </a>
        </div>
    </div>

    @if($errors->any())
        <div class="alert alert-danger">
            <ul class="mb-0">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <div class="alert alert-info">
        <i class="fas fa-info-circle"></i>
        <strong>Note:</strong> When you update role permissions, the changes will be applied immediately to all users with this role.
        Users may need to refresh their browser or navigate to a new page to see the updated permissions in the interface.
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Role Information</h6>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.roles.update', $role) }}" method="POST">
                @csrf
                @method('PUT')
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="name">Role Name *</label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   id="name" name="name" value="{{ old('name', $role->name) }}" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    

                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="status">Status *</label>
                            <select class="form-control @error('status') is-invalid @enderror" id="status" name="status" required>
                                <option value="active" {{ old('status', $role->status) == 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ old('status', $role->status) == 'inactive' ? 'selected' : '' }}>Inactive</option>
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="description">Description</label>
                    <textarea class="form-control @error('description') is-invalid @enderror" 
                              id="description" name="description" rows="3">{{ old('description', $role->description) }}</textarea>
                    @error('description')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="form-group">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <label class="mb-0">Permissions</label>
                        <div>
                            <button type="button" class="btn btn-sm btn-outline-primary" id="selectAllPermissions">
                                <i class="fas fa-check-square"></i> Select All
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary ml-1" id="deselectAllPermissions">
                                <i class="fas fa-square"></i> Deselect All
                            </button>
                        </div>
                    </div>
                    <div class="permissions-container">
                        @if(isset($groupedPermissions) && $groupedPermissions->count() > 0)
                            @foreach($groupedPermissions as $module => $modulePermissions)
                            <div class="permission-module">
                                <div class="module-header">
                                    <h6 class="text-primary">
                                        <input type="checkbox" class="module-checkbox" data-module="{{ $module }}">
                                        {{ ucfirst($module) }} Module
                                        <span class="badge badge-info ml-2">{{ $modulePermissions->count() }} permissions</span>
                                    </h6>
                                </div>
                                <div class="permission-list">
                                    @foreach($modulePermissions as $permission)
                                    <div class="form-check">
                                        <input class="form-check-input permission-checkbox"
                                               type="checkbox"
                                               name="permissions[]"
                                               value="{{ $permission->id }}"
                                               id="permission_{{ $permission->id }}"
                                               data-module="{{ $module }}"
                                               {{ in_array($permission->id, old('permissions', $rolePermissions ?? [])) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="permission_{{ $permission->id }}">
                                            {{ $permission->name }}
                                            @if($permission->description)
                                                <small class="text-muted d-block">{{ $permission->description }}</small>
                                            @endif
                                        </label>
                                    </div>
                                    @endforeach
                                </div>
                            </div>
                            @endforeach
                        @else
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                No permissions available. Please contact your administrator.
                            </div>
                        @endif
                    </div>
                </div>

                <div class="form-group">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Role
                            </button>
                            <a href="{{ route('admin.roles.show', $role) }}" class="btn btn-info">
                                <i class="fas fa-eye"></i> View Role
                            </a>
                        </div>
                        <div>
                            <a href="{{ route('admin.roles.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Roles
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Current Role Information -->
    <div class="row">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">Current Role Information</h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Name:</strong></td>
                            <td>{{ $role->name }}</td>
                        </tr>
                        <tr>
                            <td><strong>Slug:</strong></td>
                            <td><code>{{ $role->slug }}</code></td>
                        </tr>
                        <tr>
                            <td><strong>Permissions Count:</strong></td>
                            <td><span class="badge text-dark">{{ $role->permissions->count() }} permissions</span></td>
                        </tr>
                        <tr>
                            <td><strong>Status:</strong></td>
                            <td><span class="badge text-dark">{{ ucfirst($role->status) }}</span></td>
                        </tr>
                        <tr>
                            <td><strong>Created:</strong></td>
                            <td>{{ $role->created_at->format('M d, Y H:i') }}</td>
                        </tr>
                        <tr>
                            <td><strong>Updated:</strong></td>
                            <td>{{ $role->updated_at->format('M d, Y H:i') }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">Current Permissions</h6>
                </div>
                <div class="card-body">
                    <div class="current-permissions">
                        @if($role->permissions->count() > 0)
                            @foreach($role->permissions->groupBy('module') as $module => $perms)
                                <div class="mb-3">
                                    <h6 class="text-primary">{{ ucfirst($module) }}</h6>
                                    @foreach($perms as $perm)
                                        <span class="badge badge-secondary mr-1 mb-1 text-dark">{{ $perm->name }}</span>
                                    @endforeach
                                </div>
                            @endforeach
                        @else
                            <p class="text-muted">No permissions assigned to this role.</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
.permissions-container {
    border: 1px solid #e3e6f0;
    border-radius: 8px;
    padding: 15px;
    background-color: #f8f9fc;
    max-height: 400px;
    overflow-y: auto;
}

.permission-module {
    margin-bottom: 20px;
    padding: 15px;
    background-color: white;
    border-radius: 6px;
    border: 1px solid #e3e6f0;
}

.permission-module:last-child {
    margin-bottom: 0;
}

.module-header {
    margin-bottom: 10px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e3e6f0;
}

.module-header h6 {
    margin: 0;
    font-weight: 600;
}

.permission-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 8px;
}

.form-check {
    padding: 5px 0;
}

.form-check-label {
    font-weight: 500;
    color: #5a5c69;
}

.module-checkbox {
    margin-right: 8px;
}

.current-permissions .badge {
    font-size: 0.75rem;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Module checkbox functionality
    document.querySelectorAll('.module-checkbox').forEach(function(moduleCheckbox) {
        moduleCheckbox.addEventListener('change', function() {
            const module = this.dataset.module;
            const isChecked = this.checked;

            document.querySelectorAll(`.permission-checkbox[data-module="${module}"]`).forEach(function(permCheckbox) {
                permCheckbox.checked = isChecked;
            });
        });
    });

    // Update module checkbox when individual permissions change
    document.querySelectorAll('.permission-checkbox').forEach(function(permCheckbox) {
        permCheckbox.addEventListener('change', function() {
            const module = this.dataset.module;
            const totalPermissions = document.querySelectorAll(`.permission-checkbox[data-module="${module}"]`).length;
            const checkedPermissions = document.querySelectorAll(`.permission-checkbox[data-module="${module}"]:checked`).length;

            const moduleCheckbox = document.querySelector(`.module-checkbox[data-module="${module}"]`);

            if (checkedPermissions === 0) {
                moduleCheckbox.checked = false;
                moduleCheckbox.indeterminate = false;
            } else if (checkedPermissions === totalPermissions) {
                moduleCheckbox.checked = true;
                moduleCheckbox.indeterminate = false;
            } else {
                moduleCheckbox.checked = false;
                moduleCheckbox.indeterminate = true;
            }
        });
    });

    // Initialize module checkboxes state
    document.querySelectorAll('.permission-checkbox').forEach(function(permCheckbox) {
        permCheckbox.dispatchEvent(new Event('change'));
    });

    // Select All functionality
    document.getElementById('selectAllPermissions').addEventListener('click', function() {
        document.querySelectorAll('.permission-checkbox').forEach(function(checkbox) {
            checkbox.checked = true;
            checkbox.dispatchEvent(new Event('change'));
        });
    });

    // Deselect All functionality
    document.getElementById('deselectAllPermissions').addEventListener('click', function() {
        document.querySelectorAll('.permission-checkbox').forEach(function(checkbox) {
            checkbox.checked = false;
            checkbox.dispatchEvent(new Event('change'));
        });
    });

    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        const roleName = document.getElementById('name').value.trim();
        if (!roleName) {
            e.preventDefault();
            alert('Please enter a role name.');
            return false;
        }
    });
});
</script>
@endpush
@endsection
