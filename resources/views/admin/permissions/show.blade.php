@extends('layouts.app')

@section('title', 'View Permission - JMD Traders')
@section('page-title', 'View Permission')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Permission Details: {{ $permission->name }}</h1>
        <div>
            <a href="{{ route('admin.permissions.edit', $permission) }}" class="btn btn-warning btn-sm">
                <i class="fas fa-edit"></i> Edit Permission
            </a>
            <a href="{{ route('admin.permissions.index') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Back to Permissions
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Permission Information -->
        <div class="col-md-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Permission Information</h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Name:</strong></td>
                            <td>{{ $permission->name }}</td>
                        </tr>
                        <tr>
                            <td><strong>Slug:</strong></td>
                            <td><code>{{ $permission->slug }}</code></td>
                        </tr>
                        <tr>
                            <td><strong>Module:</strong></td>
                            <td>
                                @if($permission->module)
                                    <span class="badge badge-info">{{ ucfirst($permission->module) }}</span>
                                @else
                                    <span class="text-muted">No module</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Status:</strong></td>
                            <td>
                                @if($permission->status === 'active')
                                    <span class="badge badge-success">Active</span>
                                @else
                                    <span class="badge badge-secondary">Inactive</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Description:</strong></td>
                            <td>{{ $permission->description ?? 'No description provided' }}</td>
                        </tr>
                        <tr>
                            <td><strong>Created:</strong></td>
                            <td>{{ $permission->created_at->format('M d, Y H:i') }}</td>
                        </tr>
                        <tr>
                            <td><strong>Updated:</strong></td>
                            <td>{{ $permission->updated_at->format('M d, Y H:i') }}</td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Permission Statistics -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-right">
                                <h4 class="text-primary">{{ $permission->roles->count() }}</h4>
                                <small class="text-muted">Roles</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">{{ $permission->users_count ?? 0 }}</h4>
                            <small class="text-muted">Users</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.permissions.edit', $permission) }}" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit"></i> Edit Permission
                        </a>
                        
                        <form action="{{ route('admin.permissions.toggle-status', $permission) }}" method="POST" class="d-inline">
                            @csrf
                            @method('PATCH')
                            <button type="submit" class="btn btn-{{ $permission->status === 'active' ? 'secondary' : 'success' }} btn-sm w-100">
                                <i class="fas fa-toggle-{{ $permission->status === 'active' ? 'off' : 'on' }}"></i>
                                {{ $permission->status === 'active' ? 'Deactivate' : 'Activate' }}
                            </button>
                        </form>
                        
                        <form action="{{ route('admin.permissions.destroy', $permission) }}" method="POST" 
                              onsubmit="return confirm('Are you sure you want to delete this permission?')" class="d-inline">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger btn-sm w-100">
                                <i class="fas fa-trash"></i> Delete Permission
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Roles with this Permission -->
        <div class="col-md-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        Roles with this Permission ({{ $permission->roles->count() }})
                    </h6>
                </div>
                <div class="card-body">
                    @if($permission->roles->count() > 0)
                        <div class="roles-display">
                            <div class="row">
                                @foreach($permission->roles as $role)
                                <div class="col-md-6 mb-3">
                                    <div class="role-card">
                                        <div class="card border-left-primary">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div>
                                                        <h6 class="card-title mb-1">{{ $role->name }}</h6>
                                                        <p class="card-text text-muted small mb-2">{{ $role->description }}</p>
                                                        <div class="role-meta">
                                                            <span class="badge badge-info">Level {{ $role->level }}</span>
                                                            <span class="badge badge-{{ $role->status === 'active' ? 'success' : 'secondary' }}">
                                                                {{ ucfirst($role->status) }}
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <a href="{{ route('admin.roles.show', $role) }}" class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-user-tag fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Roles Assigned</h5>
                            <p class="text-muted">This permission is not assigned to any roles yet.</p>
                            <a href="{{ route('admin.roles.index') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Assign to Roles
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Related Permissions in Same Module -->
    @if($permission->module)
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-info">
                Related Permissions in {{ ucfirst($permission->module) }} Module
            </h6>
        </div>
        <div class="card-body">
            @php
                $relatedPermissions = \App\Models\Permission::where('module', $permission->module)
                                                          ->where('id', '!=', $permission->id)
                                                          ->get();
            @endphp
            
            @if($relatedPermissions->count() > 0)
                <div class="row">
                    @foreach($relatedPermissions as $relatedPerm)
                    <div class="col-md-4 mb-3">
                        <div class="card border-left-info">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title mb-1">{{ $relatedPerm->name }}</h6>
                                        <p class="card-text text-muted small mb-2">{{ $relatedPerm->description }}</p>
                                        <div class="permission-meta">
                                            <span class="badge badge-{{ $relatedPerm->status === 'active' ? 'success' : 'secondary' }}">
                                                {{ ucfirst($relatedPerm->status) }}
                                            </span>
                                            <span class="badge badge-secondary">{{ $relatedPerm->roles->count() }} roles</span>
                                        </div>
                                    </div>
                                    <div>
                                        <a href="{{ route('admin.permissions.show', $relatedPerm) }}" class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-4">
                    <i class="fas fa-key fa-2x text-muted mb-3"></i>
                    <h6 class="text-muted">No Related Permissions</h6>
                    <p class="text-muted">This is the only permission in the {{ ucfirst($permission->module) }} module.</p>
                </div>
            @endif
        </div>
    </div>
    @endif
</div>

@push('styles')
<style>
.role-card .card {
    transition: all 0.3s ease;
}

.role-card .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.d-grid {
    display: grid;
}

.gap-2 {
    gap: 0.5rem;
}

.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
</style>
@endpush
@endsection
