@extends('layouts.app')

@section('title', 'Edit Permission - JMD Traders')
@section('page-title', 'Edit Permission')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Edit Permission: {{ $permission->name }}</h1>
        <div>
            <a href="{{ route('admin.permissions.show', $permission) }}" class="btn btn-info btn-sm">
                <i class="fas fa-eye"></i> View Permission
            </a>
            <a href="{{ route('admin.permissions.index') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Back to Permissions
            </a>
        </div>
    </div>

    @if($errors->any())
        <div class="alert alert-danger">
            <ul class="mb-0">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <div class="row">
        <!-- Edit Form -->
        <div class="col-md-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Permission Information</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.permissions.update', $permission) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <div class="form-group">
                            <label for="name">Permission Name *</label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   id="name" name="name" value="{{ old('name', $permission->name) }}" required
                                   placeholder="e.g., View Users, Create Products">
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="form-group">
                            <label for="module">Module</label>
                            <select class="form-control @error('module') is-invalid @enderror" id="module" name="module">
                                <option value="">Select Module</option>
                                @foreach($modules as $module)
                                    <option value="{{ $module }}" {{ old('module', $permission->module) == $module ? 'selected' : '' }}>
                                        {{ ucfirst($module) }}
                                    </option>
                                @endforeach
                                <option value="new">+ Add New Module</option>
                            </select>
                            <input type="text" class="form-control mt-2" id="new_module" name="new_module" 
                                   placeholder="Enter new module name" style="display: none;">
                            @error('module')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3" 
                                      placeholder="Describe what this permission allows">{{ old('description', $permission->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="form-group">
                            <label for="status">Status *</label>
                            <select class="form-control @error('status') is-invalid @enderror" id="status" name="status" required>
                                <option value="active" {{ old('status', $permission->status) == 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ old('status', $permission->status) == 'inactive' ? 'selected' : '' }}>Inactive</option>
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Permission
                            </button>
                            <a href="{{ route('admin.permissions.show', $permission) }}" class="btn btn-info">
                                <i class="fas fa-eye"></i> View Permission
                            </a>
                            <a href="{{ route('admin.permissions.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Current Information -->
        <div class="col-md-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">Current Information</h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Name:</strong></td>
                            <td>{{ $permission->name }}</td>
                        </tr>
                        <tr>
                            <td><strong>Slug:</strong></td>
                            <td><code>{{ $permission->slug }}</code></td>
                        </tr>
                        <tr>
                            <td><strong>Module:</strong></td>
                            <td>
                                @if($permission->module)
                                    <span class="badge badge-info">{{ ucfirst($permission->module) }}</span>
                                @else
                                    <span class="text-muted">No module</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Status:</strong></td>
                            <td>
                                <span class="badge badge-{{ $permission->status === 'active' ? 'success' : 'secondary' }}">
                                    {{ ucfirst($permission->status) }}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Created:</strong></td>
                            <td>{{ $permission->created_at->format('M d, Y H:i') }}</td>
                        </tr>
                        <tr>
                            <td><strong>Updated:</strong></td>
                            <td>{{ $permission->updated_at->format('M d, Y H:i') }}</td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Usage Statistics -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">Usage Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-right">
                                <h4 class="text-primary">{{ $permission->roles->count() }}</h4>
                                <small class="text-muted">Roles</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">{{ $permission->users_count ?? 0 }}</h4>
                            <small class="text-muted">Users</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Assigned Roles -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">Assigned Roles</h6>
                </div>
                <div class="card-body">
                    @if($permission->roles->count() > 0)
                        <div class="assigned-roles">
                            @foreach($permission->roles as $role)
                            <div class="role-item mb-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>{{ $role->name }}</strong>
                                        <br>
                                        <small class="text-muted">Level {{ $role->level }}</small>
                                    </div>
                                    <div>
                                        <span class="badge badge-{{ $role->status === 'active' ? 'success' : 'secondary' }}">
                                            {{ ucfirst($role->status) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            @if(!$loop->last)
                                <hr class="my-2">
                            @endif
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-3">
                            <i class="fas fa-user-tag fa-2x text-muted mb-2"></i>
                            <p class="text-muted small">Not assigned to any roles</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Related Permissions -->
    @if($permission->module)
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-info">
                Related Permissions in {{ ucfirst($permission->module) }} Module
            </h6>
        </div>
        <div class="card-body">
            @php
                $relatedPermissions = \App\Models\Permission::where('module', $permission->module)
                                                          ->where('id', '!=', $permission->id)
                                                          ->get();
            @endphp
            
            @if($relatedPermissions->count() > 0)
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Status</th>
                                <th>Roles</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($relatedPermissions as $relatedPerm)
                            <tr>
                                <td>
                                    <strong>{{ $relatedPerm->name }}</strong>
                                    @if($relatedPerm->description)
                                        <br><small class="text-muted">{{ $relatedPerm->description }}</small>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge badge-{{ $relatedPerm->status === 'active' ? 'success' : 'secondary' }}">
                                        {{ ucfirst($relatedPerm->status) }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge badge-info">{{ $relatedPerm->roles->count() }} roles</span>
                                </td>
                                <td>
                                    <a href="{{ route('admin.permissions.show', $relatedPerm) }}" class="btn btn-sm btn-outline-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.permissions.edit', $relatedPerm) }}" class="btn btn-sm btn-outline-warning">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-4">
                    <i class="fas fa-key fa-2x text-muted mb-3"></i>
                    <h6 class="text-muted">No Related Permissions</h6>
                    <p class="text-muted">This is the only permission in the {{ ucfirst($permission->module) }} module.</p>
                </div>
            @endif
        </div>
    </div>
    @endif
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Handle new module selection
    $('#module').change(function() {
        if ($(this).val() === 'new') {
            $('#new_module').show().attr('required', true);
            $(this).attr('name', '');
            $('#new_module').attr('name', 'module');
        } else {
            $('#new_module').hide().attr('required', false);
            $(this).attr('name', 'module');
            $('#new_module').attr('name', '');
        }
    });
});
</script>
@endpush
@endsection
