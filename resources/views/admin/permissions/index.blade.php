@extends('layouts.app')

@section('title', 'Permissions Management - JMD Traders')
@section('page-title', 'Permissions Management')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Permissions Management</h1>
        <div>
            <a href="{{ route('admin.permissions.create') }}" class="btn btn-primary btn-sm">
                <i class="fas fa-plus"></i> Create New Permission
            </a>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">All Permissions</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="permissionsTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Module</th>
                            <th>Description</th>
                            <th>Roles</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($permissions as $permission)
                        <tr>
                            <td>
                                <strong>{{ $permission->name }}</strong>
                                <br>
                                <small class="text-muted">{{ $permission->slug }}</small>
                            </td>
                            <td>
                                @if($permission->module)
                                    <span class="badge badge-info">{{ ucfirst($permission->module) }}</span>
                                @else
                                    <span class="text-muted">No module</span>
                                @endif
                            </td>
                            <td>{{ $permission->description ?? 'No description' }}</td>
                            <td>
                                <span class="badge badge-secondary">{{ $permission->roles->count() }} roles</span>
                            </td>
                            <td>
                                @if($permission->status === 'active')
                                    <span class="badge badge-success">Active</span>
                                @else
                                    <span class="badge badge-secondary">Inactive</span>
                                @endif
                            </td>
                            <td>{{ $permission->created_at->format('M d, Y') }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.permissions.show', $permission) }}" class="btn btn-sm btn-outline-info" title="View">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.permissions.edit', $permission) }}" class="btn btn-sm btn-outline-warning" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('admin.permissions.destroy', $permission) }}" method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this permission?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="text-center">No permissions found.</td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $permissions->links() }}
            </div>
        </div>
    </div>

    <!-- Permissions by Module -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Permissions by Module</h6>
        </div>
        <div class="card-body">
            <div class="row">
                @foreach($groupedPermissions as $module => $modulePermissions)
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="card border-left-primary">
                        <div class="card-body">
                            <h6 class="card-title text-primary">
                                <i class="fas fa-cube"></i> {{ ucfirst($module) }}
                            </h6>
                            <p class="card-text">
                                <span class="badge badge-info">{{ $modulePermissions->count() }} permissions</span>
                            </p>
                            <div class="small">
                                @foreach($modulePermissions->take(3) as $perm)
                                    <div>• {{ $perm->name }}</div>
                                @endforeach
                                @if($modulePermissions->count() > 3)
                                    <div class="text-muted">... and {{ $modulePermissions->count() - 3 }} more</div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>

    <!-- Permission Statistics -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Permissions</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $permissions->total() }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-key fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Active Permissions</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $permissions->where('status', 'active')->count() }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Modules</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $groupedPermissions->count() }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-cubes fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Total Roles</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ \App\Models\Role::count() }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-tag fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    $('#permissionsTable').DataTable({
        "pageLength": 15,
        "ordering": true,
        "searching": true,
        "columnDefs": [
            { "orderable": false, "targets": [6] } // Actions column
        ]
    });
});
</script>
@endpush
@endsection
