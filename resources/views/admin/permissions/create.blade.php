@extends('layouts.app')

@section('title', 'Create Permission - JMD Traders')
@section('page-title', 'Create Permission')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Create New Permission</h1>
        <div>
            <a href="{{ route('admin.permissions.index') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Back to Permissions
            </a>
        </div>
    </div>

    @if($errors->any())
        <div class="alert alert-danger">
            <ul class="mb-0">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <div class="row">
        <!-- Single Permission Form -->
        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Single Permission</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.permissions.store') }}" method="POST">
                        @csrf
                        
                        <div class="form-group">
                            <label for="name">Permission Name *</label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   id="name" name="name" value="{{ old('name') }}" required
                                   placeholder="e.g., View Users, Create Products">
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="form-group">
                            <label for="module">Module</label>
                            <select class="form-control @error('module') is-invalid @enderror" id="module" name="module">
                                <option value="">Select Module</option>
                                @foreach($modules as $module)
                                    <option value="{{ $module }}" {{ old('module') == $module ? 'selected' : '' }}>
                                        {{ ucfirst($module) }}
                                    </option>
                                @endforeach
                                <option value="new">+ Add New Module</option>
                            </select>
                            <input type="text" class="form-control mt-2" id="new_module" name="new_module" 
                                   placeholder="Enter new module name" style="display: none;">
                            @error('module')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3" 
                                      placeholder="Describe what this permission allows">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="form-group">
                            <label for="status">Status *</label>
                            <select class="form-control @error('status') is-invalid @enderror" id="status" name="status" required>
                                <option value="active" {{ old('status', 'active') == 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Create Permission
                            </button>
                            <a href="{{ route('admin.permissions.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Bulk Permission Creation -->
        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">Bulk Permission Creation</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.permissions.bulk-create') }}" method="POST">
                        @csrf
                        
                        <div class="form-group">
                            <label for="bulk_module">Module *</label>
                            <input type="text" class="form-control" id="bulk_module" name="module" 
                                   placeholder="e.g., products, customers, reports" required>
                            <small class="form-text text-muted">Enter the module name for which you want to create permissions</small>
                        </div>
                        
                        <div class="form-group">
                            <label>Actions *</label>
                            <div class="row">
                                <div class="col-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="actions[]" value="view" id="action_view">
                                        <label class="form-check-label" for="action_view">View</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="actions[]" value="create" id="action_create">
                                        <label class="form-check-label" for="action_create">Create</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="actions[]" value="edit" id="action_edit">
                                        <label class="form-check-label" for="action_edit">Edit</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="actions[]" value="delete" id="action_delete">
                                        <label class="form-check-label" for="action_delete">Delete</label>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="actions[]" value="export" id="action_export">
                                        <label class="form-check-label" for="action_export">Export</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="actions[]" value="import" id="action_import">
                                        <label class="form-check-label" for="action_import">Import</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="actions[]" value="print" id="action_print">
                                        <label class="form-check-label" for="action_print">Print</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="actions[]" value="approve" id="action_approve">
                                        <label class="form-check-label" for="action_approve">Approve</label>
                                    </div>
                                </div>
                            </div>
                            <small class="form-text text-muted">Select the actions you want to create permissions for</small>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-plus-circle"></i> Create Bulk Permissions
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="btn-group-vertical btn-block">
                        <button class="btn btn-outline-primary btn-sm" onclick="createCRUDPermissions('users')">
                            <i class="fas fa-users"></i> Create User CRUD Permissions
                        </button>
                        <button class="btn btn-outline-success btn-sm" onclick="createCRUDPermissions('products')">
                            <i class="fas fa-box"></i> Create Product CRUD Permissions
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="createCRUDPermissions('customers')">
                            <i class="fas fa-user-tie"></i> Create Customer CRUD Permissions
                        </button>
                        <button class="btn btn-outline-warning btn-sm" onclick="createCRUDPermissions('reports')">
                            <i class="fas fa-chart-bar"></i> Create Report Permissions
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Handle new module selection
    $('#module').change(function() {
        if ($(this).val() === 'new') {
            $('#new_module').show().attr('required', true);
            $(this).attr('name', '');
            $('#new_module').attr('name', 'module');
        } else {
            $('#new_module').hide().attr('required', false);
            $(this).attr('name', 'module');
            $('#new_module').attr('name', '');
        }
    });
});

function createCRUDPermissions(module) {
    // Fill the bulk form
    $('#bulk_module').val(module);
    
    // Check common CRUD actions
    $('#action_view, #action_create, #action_edit, #action_delete').prop('checked', true);
    
    // Scroll to bulk form
    $('html, body').animate({
        scrollTop: $('.card:contains("Bulk Permission Creation")').offset().top - 100
    }, 500);
}
</script>
@endpush
@endsection
