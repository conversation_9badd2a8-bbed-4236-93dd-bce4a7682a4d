@extends('layouts.app')

@section('title', 'Permission Checker - JMD Traders')
@section('page-title', 'Permission Checker')

@section('content')
<div class="container-fluid">
    <!-- Page Header with Refresh Button -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-shield-alt"></i> Permission Checker
        </h1>
        <button type="button" class="btn btn-primary" onclick="refreshPermissions()">
            <i class="fas fa-sync-alt"></i> Refresh Permissions
        </button>
    </div>

    <div class="row">
        <!-- User Info -->
        <div class="col-lg-4 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">User Information</h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Name:</strong></td>
                            <td>{{ auth()->user()->name }}</td>
                        </tr>
                        <tr>
                            <td><strong>Email:</strong></td>
                            <td>{{ auth()->user()->email }}</td>
                        </tr>
                        <tr>
                            <td><strong>Current Company Role:</strong></td>
                            <td>
                                @if(auth()->user()->userRole)
                                    <span class="badge badge-primary">{{ auth()->user()->userRole->name }}</span>
                                @else
                                    <span class="text-muted">No role assigned in current company</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Current Company:</strong></td>
                            <td>
                                @if(auth()->user()->currentCompany)
                                    <span class="badge badge-success">{{ auth()->user()->currentCompany->name }}</span>
                                @else
                                    <span class="badge badge-warning">No company selected</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Company Access:</strong></td>
                            <td>
                                @hascompany
                                    <span class="badge badge-success">
                                        <i class="fas fa-check"></i> Active
                                    </span>
                                @else
                                    <span class="badge badge-danger">
                                        <i class="fas fa-times"></i> No Access
                                    </span>
                                @endhascompany
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- Company Roles -->
        <div class="col-lg-8 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">Company Assignments</h6>
                </div>
                <div class="card-body">
                    @if(auth()->user()->companies->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Company</th>
                                        <th>Role</th>
                                        <th>Status</th>
                                        <th>Default</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach(auth()->user()->companies as $company)
                                    <tr class="{{ $company->id == auth()->user()->current_company_id ? 'table-success' : '' }}">
                                        <td>
                                            {{ $company->name }}
                                            @if($company->id == auth()->user()->current_company_id)
                                                <span class="badge badge-primary ml-2">Current</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($company->pivot->role_id)
                                                @php
                                                    $role = \App\Models\Role::find($company->pivot->role_id);
                                                @endphp
                                                @if($role)
                                                    <span class="badge badge-info">{{ $role->name }}</span>
                                                @else
                                                    <span class="text-muted">Role not found</span>
                                                @endif
                                            @else
                                                <span class="text-muted">{{ $company->pivot->role ?? 'No role' }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge badge-{{ $company->pivot->status == 'active' ? 'success' : 'secondary' }}">
                                                {{ ucfirst($company->pivot->status) }}
                                            </span>
                                        </td>
                                        <td>
                                            @if($company->pivot->is_default)
                                                <i class="fas fa-check text-success"></i>
                                            @else
                                                <i class="fas fa-times text-muted"></i>
                                            @endif
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            You are not assigned to any companies. Contact your administrator.
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Permissions -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">Your Permissions</h6>
                </div>
                <div class="card-body">
                    @hascompany
                        @php
                            $permissions = auth()->user()->getAllPermissions();
                            $groupedPermissions = $permissions->groupBy('module');
                        @endphp
                        
                        @if($permissions->count() > 0)
                            <div class="row">
                                @foreach($groupedPermissions as $module => $modulePermissions)
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card border-left-primary h-100">
                                        <div class="card-header bg-light">
                                            <h6 class="m-0 text-primary">
                                                <i class="fas fa-shield-alt"></i>
                                                {{ ucfirst($module) }} Module
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            @foreach($modulePermissions as $permission)
                                                <span class="badge badge-success mb-1">{{ $permission->name }}</span>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                            
                            <div class="mt-4">
                                <h6>Permission Summary:</h6>
                                <p class="text-muted">
                                    You have <strong>{{ $permissions->count() }}</strong> permissions across 
                                    <strong>{{ $groupedPermissions->count() }}</strong> modules.
                                </p>
                            </div>
                        @else
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                You don't have any specific permissions assigned. Contact your administrator.
                            </div>
                        @endif
                    @else
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>No Company Access</strong><br>
                            You need to be assigned to a company to view your permissions.
                        </div>
                    @endhascompany
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Permission Tests -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">Quick Permission Tests</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <h6>Dashboard Access</h6>
                            @permission('dashboard.view')
                                <span class="badge badge-success"><i class="fas fa-check"></i> Allowed</span>
                            @else
                                <span class="badge badge-danger"><i class="fas fa-times"></i> Denied</span>
                            @endpermission
                        </div>
                        
                        <div class="col-md-3">
                            <h6>User Management</h6>
                            @anypermission(['users.view', 'users.create', 'users.edit'])
                                <span class="badge badge-success"><i class="fas fa-check"></i> Allowed</span>
                            @else
                                <span class="badge badge-danger"><i class="fas fa-times"></i> Denied</span>
                            @endanypermission
                        </div>
                        
                        <div class="col-md-3">
                            <h6>Sales Access</h6>
                            @anypermission(['sales.view', 'sales.create'])
                                <span class="badge badge-success"><i class="fas fa-check"></i> Allowed</span>
                            @else
                                <span class="badge badge-danger"><i class="fas fa-times"></i> Denied</span>
                            @endanypermission
                        </div>
                        
                        <div class="col-md-3">
                            <h6>Administration</h6>
                            @anypermission(['roles.view', 'permissions.view', 'companies.view'])
                                <span class="badge badge-success"><i class="fas fa-check"></i> Allowed</span>
                            @else
                                <span class="badge badge-danger"><i class="fas fa-times"></i> Denied</span>
                            @endanypermission
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .border-left-primary {
        border-left: 0.25rem solid #4e73df !important;
    }
    
    .table-success {
        background-color: rgba(40, 167, 69, 0.1);
    }
    
    .badge {
        font-size: 0.75em;
    }
</style>
@endpush

@push('scripts')
<script>
function refreshPermissions() {
    const button = event.target.closest('button');
    const originalText = button.innerHTML;

    // Show loading state
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
    button.disabled = true;

    // Make AJAX request to refresh permissions
    fetch('{{ route("refresh.permissions") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            const alert = document.createElement('div');
            alert.className = 'alert alert-success alert-dismissible fade show';
            alert.innerHTML = `
                <i class="fas fa-check-circle"></i> ${data.message}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            `;

            // Insert alert at the top of the container
            const container = document.querySelector('.container-fluid');
            container.insertBefore(alert, container.firstChild.nextSibling);

            // Auto-hide after 3 seconds
            setTimeout(() => {
                alert.remove();
            }, 3000);

            // Reload the page to show updated permissions
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while refreshing permissions.');
    })
    .finally(() => {
        // Restore button state
        button.innerHTML = originalText;
        button.disabled = false;
    });
}
</script>
@endpush
