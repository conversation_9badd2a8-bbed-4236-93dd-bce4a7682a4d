@extends('layouts.app')

@section('title', 'Pagination Demo')

@section('content')
<div class="container-fluid px-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-list me-2"></i>Pagination Design Demo
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5 class="text-primary">🎨 Custom Pagination Features</h5>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Modern, responsive design</li>
                                <li><i class="fas fa-check text-success me-2"></i>Page size selector (10, 25, 50, 100)</li>
                                <li><i class="fas fa-check text-success me-2"></i>Smart page number display</li>
                                <li><i class="fas fa-check text-success me-2"></i>Mobile-friendly controls</li>
                                <li><i class="fas fa-check text-success me-2"></i>Hover effects and animations</li>
                                <li><i class="fas fa-check text-success me-2"></i>Results count display</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5 class="text-info">📱 Responsive Features</h5>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-mobile-alt text-info me-2"></i>Adapts to screen size</li>
                                <li><i class="fas fa-tablet-alt text-info me-2"></i>Touch-friendly buttons</li>
                                <li><i class="fas fa-desktop text-info me-2"></i>Desktop optimized</li>
                                <li><i class="fas fa-arrows-alt text-info me-2"></i>Flexible layout</li>
                            </ul>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Implementation:</strong> The custom pagination is now active on all master pages (Products, Customers, Users, Categories).
                        It includes search functionality, filtering, and per-page selection.
                    </div>

                    <!-- Sample Table -->
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead class="table-primary">
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Category</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for($i = 1; $i <= 25; $i++)
                                <tr>
                                    <td>{{ $i }}</td>
                                    <td>Sample Item {{ $i }}</td>
                                    <td>
                                        <span class="badge bg-primary">Category {{ ($i % 5) + 1 }}</span>
                                    </td>
                                    <td>
                                        <span class="badge {{ $i % 2 == 0 ? 'bg-success' : 'bg-warning' }}">
                                            {{ $i % 2 == 0 ? 'Active' : 'Pending' }}
                                        </span>
                                    </td>
                                    <td>{{ now()->subDays($i)->format('M d, Y') }}</td>
                                </tr>
                                @endfor
                            </tbody>
                        </table>
                    </div>

                    <!-- Demo Pagination -->
                    <div class="mt-4">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-eye me-2"></i>Pagination Preview
                        </h6>
                        
                        <!-- Custom Pagination Demo -->
                        <nav class="pagination-wrapper" aria-label="Pagination Navigation">
                            <div class="pagination-info">
                                <span class="pagination-text">
                                    Showing 1 to 25 of 792 results
                                </span>
                            </div>
                            
                            <ul class="pagination-list">
                                <li class="pagination-item disabled">
                                    <span class="pagination-link">
                                        <i class="fas fa-chevron-left"></i>
                                        <span class="pagination-text-mobile">Previous</span>
                                    </span>
                                </li>
                                
                                <li class="pagination-item active">
                                    <span class="pagination-link">1</span>
                                </li>
                                <li class="pagination-item">
                                    <a class="pagination-link" href="#">2</a>
                                </li>
                                <li class="pagination-item">
                                    <a class="pagination-link" href="#">3</a>
                                </li>
                                <li class="pagination-item disabled">
                                    <span class="pagination-link pagination-dots">...</span>
                                </li>
                                <li class="pagination-item">
                                    <a class="pagination-link" href="#">32</a>
                                </li>
                                
                                <li class="pagination-item">
                                    <a class="pagination-link" href="#" rel="next">
                                        <span class="pagination-text-mobile">Next</span>
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            </ul>
                            
                            <div class="pagination-size-selector">
                                <select class="form-select form-select-sm">
                                    <option value="10">10 per page</option>
                                    <option value="25" selected>25 per page</option>
                                    <option value="50">50 per page</option>
                                    <option value="100">100 per page</option>
                                </select>
                            </div>
                        </nav>
                    </div>

                    <div class="mt-4">
                        <h6 class="text-success">✅ Updated Pages</h6>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="card border-success">
                                    <div class="card-body text-center">
                                        <i class="fas fa-box fa-2x text-success mb-2"></i>
                                        <h6>Products</h6>
                                        <small class="text-muted">Search, Filter, Pagination</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card border-info">
                                    <div class="card-body text-center">
                                        <i class="fas fa-users fa-2x text-info mb-2"></i>
                                        <h6>Customers</h6>
                                        <small class="text-muted">Custom Pagination</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card border-warning">
                                    <div class="card-body text-center">
                                        <i class="fas fa-user-tie fa-2x text-warning mb-2"></i>
                                        <h6>Users</h6>
                                        <small class="text-muted">Custom Pagination</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card border-primary">
                                    <div class="card-body text-center">
                                        <i class="fas fa-tags fa-2x text-primary mb-2"></i>
                                        <h6>Categories</h6>
                                        <small class="text-muted">Custom Pagination</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Include the pagination styles here for demo */
.pagination-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
    padding: 20px 0;
    margin-top: 20px;
    border-top: 1px solid #e3e6f0;
}

.pagination-info {
    color: #6c757d;
    font-size: 0.875rem;
    font-weight: 500;
}

.pagination-list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 4px;
    align-items: center;
}

.pagination-item {
    display: flex;
}

.pagination-link {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
    padding: 8px 12px;
    color: #6c757d;
    text-decoration: none;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    gap: 6px;
}

.pagination-link:hover {
    color: #4e73df;
    background: #f8f9fc;
    border-color: #4e73df;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(78, 115, 223, 0.15);
}

.pagination-item.active .pagination-link {
    color: white;
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    border-color: #4e73df;
    box-shadow: 0 4px 12px rgba(78, 115, 223, 0.3);
    font-weight: 600;
}

.pagination-item.disabled .pagination-link {
    color: #adb5bd;
    background: #f8f9fa;
    border-color: #dee2e6;
    cursor: not-allowed;
    opacity: 0.6;
}

.pagination-item.disabled .pagination-link:hover {
    transform: none;
    box-shadow: none;
}

.pagination-dots {
    cursor: default !important;
}

.pagination-size-selector {
    display: flex;
    align-items: center;
}

.pagination-size-selector .form-select {
    min-width: 130px;
    border-radius: 8px;
    border-color: #dee2e6;
    font-size: 0.875rem;
    padding: 6px 12px;
}

.pagination-size-selector .form-select:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}
</style>
@endsection
