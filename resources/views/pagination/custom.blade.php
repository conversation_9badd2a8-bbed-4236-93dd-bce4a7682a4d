@if ($paginator->hasPages())
    <nav class="pagination-wrapper" aria-label="Pagination Navigation">
        <div class="pagination-info">
            <span class="pagination-text">
                Showing {{ $paginator->firstItem() }} to {{ $paginator->lastItem() }} of {{ $paginator->total() }} results
            </span>
        </div>
        
        <ul class="pagination-list">
            {{-- Previous Page Link --}}
            @if ($paginator->onFirstPage())
                <li class="pagination-item disabled">
                    <span class="pagination-link">
                        <i class="fas fa-chevron-left"></i>
                        <span class="pagination-text-mobile">Previous</span>
                    </span>
                </li>
            @else
                <li class="pagination-item">
                    <a class="pagination-link" href="{{ $paginator->previousPageUrl() }}" rel="prev">
                        <i class="fas fa-chevron-left"></i>
                        <span class="pagination-text-mobile">Previous</span>
                    </a>
                </li>
            @endif

            {{-- First Page --}}
            @if($paginator->currentPage() > 3)
                <li class="pagination-item">
                    <a class="pagination-link" href="{{ $paginator->url(1) }}">1</a>
                </li>
                @if($paginator->currentPage() > 4)
                    <li class="pagination-item disabled">
                        <span class="pagination-link pagination-dots">...</span>
                    </li>
                @endif
            @endif

            {{-- Pagination Elements --}}
            @foreach ($elements as $element)
                {{-- "Three Dots" Separator --}}
                @if (is_string($element))
                    <li class="pagination-item disabled">
                        <span class="pagination-link pagination-dots">{{ $element }}</span>
                    </li>
                @endif

                {{-- Array Of Links --}}
                @if (is_array($element))
                    @foreach ($element as $page => $url)
                        @if ($page == $paginator->currentPage())
                            <li class="pagination-item active">
                                <span class="pagination-link">{{ $page }}</span>
                            </li>
                        @else
                            <li class="pagination-item">
                                <a class="pagination-link" href="{{ $url }}">{{ $page }}</a>
                            </li>
                        @endif
                    @endforeach
                @endif
            @endforeach

            {{-- Last Page --}}
            @if($paginator->currentPage() < $paginator->lastPage() - 2)
                @if($paginator->currentPage() < $paginator->lastPage() - 3)
                    <li class="pagination-item disabled">
                        <span class="pagination-link pagination-dots">...</span>
                    </li>
                @endif
                <li class="pagination-item">
                    <a class="pagination-link" href="{{ $paginator->url($paginator->lastPage()) }}">{{ $paginator->lastPage() }}</a>
                </li>
            @endif

            {{-- Next Page Link --}}
            @if ($paginator->hasMorePages())
                <li class="pagination-item">
                    <a class="pagination-link" href="{{ $paginator->nextPageUrl() }}" rel="next">
                        <span class="pagination-text-mobile">Next</span>
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            @else
                <li class="pagination-item disabled">
                    <span class="pagination-link">
                        <span class="pagination-text-mobile">Next</span>
                        <i class="fas fa-chevron-right"></i>
                    </span>
                </li>
            @endif
        </ul>
        
        {{-- Page Size Selector --}}
        <div class="pagination-size-selector">
            <select class="form-select form-select-sm" onchange="changePageSize(this.value)">
                <option value="10" {{ request('per_page') == 10 ? 'selected' : '' }}>10 per page</option>
                <option value="25" {{ request('per_page') == 25 ? 'selected' : '' }}>25 per page</option>
                <option value="50" {{ request('per_page') == 50 ? 'selected' : '' }}>50 per page</option>
                <option value="100" {{ request('per_page') == 100 ? 'selected' : '' }}>100 per page</option>
            </select>
        </div>
    </nav>

    <style>
        .pagination-wrapper {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
            padding: 20px 0;
            margin-top: 20px;
            border-top: 1px solid #e3e6f0;
        }

        .pagination-info {
            color: #6c757d;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .pagination-list {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            gap: 4px;
            align-items: center;
        }

        .pagination-item {
            display: flex;
        }

        .pagination-link {
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 40px;
            height: 40px;
            padding: 8px 12px;
            color: #6c757d;
            text-decoration: none;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.2s ease;
            gap: 6px;
        }

        .pagination-link:hover {
            color: #4e73df;
            background: #f8f9fc;
            border-color: #4e73df;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(78, 115, 223, 0.15);
        }

        .pagination-item.active .pagination-link {
            color: white;
            background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
            border-color: #4e73df;
            box-shadow: 0 4px 12px rgba(78, 115, 223, 0.3);
            font-weight: 600;
        }

        .pagination-item.disabled .pagination-link {
            color: #adb5bd;
            background: #f8f9fa;
            border-color: #dee2e6;
            cursor: not-allowed;
            opacity: 0.6;
        }

        .pagination-item.disabled .pagination-link:hover {
            transform: none;
            box-shadow: none;
        }

        .pagination-dots {
            cursor: default !important;
        }

        .pagination-size-selector {
            display: flex;
            align-items: center;
        }

        .pagination-size-selector .form-select {
            min-width: 130px;
            border-radius: 8px;
            border-color: #dee2e6;
            font-size: 0.875rem;
            padding: 6px 12px;
        }

        .pagination-size-selector .form-select:focus {
            border-color: #4e73df;
            box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .pagination-wrapper {
                flex-direction: column;
                text-align: center;
                gap: 10px;
            }

            .pagination-list {
                flex-wrap: wrap;
                justify-content: center;
            }

            .pagination-link {
                min-width: 36px;
                height: 36px;
                padding: 6px 10px;
                font-size: 0.8rem;
            }

            .pagination-text-mobile {
                display: none;
            }

            .pagination-info {
                order: 3;
                font-size: 0.8rem;
            }

            .pagination-size-selector {
                order: 2;
            }
        }

        @media (max-width: 480px) {
            .pagination-link {
                min-width: 32px;
                height: 32px;
                padding: 4px 8px;
            }
        }
    </style>

    <script>
        function changePageSize(perPage) {
            const url = new URL(window.location);
            url.searchParams.set('per_page', perPage);
            url.searchParams.delete('page'); // Reset to first page
            window.location.href = url.toString();
        }
    </script>
@endif
