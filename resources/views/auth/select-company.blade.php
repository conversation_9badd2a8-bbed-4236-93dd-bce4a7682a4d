@extends('layouts.guest')

@section('title', 'Select Company')

@section('content')
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100">
                <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
            </div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Select Company
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                Choose a company to continue to your dashboard
            </p>
        </div>

        @if(session('info'))
            <div class="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded relative" role="alert">
                <span class="block sm:inline">{{ session('info') }}</span>
            </div>
        @endif

        @if($errors->any())
            <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
                @foreach($errors->all() as $error)
                    <span class="block sm:inline">{{ $error }}</span>
                @endforeach
            </div>
        @endif

        <form class="mt-8 space-y-6" action="{{ route('company.set') }}" method="POST">
            @csrf
            <div class="space-y-4">
                @foreach($companies as $company)
                    <label class="relative flex cursor-pointer rounded-lg border bg-white p-4 shadow-sm focus:outline-none hover:bg-gray-50 transition-colors duration-200">
                        <input type="radio" name="company_id" value="{{ $company->id }}" class="sr-only" required>
                        <span class="flex flex-1">
                            <span class="flex flex-col">
                                <span class="block text-sm font-medium text-gray-900 flex items-center">
                                    @if($company->logo)
                                        <img src="{{ asset('storage/' . $company->logo) }}" alt="{{ $company->name }}" class="h-8 w-8 rounded mr-3">
                                    @else
                                        <div class="h-8 w-8 rounded bg-blue-100 flex items-center justify-center mr-3">
                                            <span class="text-blue-600 font-semibold text-sm">{{ substr($company->name, 0, 2) }}</span>
                                        </div>
                                    @endif
                                    {{ $company->name }}
                                </span>
                                @if($company->address)
                                    <span class="mt-1 flex items-center text-sm text-gray-500 ml-11">
                                        {{ $company->address }}
                                    </span>
                                @endif
                            </span>
                        </span>
                        <span class="ml-6 flex items-center text-sm">
                            <span class="radio-indicator h-4 w-4 rounded-full border border-gray-300 bg-white"></span>
                        </span>
                    </label>
                @endforeach
            </div>

            <div>
                <button type="submit" class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                    <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                        <svg class="h-5 w-5 text-blue-500 group-hover:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                        </svg>
                    </span>
                    Continue to Dashboard
                </button>
            </div>

            <div class="text-center">
                <form action="{{ route('logout') }}" method="POST" class="inline">
                    @csrf
                    <button type="submit" class="text-sm text-gray-600 hover:text-gray-900 transition-colors duration-200">
                        Sign out instead
                    </button>
                </form>
            </div>
        </form>
    </div>
</div>

<style>
input[type="radio"]:checked + span .radio-indicator {
    background-color: #3B82F6;
    border-color: #3B82F6;
}

input[type="radio"]:checked + span .radio-indicator::after {
    content: '';
    display: block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: white;
    margin: 2px auto;
}

label:has(input[type="radio"]:checked) {
    border-color: #3B82F6;
    background-color: #EFF6FF;
}
</style>
@endsection
