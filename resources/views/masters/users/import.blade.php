@extends('layouts.app')

@section('title', 'Import Users - JMD Traders')
@section('page-title', 'Import Users')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Import Users with Roles & Permissions</h6>
                    <div class="dropdown no-arrow">
                        <a href="{{ route('masters.users.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to Users
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            {{ session('error') }}
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    @endif

                    @if(session('validation_errors'))
                        <div class="alert alert-danger">
                            <h6>Validation Errors:</h6>
                            <ul class="mb-0">
                                @foreach(session('validation_errors') as $row => $errors)
                                    <li><strong>{{ $row }}:</strong>
                                        <ul>
                                            @foreach($errors as $error)
                                                <li>{{ $error }}</li>
                                            @endforeach
                                        </ul>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <!-- Instructions -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> Import Instructions</h6>
                                <ul class="mb-0">
                                    <li>Download the template file to see the required format</li>
                                    <li>Fill in user data with roles and permissions</li>
                                    <li>Role Name should match existing roles in the system</li>
                                    <li>Permissions should be comma-separated permission slugs</li>
                                    <li>Company ID is optional (defaults to 1)</li>
                                    <li>Password must be at least 8 characters</li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                                </div>
                                <div class="card-body">
                                    <a href="{{ route('masters.users.import.template') }}" class="btn btn-success btn-block mb-2">
                                        <i class="fas fa-download"></i> Download Template
                                    </a>
                                    <button type="button" class="btn btn-info btn-block" data-toggle="modal" data-target="#rolesModal">
                                        <i class="fas fa-eye"></i> View Available Roles
                                    </button>
                                    <button type="button" class="btn btn-warning btn-block" data-toggle="modal" data-target="#permissionsModal">
                                        <i class="fas fa-eye"></i> View Available Permissions
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Import Form -->
                    <form id="importForm" action="{{ route('masters.users.import.process') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="import_file" class="form-label">Select CSV File</label>
                                    <input type="file" class="form-control @error('import_file') is-invalid @enderror" 
                                           id="import_file" name="import_file" accept=".csv,.txt" required>
                                    @error('import_file')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="default_company_id" class="form-label">Default Company</label>
                                    <select class="form-control" id="default_company_id" name="default_company_id">
                                        <option value="1">Default Company</option>
                                        <!-- Add more companies if needed -->
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="skip_duplicates" name="skip_duplicates" value="1">
                                    <label class="form-check-label" for="skip_duplicates">
                                        Skip duplicate emails
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="update_existing" name="update_existing" value="1">
                                    <label class="form-check-label" for="update_existing">
                                        Update existing users
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-12">
                                <button type="button" id="previewBtn" class="btn btn-info">
                                    <i class="fas fa-eye"></i> Preview Import
                                </button>
                                <button type="submit" id="importBtn" class="btn btn-primary" disabled>
                                    <i class="fas fa-upload"></i> Import Users
                                </button>
                            </div>
                        </div>
                    </form>

                    <!-- Preview Results -->
                    <div id="previewResults" class="mt-4" style="display: none;">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="m-0 font-weight-bold text-primary">Import Preview</h6>
                            </div>
                            <div class="card-body">
                                <div id="previewContent"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Roles Modal -->
<div class="modal fade" id="rolesModal" tabindex="-1" role="dialog" aria-labelledby="rolesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rolesModalLabel">Available Roles</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Role Name</th>
                                <th>Description</th>
                                <th>Level</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($roles as $role)
                            <tr>
                                <td><strong>{{ $role->name }}</strong></td>
                                <td>{{ $role->description ?? 'No description' }}</td>
                                <td>{{ $role->level }}</td>
                                <td>
                                    <span class="badge badge-{{ $role->status === 'active' ? 'success' : 'secondary' }}">
                                        {{ ucfirst($role->status) }}
                                    </span>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Permissions Modal -->
<div class="modal fade" id="permissionsModal" tabindex="-1" role="dialog" aria-labelledby="permissionsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="permissionsModalLabel">Available Permissions</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                @foreach($groupedPermissions as $module => $permissions)
                <div class="mb-4">
                    <h6 class="text-primary">{{ ucfirst($module) }} Module</h6>
                    <div class="row">
                        @foreach($permissions as $permission)
                        <div class="col-md-3 mb-2">
                            <span class="badge badge-info">{{ $permission->slug }}</span>
                        </div>
                        @endforeach
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script>
$(document).ready(function() {
    $('#previewBtn').click(function() {
        var formData = new FormData();
        var fileInput = $('#import_file')[0];
        
        if (fileInput.files.length === 0) {
            alert('Please select a file first.');
            return;
        }
        
        formData.append('import_file', fileInput.files[0]);
        formData.append('_token', $('meta[name="csrf-token"]').attr('content'));
        
        $.ajax({
            url: '{{ route("masters.users.import.preview") }}',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            beforeSend: function() {
                $('#previewBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Processing...');
            },
            success: function(response) {
                if (response.success) {
                    displayPreview(response);
                    $('#importBtn').prop('disabled', false);
                } else {
                    alert('Error: ' + response.message);
                }
            },
            error: function(xhr) {
                var errorMsg = 'An error occurred';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMsg = xhr.responseJSON.message;
                }
                alert('Error: ' + errorMsg);
            },
            complete: function() {
                $('#previewBtn').prop('disabled', false).html('<i class="fas fa-eye"></i> Preview Import');
            }
        });
    });
    
    function displayPreview(response) {
        var html = '<div class="alert alert-success">Found ' + response.total_rows + ' rows to import</div>';
        
        if (response.validation.errors && Object.keys(response.validation.errors).length > 0) {
            html += '<div class="alert alert-danger"><h6>Validation Errors:</h6><ul>';
            $.each(response.validation.errors, function(row, errors) {
                html += '<li><strong>' + row + ':</strong><ul>';
                $.each(errors, function(index, error) {
                    html += '<li>' + error + '</li>';
                });
                html += '</ul></li>';
            });
            html += '</ul></div>';
            $('#importBtn').prop('disabled', true);
        }
        
        if (response.validation.warnings && response.validation.warnings.length > 0) {
            html += '<div class="alert alert-warning"><h6>Warnings:</h6><ul>';
            $.each(response.validation.warnings, function(index, warning) {
                html += '<li>' + warning + '</li>';
            });
            html += '</ul></div>';
        }
        
        html += '<div class="alert alert-info">Valid rows: ' + response.validation.valid_count + '</div>';
        
        $('#previewContent').html(html);
        $('#previewResults').show();
    }
});
</script>
@endsection
