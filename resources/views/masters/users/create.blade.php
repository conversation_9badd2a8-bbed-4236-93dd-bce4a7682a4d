@extends('layouts.app')

@section('title', 'Create New User')

@push('styles')
<style>
    :root {
        --primary-color: #4e73df;
        --success-color: #1cc88a;
        --warning-color: #f6c23e;
        --danger-color: #e74a3b;
        --secondary-color: #858796;
        --light-color: #f8f9fc;
        --dark-color: #5a5c69;
    }

    .page-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, #224abe 100%);
        color: white;
        padding: 25px;
        border-radius: 12px;
        margin-bottom: 25px;
        box-shadow: 0 8px 25px rgba(78, 115, 223, 0.15);
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -30%;
        width: 150px;
        height: 150px;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
        border-radius: 50%;
    }

    .page-header h1 {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 8px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        position: relative;
        z-index: 2;
    }

    .page-header p {
        font-size: 1rem;
        margin-bottom: 0;
        opacity: 0.9;
        position: relative;
        z-index: 2;
    }

    .form-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        overflow: hidden;
    }

    .form-header {
        background: linear-gradient(135deg, var(--light-color) 0%, #e3e6f0 100%);
        padding: 20px 30px;
        border-bottom: 1px solid #e3e6f0;
    }

    .form-header h2 {
        font-size: 1.3rem;
        font-weight: 600;
        color: var(--dark-color);
        margin: 0;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .form-body {
        padding: 30px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 8px;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        display: block;
    }

    .form-control {
        border: 2px solid #e3e6f0;
        border-radius: 8px;
        padding: 12px 15px;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        width: 100%;
    }

    .form-control:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(78, 115, 223, 0.1);
    }

    .form-control.is-invalid {
        border-color: var(--danger-color);
        box-shadow: 0 0 0 3px rgba(231, 74, 59, 0.1);
    }

    .invalid-feedback {
        color: var(--danger-color);
        font-size: 0.8rem;
        margin-top: 5px;
        display: block;
    }

    .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }

    .password-toggle {
        position: relative;
    }

    .password-toggle .toggle-btn {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: var(--secondary-color);
        cursor: pointer;
        font-size: 0.9rem;
    }

    .password-toggle .toggle-btn:hover {
        color: var(--primary-color);
    }

    .btn-group {
        display: flex;
        gap: 10px;
        margin-top: 30px;
        justify-content: flex-end;
        flex-wrap: wrap;
    }

    .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;
        cursor: pointer;
        font-size: 0.9rem;
    }

    .btn-primary {
        background: linear-gradient(135deg, var(--primary-color) 0%, #224abe 100%);
        color: white;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(78, 115, 223, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-cancel {
        background: var(--secondary-color);
        color: white;
    }

    .btn-cancel:hover {
        background: #6c7583;
        color: white;
        text-decoration: none;
    }

    .breadcrumb-container {
        margin-bottom: 20px;
    }

    .breadcrumb {
        background: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }

    .breadcrumb-item a {
        color: var(--primary-color);
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        text-decoration: underline;
    }

    .breadcrumb-item.active {
        color: var(--secondary-color);
    }

    @media (max-width: 768px) {
        .form-grid {
            grid-template-columns: 1fr;
        }
        
        .btn-group {
            flex-direction: column;
            align-items: stretch;
        }
        
        .page-header {
            text-align: center;
        }
    }

    /* Company Selection Styles */
    .company-selection {
        border: 1px solid #e3e6f0;
        border-radius: 8px;
        padding: 15px;
        background-color: #f8f9fc;
    }

    .company-item {
        padding: 10px;
        border-bottom: 1px solid #e3e6f0;
        margin-bottom: 10px;
    }

    .company-item:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .company-role {
        margin-top: 8px;
        margin-left: 25px;
    }

    .form-check-label {
        font-weight: 500;
        color: var(--dark-color);
    }

    .form-check-input:checked {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    #roleDescription {
        margin-top: 5px;
        padding: 8px;
        background-color: #f8f9fc;
        border-radius: 4px;
        border-left: 3px solid var(--primary-color);
    }
</style>
@endpush

@section('content')
<div class="container-fluid px-4">
    <!-- Breadcrumb -->
    <div class="breadcrumb-container">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="{{ route('masters.users.index') }}">Users</a></li>
                <li class="breadcrumb-item active">Create User</li>
            </ol>
        </nav>
    </div>

    <!-- Page Header -->
    <div class="page-header">
        <h1><i class="fas fa-user-plus"></i> Create New User</h1>
        <p>Add a new user to the system with appropriate role and permissions</p>
    </div>

    <!-- Form Container -->
    <div class="form-container">
        <div class="form-header">
            <h2><i class="fas fa-user-circle"></i> User Information</h2>
        </div>
        
        <div class="form-body">
            <form method="POST" action="{{ route('masters.users.store') }}" id="userForm">
                @csrf
                
                <div class="form-grid">
                    <!-- Name -->
                    <div class="form-group">
                        <label for="name" class="form-label">Full Name *</label>
                        <input type="text" class="form-control @error('name') is-invalid @enderror" 
                               id="name" name="name" value="{{ old('name') }}" required>
                        @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Email -->
                    <div class="form-group">
                        <label for="email" class="form-label">Email Address *</label>
                        <input type="email" class="form-control @error('email') is-invalid @enderror" 
                               id="email" name="email" value="{{ old('email') }}" required>
                        @error('email')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Phone -->
                    <div class="form-group">
                        <label for="phone" class="form-label">Phone Number</label>
                        <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                               id="phone" name="phone" value="{{ old('phone') }}">
                        @error('phone')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Note about company-specific roles -->
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>Company-Specific Roles:</strong> User roles are now assigned per company. You can assign different roles for each company below.
                    </div>

                    <!-- Companies Assignment -->
                    <div class="form-group">
                        <label class="form-label">Company Access *</label>
                        <div class="company-selection">
                            @foreach($companies as $company)
                            <div class="company-item">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="companies[]"
                                           value="{{ $company->id }}" id="company_{{ $company->id }}"
                                           {{ in_array($company->id, old('companies', [])) ? 'checked' : '' }}
                                           onchange="toggleCompanyRole({{ $company->id }})">
                                    <label class="form-check-label" for="company_{{ $company->id }}">
                                        <strong>{{ $company->name }}</strong>
                                        @if($company->description)
                                            <br><small class="text-muted">{{ $company->description }}</small>
                                        @endif
                                    </label>
                                </div>
                                <div class="company-role" id="role_for_company_{{ $company->id }}" style="display: none;">
                                    <select class="form-control form-control-sm" name="company_roles[]" required>
                                        <option value="">Select Role for this Company</option>
                                        @foreach($roles as $role)
                                            <option value="{{ $role->id }}">{{ $role->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            @endforeach
                        </div>
                        @error('companies')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                        <small class="form-text text-muted">Select at least one company. User will have access to selected companies.</small>
                    </div>

                    <!-- Status -->
                    <div class="form-group">
                        <label for="status" class="form-label">Status *</label>
                        <select class="form-control @error('status') is-invalid @enderror" id="status" name="status" required>
                            <option value="active" {{ old('status', 'active') == 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                        </select>
                        @error('status')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="form-grid">
                    <!-- Password -->
                    <div class="form-group">
                        <label for="password" class="form-label">Password *</label>
                        <div class="password-toggle">
                            <input type="password" class="form-control @error('password') is-invalid @enderror" 
                                   id="password" name="password" required minlength="8">
                            <button type="button" class="toggle-btn" onclick="togglePassword('password')">
                                <i class="fas fa-eye" id="password-icon"></i>
                            </button>
                        </div>
                        @error('password')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Confirm Password -->
                    <div class="form-group">
                        <label for="password_confirmation" class="form-label">Confirm Password *</label>
                        <div class="password-toggle">
                            <input type="password" class="form-control" 
                                   id="password_confirmation" name="password_confirmation" required minlength="8">
                            <button type="button" class="toggle-btn" onclick="togglePassword('password_confirmation')">
                                <i class="fas fa-eye" id="password_confirmation-icon"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Buttons -->
                <div class="btn-group">
                    <a href="{{ route('masters.users.index') }}" class="btn btn-cancel">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Create User
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Company Management Functions

function toggleCompanyRole(companyId) {
    const checkbox = document.getElementById(`company_${companyId}`);
    const roleDiv = document.getElementById(`role_for_company_${companyId}`);

    if (checkbox.checked) {
        roleDiv.style.display = 'block';
    } else {
        roleDiv.style.display = 'none';
        // Reset the role selection
        const roleSelect = roleDiv.querySelector('select');
        if (roleSelect) {
            roleSelect.value = '';
        }
    }
}

function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '-icon');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.getElementById('userForm');
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('password_confirmation');

    function validatePasswords() {
        if (password.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity('Passwords do not match');
        } else {
            confirmPassword.setCustomValidity('');
        }
    }

    password.addEventListener('input', validatePasswords);
    confirmPassword.addEventListener('input', validatePasswords);

    form.addEventListener('submit', function(e) {
        validatePasswords();

        // Validate company selection
        const checkedCompanies = document.querySelectorAll('input[name="companies[]"]:checked');

        if (checkedCompanies.length === 0) {
            e.preventDefault();
            alert('Please select at least one company for the user.');
            return false;
        }

        // Validate that each selected company has a role
        let hasError = false;
        checkedCompanies.forEach(function(checkbox) {
            const companyId = checkbox.value;
            const roleSelect = document.querySelector('#role_for_company_' + companyId + ' select');
            if (!roleSelect.value) {
                hasError = true;
            }
        });

        if (hasError) {
            e.preventDefault();
            alert('Please select a role for each selected company.');
            return false;
        }

        if (!form.checkValidity()) {
            e.preventDefault();
            e.stopPropagation();
        }
    });
});
</script>
@endpush
