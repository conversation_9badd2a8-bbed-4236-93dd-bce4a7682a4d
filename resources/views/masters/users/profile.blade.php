@extends('layouts.app')

@section('title', 'User Profile')

@push('styles')
<style>
    :root {
        --primary-color: #4e73df;
        --success-color: #1cc88a;
        --warning-color: #f6c23e;
        --danger-color: #e74a3b;
        --secondary-color: #858796;
        --light-color: #f8f9fc;
        --dark-color: #5a5c69;
    }

    .profile-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, #224abe 100%);
        color: white;
        padding: 30px;
        border-radius: 12px;
        margin-bottom: 25px;
        box-shadow: 0 8px 25px rgba(78, 115, 223, 0.15);
        position: relative;
        overflow: hidden;
    }

    .profile-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200px;
        height: 200px;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
        border-radius: 50%;
    }

    .profile-avatar {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 3rem;
        font-weight: 700;
        color: white;
        margin: 0 auto 20px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        position: relative;
        z-index: 2;
    }

    .profile-info {
        text-align: center;
        position: relative;
        z-index: 2;
    }

    .profile-name {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 10px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .profile-email {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 15px;
    }

    .profile-badges {
        display: flex;
        justify-content: center;
        gap: 15px;
        flex-wrap: wrap;
    }

    .profile-badge {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;
        backdrop-filter: blur(10px);
    }

    .content-section {
        background: white;
        border-radius: 12px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        overflow: hidden;
        margin-bottom: 20px;
    }

    .section-header {
        background: linear-gradient(135deg, #f8f9fc 0%, #e3e6f0 100%);
        padding: 20px;
        border-bottom: 1px solid #e3e6f0;
    }

    .section-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: var(--dark-color);
        margin: 0;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .section-content {
        padding: 25px;
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }

    .info-item {
        padding: 15px;
        background: #f8f9fc;
        border-radius: 8px;
        border-left: 4px solid var(--primary-color);
    }

    .info-label {
        font-size: 0.85rem;
        font-weight: 600;
        color: var(--secondary-color);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 5px;
    }

    .info-value {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--dark-color);
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        font-size: 0.9rem;
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 8px;
        display: block;
        text-transform: uppercase;
        letter-spacing: 0.3px;
    }

    .form-control {
        border: 2px solid #e3e6f0;
        border-radius: 8px;
        padding: 12px 16px;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        background: white;
    }

    .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(78, 115, 223, 0.1);
        outline: none;
    }

    .btn-update {
        background: linear-gradient(135deg, var(--primary-color), #224abe);
        color: white;
        border: none;
        padding: 12px 30px;
        border-radius: 8px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 10px;
        font-size: 0.95rem;
    }

    .btn-update:hover {
        background: linear-gradient(135deg, #224abe, #1e3a8a);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(78, 115, 223, 0.4);
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
        border: none;
        padding: 12px 30px;
        border-radius: 8px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 10px;
        font-size: 0.95rem;
    }

    .btn-secondary:hover {
        background: #5a6268;
        color: white;
        transform: translateY(-2px);
    }

    .activity-timeline {
        position: relative;
        padding-left: 30px;
    }

    .activity-timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #e3e6f0;
    }

    .activity-item {
        position: relative;
        margin-bottom: 20px;
    }

    .activity-item::before {
        content: '';
        position: absolute;
        left: -23px;
        top: 5px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: var(--primary-color);
        border: 3px solid white;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .activity-content {
        background: #f8f9fc;
        padding: 15px;
        border-radius: 8px;
        border-left: 3px solid var(--primary-color);
    }

    .activity-title {
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 5px;
    }

    .activity-time {
        font-size: 0.85rem;
        color: var(--secondary-color);
    }

    .stats-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 25px;
    }

    .stats-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        border-left: 4px solid var(--primary-color);
        transition: all 0.3s ease;
    }

    .stats-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    .stats-card.success { border-left-color: var(--success-color); }
    .stats-card.warning { border-left-color: var(--warning-color); }
    .stats-card.info { border-left-color: #17a2b8; }

    .stats-number {
        font-size: 2rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 5px;
        line-height: 1;
    }

    .stats-card.success .stats-number { color: var(--success-color); }
    .stats-card.warning .stats-number { color: var(--warning-color); }
    .stats-card.info .stats-number { color: #17a2b8; }

    .stats-label {
        color: var(--secondary-color);
        font-weight: 600;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .alert-info {
        background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
        border: 1px solid #bee5eb;
        color: #0c5460;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }

    @media (max-width: 768px) {
        .profile-avatar {
            width: 80px;
            height: 80px;
            font-size: 2rem;
        }

        .profile-name {
            font-size: 1.5rem;
        }

        .info-grid {
            grid-template-columns: 1fr;
        }

        .stats-cards {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 576px) {
        .profile-header {
            padding: 20px;
        }

        .stats-cards {
            grid-template-columns: 1fr;
        }

        .profile-badges {
            justify-content: center;
        }
    }
</style>
@endpush

@section('content')
<div class="container-fluid px-4">
    <!-- Profile Header -->
    <div class="profile-header">
        <div class="profile-avatar">
            {{ strtoupper(substr($user->name, 0, 2)) }}
        </div>
        <div class="profile-info">
            <h1 class="profile-name">{{ $user->name }}</h1>
            <p class="profile-email">{{ $user->email }}</p>
            <div class="profile-badges">
                <div class="profile-badge">
                    @if($user->role == 'admin')
                        <i class="fas fa-crown"></i>
                    @elseif($user->role == 'manager')
                        <i class="fas fa-user-tie"></i>
                    @else
                        <i class="fas fa-user"></i>
                    @endif
                    {{ ucfirst($user->role) }}
                </div>
                <div class="profile-badge">
                    @if($user->status == 'active')
                        <i class="fas fa-check-circle"></i>
                    @else
                        <i class="fas fa-times-circle"></i>
                    @endif
                    {{ ucfirst($user->status) }}
                </div>
                <div class="profile-badge">
                    <i class="fas fa-calendar"></i>
                    Member since {{ $user->created_at->format('M Y') }}
                </div>
            </div>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i> {{ session('success') }}
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i>
            <ul class="mb-0">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <!-- Statistics Cards -->
    <div class="stats-cards">
        <div class="stats-card success">
            <div class="stats-number">{{ $user->salesEntries()->count() ?? 0 }}</div>
            <div class="stats-label">Sales Entries</div>
        </div>
        <div class="stats-card warning">
            <div class="stats-number">{{ $user->transportEntries()->count() ?? 0 }}</div>
            <div class="stats-label">Transport Entries</div>
        </div>
        <div class="stats-card info">
            <div class="stats-number">{{ $user->created_at->diffInDays(now()) }}</div>
            <div class="stats-label">Days Active</div>
        </div>
        <div class="stats-card">
            <div class="stats-number">{{ $user->updated_at->diffForHumans() }}</div>
            <div class="stats-label">Last Updated</div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Profile Information -->
            <div class="content-section">
                <div class="section-header">
                    <h3 class="section-title">
                        <i class="fas fa-user-edit"></i>
                        Update Profile Information
                    </h3>
                </div>
                <div class="section-content">
                    @if($user->id == auth()->id())
                    <div class="alert-info">
                        <i class="fas fa-info-circle"></i>
                        You are editing your own profile. Changes will be applied immediately.
                    </div>
                    @endif

                    <form method="POST" action="{{ route('users.profile.update') }}">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Full Name</label>
                                    <input type="text" 
                                           name="name" 
                                           class="form-control" 
                                           value="{{ old('name', $user->name) }}" 
                                           required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Email Address</label>
                                    <input type="email" 
                                           name="email" 
                                           class="form-control" 
                                           value="{{ old('email', $user->email) }}" 
                                           required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Phone Number</label>
                                    <input type="text" 
                                           name="phone" 
                                           class="form-control" 
                                           value="{{ old('phone', $user->phone) }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Current Password (required for changes)</label>
                                    <input type="password" 
                                           name="current_password" 
                                           class="form-control" 
                                           placeholder="Enter current password">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">New Password (optional)</label>
                                    <input type="password" 
                                           name="password" 
                                           class="form-control" 
                                           placeholder="Leave blank to keep current">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Confirm New Password</label>
                                    <input type="password" 
                                           name="password_confirmation" 
                                           class="form-control" 
                                           placeholder="Confirm new password">
                                </div>
                            </div>
                        </div>

                        <div class="d-flex gap-3">
                            <button type="submit" class="btn-update">
                                <i class="fas fa-save"></i>
                                Update Profile
                            </button>
                            <a href="{{ route('users.index') }}" class="btn-secondary">
                                <i class="fas fa-arrow-left"></i>
                                Back to Users
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Account Details -->
            <div class="content-section">
                <div class="section-header">
                    <h3 class="section-title">
                        <i class="fas fa-info-circle"></i>
                        Account Details
                    </h3>
                </div>
                <div class="section-content">
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">User ID</div>
                            <div class="info-value">#{{ str_pad($user->id, 6, '0', STR_PAD_LEFT) }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Account Status</div>
                            <div class="info-value">
                                <span class="badge badge-{{ $user->status == 'active' ? 'success' : 'secondary' }}">
                                    {{ ucfirst($user->status) }}
                                </span>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">User Role</div>
                            <div class="info-value">
                                <span class="badge badge-{{ $user->role_badge }}">
                                    {{ ucfirst($user->role) }}
                                </span>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Created Date</div>
                            <div class="info-value">{{ $user->created_at->format('M d, Y') }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Last Updated</div>
                            <div class="info-value">{{ $user->updated_at->format('M d, Y') }}</div>
                        </div>
                        @if($user->phone)
                        <div class="info-item">
                            <div class="info-label">Phone</div>
                            <div class="info-value">{{ $user->phone }}</div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="content-section">
                <div class="section-header">
                    <h3 class="section-title">
                        <i class="fas fa-clock"></i>
                        Recent Activity
                    </h3>
                </div>
                <div class="section-content">
                    <div class="activity-timeline">
                        <div class="activity-item">
                            <div class="activity-content">
                                <div class="activity-title">Profile Created</div>
                                <div class="activity-time">{{ $user->created_at->format('M d, Y h:i A') }}</div>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-content">
                                <div class="activity-title">Last Profile Update</div>
                                <div class="activity-time">{{ $user->updated_at->format('M d, Y h:i A') }}</div>
                            </div>
                        </div>
                        @if($user->salesEntries()->count() > 0)
                        <div class="activity-item">
                            <div class="activity-content">
                                <div class="activity-title">Latest Sales Entry</div>
                                <div class="activity-time">{{ $user->salesEntries()->latest()->first()->created_at->format('M d, Y') }}</div>
                            </div>
                        </div>
                        @endif
                        @if($user->transportEntries()->count() > 0)
                        <div class="activity-item">
                            <div class="activity-content">
                                <div class="activity-title">Latest Transport Entry</div>
                                <div class="activity-time">{{ $user->transportEntries()->latest()->first()->created_at->format('M d, Y') }}</div>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
