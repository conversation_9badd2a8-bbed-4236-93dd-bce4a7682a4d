@extends('layouts.app')

@section('title', 'User Details')

@push('styles')
<style>
    .page-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, #224abe 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        margin-bottom: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .page-header h1 {
        font-size: 1.            <a href="{{ route('masters.users.index') }}" class="btn btn-back">
                <i class="fas fa-arrow-left"></i> Back to Users
            </a>
            <a href="{{ route('masters.users.edit', $user) }}" class="btn btn-edit">;
        margin-bottom: 5px;
    }

    .page-header p {
        font-size: 0.85rem;
        margin-bottom: 0;
    }

    .details-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .details-header {
        background: var(--primary-color);
        color: white;
        padding: 12px 20px;
        font-weight: 600;
        font-size: 0.95rem;
    }

    .details-body {
        padding: 20px;
    }

    .detail-row {
        display: flex;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid #f1f3f4;
    }

    .detail-row:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .detail-label {
        font-weight: 600;
        color: var(--dark-color);
        width: 150px;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.3px;
    }

    .detail-value {
        flex: 1;
        color: var(--secondary-color);
        font-size: 0.9rem;
    }

    .status-badge {
        padding: 3px 10px;
        border-radius: 12px;
        font-weight: 600;
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.3px;
    }

    .status-active {
        background: rgba(28, 200, 138, 0.1);
        color: var(--success-color);
    }

    .status-inactive {
        background: rgba(133, 135, 150, 0.1);
        color: var(--secondary-color);
    }

    .role-badge {
        padding: 3px 10px;
        border-radius: 10px;
        font-weight: 600;
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.3px;
    }

    .role-admin {
        background: rgba(231, 74, 59, 0.1);
        color: var(--danger-color);
    }

    .role-manager {
        background: rgba(246, 194, 62, 0.1);
        color: var(--warning-color);
    }

    .role-user {
        background: rgba(78, 115, 223, 0.1);
        color: var(--primary-color);
    }

    .action-buttons {
        display: flex;
        gap: 10px;
        margin-top: 20px;
        padding-top: 15px;
        border-top: 1px solid #e3e6f0;
    }

    .btn-edit {
        background: var(--warning-color);
        border: none;
        color: white;
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        transition: all 0.3s ease;
        font-size: 0.85rem;
        text-decoration: none;
        display: inline-block;
    }

    .btn-edit:hover {
        background: #dda20a;
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(246, 194, 62, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-back {
        background: var(--secondary-color);
        border: none;
        color: white;
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        transition: all 0.3s ease;
        font-size: 0.85rem;
        text-decoration: none;
        display: inline-block;
    }

    .btn-back:hover {
        background: #6c757d;
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(133, 135, 150, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-delete {
        background: var(--danger-color);
        border: none;
        color: white;
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        transition: all 0.3s ease;
        font-size: 0.85rem;
    }

    .btn-delete:hover {
        background: #c82333;
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(231, 74, 59, 0.4);
        color: white;
    }

    .breadcrumb-container {
        background: white;
        border-radius: 6px;
        padding: 10px 15px;
        margin-bottom: 15px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .breadcrumb {
        margin: 0;
        background: none;
        padding: 0;
        font-size: 0.85rem;
    }

    .breadcrumb-item a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
    }

    .breadcrumb-item a:hover {
        text-decoration: underline;
    }

    .breadcrumb-item.active {
        color: var(--secondary-color);
    }

    .user-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: var(--primary-color);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 2rem;
        margin-bottom: 15px;
    }

    .user-info {
        background: rgba(78, 115, 223, 0.05);
        padding: 15px;
        border-radius: 8px;
        border-left: 4px solid var(--primary-color);
        text-align: center;
    }

    .permissions-info {
        background: rgba(246, 194, 62, 0.05);
        padding: 10px;
        border-radius: 6px;
        border-left: 3px solid var(--warning-color);
        margin-top: 10px;
    }

    .permission-item {
        display: flex;
        align-items: center;
        margin-bottom: 5px;
        font-size: 0.85rem;
    }

    .permission-item:last-child {
        margin-bottom: 0;
    }

    .permission-icon {
        width: 16px;
        color: var(--success-color);
        margin-right: 8px;
    }
</style>
@endpush

@section('content')
<!-- Breadcrumb -->
<div class="breadcrumb-container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="#">Masters</a></li>
            <li class="breadcrumb-item"><a href="{{ route('masters.users.index') }}">Users</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ $user->name }}</li>
        </ol>
    </nav>
</div>

<!-- Page Header -->
<div class="page-header">
    <h1><i class="fas fa-users"></i> User Details</h1>
    <p>View complete information for {{ $user->name }}</p>
</div>

<!-- User Details -->
<div class="details-container">
    <div class="details-header">
        <i class="fas fa-info-circle"></i> User Information
    </div>
    
    <div class="details-body">
        <div class="row">
            <div class="col-md-8">
                <div class="detail-row">
                    <div class="detail-label">Full Name:</div>
                    <div class="detail-value"><strong>{{ $user->name }}</strong></div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">Email:</div>
                    <div class="detail-value">{{ $user->email ?? 'N/A' }}</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">Phone:</div>
                    <div class="detail-value">{{ $user->phone ?? 'N/A' }}</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">User Role:</div>
                    <div class="detail-value">
                        <span class="role-badge role-{{ $user->role }}">
                            {{ ucfirst($user->role) }}
                        </span>
                    </div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">Status:</div>
                    <div class="detail-value">
                        <span class="status-badge status-{{ $user->status }}">
                            {{ ucfirst($user->status) }}
                        </span>
                    </div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">Account Created:</div>
                    <div class="detail-value">{{ $user->created_at->format('d M Y, h:i A') }}</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">Last Updated:</div>
                    <div class="detail-value">{{ $user->updated_at->format('d M Y, h:i A') }}</div>
                </div>

                @if($user->email_verified_at)
                <div class="detail-row">
                    <div class="detail-label">Email Verified:</div>
                    <div class="detail-value">{{ $user->email_verified_at->format('d M Y, h:i A') }}</div>
                </div>
                @endif
            </div>

            <div class="col-md-4">
                <div class="user-info">
                    <div class="user-avatar">
                        {{ strtoupper(substr($user->name, 0, 1)) }}
                    </div>
                    <h5 class="mb-2">{{ $user->name }}</h5>
                    <p class="text-muted mb-0">{{ ucfirst($user->role) }} User</p>
                    
                    @if($user->role !== 'user')
                    <div class="permissions-info">
                        <h6 class="mb-2"><i class="fas fa-shield-alt"></i> Permissions</h6>
                        
                        @if($user->role === 'admin')
                        <div class="permission-item">
                            <i class="fas fa-check permission-icon"></i>
                            <span>Full System Access</span>
                        </div>
                        <div class="permission-item">
                            <i class="fas fa-check permission-icon"></i>
                            <span>User Management</span>
                        </div>
                        <div class="permission-item">
                            <i class="fas fa-check permission-icon"></i>
                            <span>Master Data Management</span>
                        </div>
                        <div class="permission-item">
                            <i class="fas fa-check permission-icon"></i>
                            <span>Reports & Analytics</span>
                        </div>
                        @elseif($user->role === 'manager')
                        <div class="permission-item">
                            <i class="fas fa-check permission-icon"></i>
                            <span>Sales Management</span>
                        </div>
                        <div class="permission-item">
                            <i class="fas fa-check permission-icon"></i>
                            <span>Customer Management</span>
                        </div>
                        <div class="permission-item">
                            <i class="fas fa-check permission-icon"></i>
                            <span>Reports Access</span>
                        </div>
                        @endif
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="action-buttons">
            <a href="{{ route('users.index') }}" class="btn btn-back">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
            <a href="{{ route('users.edit', $user) }}" class="btn btn-edit">
                <i class="fas fa-edit"></i> Edit User
            </a>
            @if($user->id !== auth()->id())
            <form action="{{ route('masters.users.destroy', $user) }}" method="POST" style="display: inline;" 
                  onsubmit="return confirm('Are you sure you want to delete this user?')">
                @csrf
                @method('DELETE')
                <button type="submit" class="btn btn-delete">
                    <i class="fas fa-trash"></i> Delete User
                </button>
            </form>
            @endif
        </div>
    </div>
</div>
@endsection
