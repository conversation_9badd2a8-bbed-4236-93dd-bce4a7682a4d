@extends('layouts.app')

@section('title', 'User Management')

@push('styles')
<style>
    :root {
        --primary-color: #4e73df;
        --success-color: #1cc88a;
        --warning-color: #f6c23e;
        --danger-color: #e74a3b;
        --secondary-color: #858796;
        --light-color: #f8f9fc;
        --dark-color: #5a5c69;
    }

    .page-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, #224abe 100%);
        color: white;
        padding: 20px;
        border-radius: 12px;
        margin-bottom: 20px;
        box-shadow: 0 8px 25px rgba(78, 115, 223, 0.15);
    }

    .page-header h1 {
        font-size: 1.75rem;
        font-weight: 700;
        margin-bottom: 8px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .page-header p {
        font-size: 0.9rem;
        margin-bottom: 0;
        opacity: 0.9;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 25px;
    }

    .stats-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        border-left: 4px solid var(--primary-color);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, rgba(78, 115, 223, 0.1), rgba(78, 115, 223, 0.05));
        border-radius: 0 0 0 60px;
    }

    .stats-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    .stats-card.success { border-left-color: var(--success-color); }
    .stats-card.warning { border-left-color: var(--warning-color); }
    .stats-card.danger { border-left-color: var(--danger-color); }

    .stats-number {
        font-size: 2rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 5px;
        line-height: 1;
    }

    .stats-card.success .stats-number { color: var(--success-color); }
    .stats-card.warning .stats-number { color: var(--warning-color); }
    .stats-card.danger .stats-number { color: var(--danger-color); }
        letter-spacing: 0.5px;
        font-size: 0.7rem;
    }

    .action-buttons {
        display: flex;
        gap: 8px;
        margin-bottom: 10px;
        align-items: center;
    }

    .btn-create {
        background: var(--success-color);
        border: none;
        color: white;
        padding: 6px 12px;
        border-radius: 6px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        transition: all 0.3s ease;
        font-size: 0.8rem;
    }

    .btn-create:hover {
        background: #17a673;
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(28, 200, 138, 0.4);
        color: white;
    }

    .search-box {
        background: white;
        border: 1px solid #e3e6f0;
        border-radius: 6px;
        padding: 6px 12px;
        font-size: 0.85rem;
        transition: border-color 0.3s ease;
        width: 200px;
    }

    .search-box:focus {
        border-color: var(--primary-color);
        outline: none;
        box-shadow: 0 0 0 0.15rem rgba(78, 115, 223, 0.2);
    }

    .table-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .table-header {
        background: var(--primary-color);
        color: white;
        padding: 10px 15px;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .custom-table {
        margin: 0;
    }

    .custom-table thead th {
        background: var(--light-color);
        color: var(--dark-color);
        font-weight: 600;
        border: none;
        padding: 8px 12px;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        font-size: 0.75rem;
    }

    .custom-table tbody td {
        padding: 8px 12px;
        vertical-align: middle;
        border-color: #e3e6f0;
        font-size: 0.85rem;
    }

    .custom-table tbody tr:hover {
        background: rgba(78, 115, 223, 0.03);
    }

    .status-badge {
        padding: 2px 8px;
        border-radius: 12px;
        font-weight: 600;
        font-size: 0.7rem;
        text-transform: uppercase;
        letter-spacing: 0.3px;
    }

    .status-active {
        background: rgba(28, 200, 138, 0.1);
        color: var(--success-color);
    }

    .status-inactive {
        background: rgba(133, 135, 150, 0.1);
        color: var(--secondary-color);
    }

    .role-badge {
        padding: 2px 8px;
        border-radius: 10px;
        font-weight: 600;
        font-size: 0.7rem;
        text-transform: uppercase;
        letter-spacing: 0.3px;
    }

    .role-admin {
        background: rgba(231, 74, 59, 0.1);
        color: var(--danger-color);
    }

    .role-manager {
        background: rgba(246, 194, 62, 0.1);
        color: var(--warning-color);
    }

    .role-user {
        background: rgba(78, 115, 223, 0.1);
        color: var(--primary-color);
    }

    .action-btn {
        padding: 4px 8px;
        border-radius: 4px;
        border: none;
        font-weight: 500;
        font-size: 0.75rem;
        transition: all 0.3s ease;
        margin: 0 1px;
    }

    .btn-view {
        background: var(--info-color);
        color: white;
    }

    .btn-view:hover {
        background: #138496;
        color: white;
    }

    .btn-edit {
        background: var(--warning-color);
        color: white;
    }

    .btn-edit:hover {
        background: #dda20a;
        color: white;
    }

    .btn-delete {
        background: var(--danger-color);
        color: white;
    }

    .btn-delete:hover {
        background: #c82333;
        color: white;
    }

    .pagination-container {
        padding: 15px;
        background: white;
        border-top: 1px solid #e3e6f0;
    }

    .alert-custom {
        border-radius: 8px;
        border: none;
        padding: 10px 15px;
        margin-bottom: 15px;
        font-weight: 500;
        font-size: 0.9rem;
    }

    .breadcrumb-container {
        background: white;
        border-radius: 6px;
        padding: 10px 15px;
        margin-bottom: 15px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .breadcrumb {
        margin: 0;
        background: none;
        padding: 0;
        font-size: 0.85rem;
    }

    .breadcrumb-item a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
    }

    .breadcrumb-item a:hover {
        text-decoration: underline;
    }

    .breadcrumb-item.active {
        color: var(--secondary-color);
    }

    .user-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: var(--primary-color);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 0.8rem;
        margin-right: 8px;
    }

    .companies-list {
        max-width: 250px;
    }

    .company-item {
        display: flex;
        align-items: center;
        gap: 5px;
        flex-wrap: wrap;
        font-size: 0.85rem;
    }

    .company-item strong {
        color: var(--dark-color);
    }

    .badge {
        font-size: 0.7rem;
        padding: 2px 6px;
        border-radius: 10px;
    }

    .badge-primary {
        background-color: var(--primary-color);
        color: white;
    }

    .badge-success {
        background-color: var(--success-color);
        color: white;
    }

    .badge-secondary {
        background-color: var(--secondary-color);
        color: white;
    }
</style>
@endpush

@section('content')
<!-- Breadcrumb -->
<div class="breadcrumb-container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="#">Masters</a></li>
            <li class="breadcrumb-item active" aria-current="page">Users</li>
        </ol>
    </nav>
</div>

<!-- Page Header -->
<div class="page-header">
    <h1><i class="fas fa-users"></i> User Master</h1>
    <p>Manage system users with roles and permissions</p>
</div>

<!-- Success/Error Messages -->
@if(session('success'))
    <div class="alert alert-success alert-custom">
        <i class="fas fa-check-circle"></i> {{ session('success') }}
    </div>
@endif

@if(session('error'))
    <div class="alert alert-danger alert-custom">
        <i class="fas fa-exclamation-circle"></i> {{ session('error') }}
    </div>
@endif

<!-- Statistics Cards -->
<div class="row mb-3">
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number">{{ $users->total() }}</div>
            <div class="stats-label">Total Users</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number">{{ $users->where('status', 'active')->count() }}</div>
            <div class="stats-label">Active Users</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number">{{ $stats['with_companies'] }}</div>
            <div class="stats-label">With Company Access</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number">{{ $stats['without_companies'] }}</div>
            <div class="stats-label">Without Company Access</div>
        </div>
    </div>
</div>

<!-- Action Buttons -->
<div class="action-buttons">
    <a href="{{ route('masters.users.create') }}" class="btn btn-create">
        <i class="fas fa-plus"></i> Add New User
    </a>
    
    <input type="text" class="search-box" placeholder="Search users..." id="searchInput">
</div>

<!-- Users Table -->
<div class="table-container">
    <div class="table-header">
        <i class="fas fa-list"></i> Users List
    </div>
    
    <div class="table-responsive">
        <table class="table custom-table">
            <thead>
                <tr>
                    <th>User</th>
                    <th>Email</th>
                    <th>Phone</th>
                    <th>Companies & Roles</th>
                    <th>Status</th>
                    <th>Last Login</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                @forelse($users as $user)
                <tr>
                    <td>
                        <div class="d-flex align-items-center">
                            <div class="user-avatar">
                                {{ strtoupper(substr($user->name, 0, 1)) }}
                            </div>
                            <div>
                                <strong>{{ $user->name }}</strong>
                                <br><small class="text-muted">ID: {{ $user->id }}</small>
                            </div>
                        </div>
                    </td>
                    <td>{{ $user->email }}</td>
                    <td>{{ $user->phone ?? 'N/A' }}</td>
                    <td>
                        @if($user->companies->count() > 0)
                            <div class="companies-list">
                                @foreach($user->companies as $company)
                                    <div class="company-item mb-1">
                                        <strong>{{ $company->name }}</strong>
                                        @if($company->pivot->role_id)
                                            @php
                                                $role = \App\Models\Role::find($company->pivot->role_id);
                                            @endphp
                                            @if($role)
                                                <span class="badge badge-primary">{{ $role->name }}</span>
                                            @else
                                                <span class="badge badge-secondary">No Role</span>
                                            @endif
                                        @else
                                            <span class="badge badge-secondary">No Role</span>
                                        @endif
                                        @if($user->current_company_id == $company->id)
                                            <span class="badge badge-success">Current</span>
                                        @endif
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <span class="text-muted">No Company Assigned</span>
                        @endif
                    </td>
                    <td>
                        <span class="status-badge status-{{ $user->status }}">
                            {{ ucfirst($user->status) }}
                        </span>
                    </td>
                    <td>
                        @if($user->updated_at)
                            {{ $user->updated_at->format('d M Y') }}
                            <br><small class="text-muted">{{ $user->updated_at->format('h:i A') }}</small>
                        @else
                            <span class="text-muted">Never</span>
                        @endif
                    </td>
                    <td>
                        <a href="{{ route('masters.users.show', $user) }}" class="btn action-btn btn-view" title="View">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="{{ route('masters.users.edit', $user) }}" class="btn action-btn btn-edit" title="Edit">
                            <i class="fas fa-edit"></i>
                        </a>
                        @if($user->id !== auth()->id())
                        <form action="{{ route('masters.users.destroy', $user) }}" method="POST" style="display: inline;" 
                              onsubmit="return confirm('Are you sure you want to delete this user?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn action-btn btn-delete" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </form>
                        @endif
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="7" class="text-center py-4">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                                <p class="text-muted">No users found. <a href="{{ route('masters.users.create') }}">Create your first user</a></p>
                    </td>
                </tr>
                @endforelse
            </tbody>
        </table>
    </div>
    
    @if($users->hasPages())
    <div class="pagination-container">
        {{ $users->links('pagination.custom') }}
    </div>
    @endif
</div>
@endsection

@push('scripts')
<script>
    // Search functionality
    document.getElementById('searchInput').addEventListener('keyup', function() {
        const searchTerm = this.value.toLowerCase();
        const tableRows = document.querySelectorAll('.custom-table tbody tr');
        
        tableRows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });
</script>
@endpush
