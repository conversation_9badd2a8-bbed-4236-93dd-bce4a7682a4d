@extends('layouts.app')

@section('title', 'City Master')

@push('styles')
<style>
    .page-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, #224abe 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        margin-bottom: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .page-header h1 {
        font-size: 1.5rem;
        margin-bottom: 5px;
    }

    .page-header p {
        font-size: 0.85rem;
        margin-bottom: 0;
    }

    .stats-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
    }

    .stat-card {
        background: white;
        border-radius: 8px;
        padding: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        border-left: 4px solid var(--primary-color);
        transition: transform 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-2px);
    }

    .stat-card h3 {
        font-size: 1.8rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 5px;
    }

    .stat-card p {
        color: var(--secondary-color);
        margin: 0;
        font-size: 0.85rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
    }

    .content-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .content-header {
        background: var(--primary-color);
        color: white;
        padding: 12px 20px;
        display: flex;
        justify-content: between;
        align-items: center;
        font-weight: 600;
        font-size: 0.95rem;
    }

    .content-body {
        padding: 20px;
    }

    .search-container {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
        align-items: center;
    }

    .search-input {
        flex: 1;
        border: 1px solid #e3e6f0;
        border-radius: 6px;
        padding: 8px 12px;
        font-size: 0.9rem;
        background: #f8f9fc;
    }

    .search-input:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.15rem rgba(78, 115, 223, 0.2);
        background: white;
        outline: none;
    }

    .btn-add {
        background: var(--success-color);
        border: none;
        color: white;
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        transition: all 0.3s ease;
        font-size: 0.85rem;
    }

    .btn-add:hover {
        background: #1cc88a;
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(28, 200, 138, 0.4);
        color: white;
    }

    .table-container {
        overflow-x: auto;
    }

    .data-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 0.85rem;
    }

    .data-table th {
        background: #f8f9fc;
        color: var(--dark-color);
        font-weight: 600;
        padding: 10px 12px;
        text-align: left;
        border-bottom: 2px solid #e3e6f0;
        font-size: 0.8rem;
        text-transform: uppercase;
        letter-spacing: 0.3px;
    }

    .data-table td {
        padding: 10px 12px;
        border-bottom: 1px solid #e3e6f0;
        vertical-align: middle;
    }

    .data-table tr:hover {
        background: rgba(78, 115, 223, 0.05);
    }

    .status-badge {
        padding: 3px 8px;
        border-radius: 10px;
        font-weight: 600;
        font-size: 0.7rem;
        text-transform: uppercase;
        letter-spacing: 0.3px;
    }

    .status-active {
        background: rgba(28, 200, 138, 0.1);
        color: var(--success-color);
    }

    .status-inactive {
        background: rgba(133, 135, 150, 0.1);
        color: var(--secondary-color);
    }

    .action-buttons {
        display: flex;
        gap: 5px;
    }

    .btn-edit {
        background: var(--warning-color);
        border: none;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.75rem;
        transition: all 0.3s ease;
    }

    .btn-edit:hover {
        background: #dda20a;
        transform: translateY(-1px);
    }

    .btn-delete {
        background: var(--danger-color);
        border: none;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.75rem;
        transition: all 0.3s ease;
    }

    .btn-delete:hover {
        background: #c82333;
        transform: translateY(-1px);
    }

    .pagination-container {
        display: flex;
        justify-content: center;
        margin-top: 15px;
    }

    /* Filter Section Styles */
    .filter-section {
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
        border: 1px solid #e3e6f0;
        margin-bottom: 20px;
    }

    .search-container {
        position: relative;
    }

    .search-icon {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        z-index: 2;
    }

    .search-input {
        padding-left: 40px !important;
        border: 1px solid #d1d3e2;
        border-radius: 6px;
        font-size: 0.875rem;
        transition: all 0.2s ease;
    }

    .search-input:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    }

    .filter-select {
        border: 1px solid #d1d3e2;
        border-radius: 6px;
        font-size: 0.875rem;
        transition: all 0.2s ease;
    }

    .filter-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    }

    .filter-btn {
        width: 100%;
        border-radius: 6px;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .filter-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .breadcrumb-container {
        background: white;
        border-radius: 6px;
        padding: 10px 15px;
        margin-bottom: 15px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .breadcrumb {
        margin: 0;
        background: none;
        padding: 0;
        font-size: 0.85rem;
    }

    .breadcrumb-item a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
    }

    .breadcrumb-item a:hover {
        text-decoration: underline;
    }

    .breadcrumb-item.active {
        color: var(--secondary-color);
    }

    /* Modal Styles */
    .modal-content {
        border: none;
        border-radius: 8px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    }

    .modal-header {
        background: var(--primary-color);
        color: white;
        border-radius: 8px 8px 0 0;
        padding: 12px 20px;
    }

    .modal-title {
        font-size: 1rem;
        font-weight: 600;
    }

    .btn-close {
        filter: invert(1);
    }

    .modal-body {
        padding: 20px;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .form-label {
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 5px;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.3px;
    }

    .form-control {
        border: 1px solid #e3e6f0;
        border-radius: 6px;
        padding: 8px 12px;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        background: #f8f9fc;
    }

    .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.15rem rgba(78, 115, 223, 0.2);
        background: white;
        outline: none;
    }

    .form-select {
        border: 1px solid #e3e6f0;
        border-radius: 6px;
        padding: 8px 12px;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        background: #f8f9fc;
    }

    .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.15rem rgba(78, 115, 223, 0.2);
        background: white;
        outline: none;
    }

    .btn-submit {
        background: var(--primary-color);
        border: none;
        color: white;
        padding: 8px 20px;
        border-radius: 6px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        transition: all 0.3s ease;
        font-size: 0.85rem;
    }

    .btn-submit:hover {
        background: #224abe;
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(78, 115, 223, 0.4);
        color: white;
    }

    .btn-cancel {
        background: var(--secondary-color);
        border: none;
        color: white;
        padding: 8px 20px;
        border-radius: 6px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        transition: all 0.3s ease;
        font-size: 0.85rem;
    }

    .btn-cancel:hover {
        background: #6c757d;
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(133, 135, 150, 0.4);
        color: white;
    }

    .required {
        color: var(--danger-color);
    }

    .state-badge {
        background: rgba(78, 115, 223, 0.1);
        color: var(--primary-color);
        padding: 2px 8px;
        border-radius: 8px;
        font-size: 0.75rem;
        font-weight: 600;
    }
</style>
@endpush

@section('content')
<!-- Breadcrumb -->
<div class="breadcrumb-container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="#">Masters</a></li>
            <li class="breadcrumb-item active" aria-current="page">City Master</li>
        </ol>
    </nav>
</div>

<!-- Page Header -->
<div class="page-header">
    <h1><i class="fas fa-city"></i> City Master</h1>
    <p>Manage cities and their state associations</p>
</div>

<!-- Statistics Cards -->
<div class="stats-container">
    <div class="stat-card">
        <h3>{{ $cities->total() }}</h3>
        <p>Total Cities</p>
    </div>
    <div class="stat-card">
        <h3>{{ $cities->where('status', 'active')->count() }}</h3>
        <p>Active Cities</p>
    </div>
    <div class="stat-card">
        <h3>{{ $cities->where('status', 'inactive')->count() }}</h3>
        <p>Inactive Cities</p>
    </div>
    <div class="stat-card">
        <h3>{{ $states->count() }}</h3>
        <p>Available States</p>
    </div>
</div>

<!-- Content Container -->
<div class="content-container">
    <div class="content-header">
        <span><i class="fas fa-list"></i> City List</span>
    </div>
    
    <div class="content-body">
        <!-- Search and Filter Form -->
        <div class="filter-section mb-4">
            <form method="GET" action="{{ route('masters.city') }}" class="row g-3">
                <div class="col-md-3">
                    <div class="search-container">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="form-control search-input" name="search"
                               value="{{ request('search') }}" placeholder="Search cities or states...">
                    </div>
                </div>
                <div class="col-md-2">
                    <select class="form-select filter-select" name="state_id">
                        <option value="">All States</option>
                        @foreach($states as $state)
                            <option value="{{ $state->id }}" {{ request('state_id') == $state->id ? 'selected' : '' }}>
                                {{ $state->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select filter-select" name="status">
                        <option value="">All Status</option>
                        <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                        <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary filter-btn">
                        <i class="fas fa-search me-1"></i>Search
                    </button>
                </div>
                <div class="col-md-2">
                    <a href="{{ route('masters.city') }}" class="btn btn-outline-secondary filter-btn">
                        <i class="fas fa-times me-1"></i>Clear
                    </a>
                </div>
                <div class="col-md-1 text-end">
                    <button type="button" class="btn btn-add" data-bs-toggle="modal" data-bs-target="#cityModal" onclick="openAddModal()">
                        <i class="fas fa-plus"></i> Add City
                    </button>
                </div>
            </form>
        </div>

        <!-- Cities Table -->
        <div class="table-container">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>City Name</th>
                        <th>State</th>
                        <th>Status</th>
                        <th>Created Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="citiesTableBody">
                    @foreach($cities as $city)
                    <tr>
                        <td>{{ $city->id }}</td>
                        <td><strong>{{ $city->name }}</strong></td>
                        <td>
                            <span class="state-badge">{{ $city->state->name }}</span>
                        </td>
                        <td>
                            <span class="status-badge status-{{ $city->status }}">
                                {{ ucfirst($city->status) }}
                            </span>
                        </td>
                        <td>{{ $city->created_at->format('d M Y') }}</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-edit" onclick="editCity({{ $city->id }}, '{{ $city->name }}', {{ $city->state_id }}, '{{ $city->status }}')">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-delete" onclick="deleteCity({{ $city->id }})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        @if($cities->hasPages())
        <div class="pagination-container">
            {{ $cities->links('pagination.custom') }}
        </div>
        @endif
    </div>
</div>

<!-- City Modal -->
<div class="modal fade" id="cityModal" tabindex="-1" aria-labelledby="cityModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="cityModalLabel">Add New City</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="cityForm">
                    <input type="hidden" id="cityId" name="id">
                    
                    <div class="form-group">
                        <label for="cityName" class="form-label">City Name <span class="required">*</span></label>
                        <input type="text" class="form-control" id="cityName" name="name" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="stateId" class="form-label">State <span class="required">*</span></label>
                        <select class="form-select" id="stateId" name="state_id" required>
                            <option value="">Select State</option>
                            @foreach($states as $state)
                                <option value="{{ $state->id }}">{{ $state->name }} ({{ $state->code }})</option>
                            @endforeach
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="cityStatus" class="form-label">Status <span class="required">*</span></label>
                        <select class="form-select" id="cityStatus" name="status" required>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                    
                    <div class="d-flex justify-content-end gap-2">
                        <button type="button" class="btn btn-cancel" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-submit">Save City</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    let isEditMode = false;

    // Open Add Modal
    function openAddModal() {
        isEditMode = false;
        document.getElementById('cityModalLabel').textContent = 'Add New City';
        document.getElementById('cityForm').reset();
        document.getElementById('cityId').value = '';
    }

    // Edit City
    function editCity(id, name, stateId, status) {
        isEditMode = true;
        document.getElementById('cityModalLabel').textContent = 'Edit City';
        document.getElementById('cityId').value = id;
        document.getElementById('cityName').value = name;
        document.getElementById('stateId').value = stateId;
        document.getElementById('cityStatus').value = status;
        
        const modal = new bootstrap.Modal(document.getElementById('cityModal'));
        modal.show();
    }

    // Delete City
    function deleteCity(id) {
        if (confirm('Are you sure you want to delete this city?')) {
            fetch(`/masters/city/${id}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Error deleting city');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error deleting city');
            });
        }
    }

    // Form Submit
    document.getElementById('cityForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const data = Object.fromEntries(formData);
        
        const url = isEditMode ? `/masters/city/${data.id}` : '/masters/city';
        const method = isEditMode ? 'PUT' : 'POST';
        
        fetch(url, {
            method: method,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error saving city');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error saving city');
        });
    });

    // Search functionality
    document.getElementById('searchInput').addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const rows = document.querySelectorAll('#citiesTableBody tr');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });
</script>
@endpush
