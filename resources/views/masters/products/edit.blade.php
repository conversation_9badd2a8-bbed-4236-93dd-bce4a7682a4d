@extends('layouts.app')

@section('title', 'Edit Product')

@push('styles')
<style>
    .page-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, #224abe 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        margin-bottom: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .page-header h1 {
        font-size: 1.5rem;
        margin-bottom: 5px;
    }

    .page-header p {
        font-size: 0.85rem;
        margin-bottom: 0;
    }

    .form-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .form-header {
        background: var(--primary-color);
        color: white;
        padding: 12px 20px;
        font-weight: 600;
        font-size: 0.95rem;
    }

    .form-body {
        padding: 20px;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .form-label {
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 5px;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.3px;
    }

    .form-control {
        border: 1px solid #e3e6f0;
        border-radius: 6px;
        padding: 8px 12px;
        font-size: 0.9rem;
        transition: all 0.3s ease;
       
    }

    .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.15rem rgba(78, 115, 223, 0.2);
        background: white;
        outline: none;
    }

    .form-select {
        border: 1px solid #e3e6f0;
        border-radius: 6px;
        padding: 8px 12px;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        background: #f8f9fc;
    }

    .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.15rem rgba(78, 115, 223, 0.2);
        background: white;
        outline: none;
    }

    .btn-submit {
        background: var(--primary-color);
        border: none;
        color: white;
        padding: 8px 20px;
        border-radius: 6px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        transition: all 0.3s ease;
        font-size: 0.85rem;
    }

    .btn-submit:hover {
        background: #224abe;
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(78, 115, 223, 0.4);
        color: white;
    }

    .btn-cancel {
        background: var(--secondary-color);
        border: none;
        color: white;
        padding: 8px 20px;
        border-radius: 6px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        transition: all 0.3s ease;
        font-size: 0.85rem;
        text-decoration: none;
        display: inline-block;
    }

    .btn-cancel:hover {
        background: #6c757d;
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(133, 135, 150, 0.4);
        color: white;
        text-decoration: none;
    }

    .invalid-feedback {
        display: block;
        color: var(--danger-color);
        font-size: 0.8rem;
        margin-top: 3px;
        font-weight: 500;
    }

    .is-invalid {
        border-color: var(--danger-color) !important;
        box-shadow: 0 0 0 0.15rem rgba(231, 74, 59, 0.2) !important;
    }

    .form-text {
        color: var(--secondary-color);
        font-size: 0.8rem;
        margin-top: 3px;
    }

    .required {
        color: var(--danger-color);
    }

    .button-group {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
        margin-top: 20px;
        padding-top: 15px;
        border-top: 1px solid #e3e6f0;
    }

    .breadcrumb-container {
        background: white;
        border-radius: 6px;
        padding: 10px 15px;
        margin-bottom: 15px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .breadcrumb {
        margin: 0;
        background: none;
        padding: 0;
        font-size: 0.85rem;
    }

    .breadcrumb-item a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
    }

    .breadcrumb-item a:hover {
        text-decoration: underline;
    }

    .breadcrumb-item.active {
        color: var(--secondary-color);
    }

    .input-group-text {
        background: var(--light-color);
        border: 1px solid #e3e6f0;
        color: var(--dark-color);
        font-size: 0.85rem;
        padding: 8px 12px;
    }

    .row .col-md-6 {
        padding-left: 7.5px;
        padding-right: 7.5px;
    }
</style>
@endpush

@section('content')
<!-- Breadcrumb -->
<div class="breadcrumb-container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="#">Masters</a></li>
            <li class="breadcrumb-item"><a href="{{ route('products.index') }}">Products</a></li>
            <li class="breadcrumb-item active" aria-current="page">Edit {{ $product->name }}</li>
        </ol>
    </nav>
</div>

<!-- Page Header -->
<div class="page-header">
    <h1><i class="fas fa-edit"></i> Edit Product</h1>
    <p>Update product information for {{ $product->name }}</p>
</div>

<!-- Product Form -->
<div class="form-container">
    <div class="form-header">
        <i class="fas fa-box"></i> Product Information
    </div>
    
    <div class="form-body">
        <form action="{{ route('products.update', $product) }}" method="POST">
            @csrf
            @method('PUT')
            
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="name" class="form-label">Product Name <span class="required">*</span></label>
                        <input type="text" class="form-control @error('name') is-invalid @enderror" 
                               id="name" name="name" value="{{ old('name', $product->name) }}" required>
                        @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="category_id" class="form-label">Group <span class="required">*</span></label>
                        <select class="form-select @error('category_id') is-invalid @enderror" 
                                id="category_id" name="category_id" required>
                            <option value="">Select Group</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}" {{ old('category_id', $product->category_id) == $category->id ? 'selected' : '' }}>
                                    {{ $category->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('category_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            

                
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="hsn_code" class="form-label">HSN Code</label>
                        <input type="text" class="form-control @error('hsn_code') is-invalid @enderror" 
                               id="hsn_code" name="hsn_code" value="{{ old('hsn_code', $product->hsn_code) }}">
                        <div class="form-text">Harmonized System of Nomenclature</div>
                        @error('hsn_code')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="gst_percentage" class="form-label">GST % <span class="required">*</span></label>
                        <div class="input-group">
                            <input type="number" class="form-control @error('gst_percentage') is-invalid @enderror" 
                                   id="gst_percentage" name="gst_percentage" value="{{ old('gst_percentage', $product->gst_percentage) }}" 
                                   min="0" max="100" step="0.01" required>
                            <span class="input-group-text">%</span>
                        </div>
                        @error('gst_percentage')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="unit" class="form-label">Unit <span class="required">*</span></label>
                        <select class="form-select @error('unit') is-invalid @enderror" id="unit" name="unit" required>
                            <option value="">Select Unit</option>
                             <option value="PCS" {{ old('unit') == 'MT' ? 'selected' : '' }}>MT</option>
                            <option value="PCS" {{ old('unit', $product->unit) == 'PCS' ? 'selected' : '' }}>PCS (Pieces)</option>
                            <option value="KG" {{ old('unit', $product->unit) == 'KG' ? 'selected' : '' }}>KG (Kilogram)</option>
                            <option value="GRAM" {{ old('unit', $product->unit) == 'GRAM' ? 'selected' : '' }}>GRAM</option>
                            <option value="LITER" {{ old('unit', $product->unit) == 'LITER' ? 'selected' : '' }}>LITER</option>
                            <option value="METER" {{ old('unit', $product->unit) == 'METER' ? 'selected' : '' }}>METER</option>
                            <option value="BOX" {{ old('unit', $product->unit) == 'BOX' ? 'selected' : '' }}>BOX</option>
                            <option value="SET" {{ old('unit', $product->unit) == 'SET' ? 'selected' : '' }}>SET</option>
                        </select>
                        @error('unit')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="stock_quantity" class="form-label">Stock Quantity <span class="required">*</span></label>
                        <input type="number" class="form-control @error('stock_quantity') is-invalid @enderror" 
                               id="stock_quantity" name="stock_quantity" value="{{ old('stock_quantity', $product->stock_quantity) }}" 
                               min="0" required>
                        @error('stock_quantity')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="rate" class="form-label">Rate (₹) <span class="required">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text">₹</span>
                            <input type="number" class="form-control @error('rate') is-invalid @enderror" 
                                   id="rate" name="rate" value="{{ old('rate', $product->rate) }}" 
                                   min="0" step="0.01" required>
                        </div>
                        <div class="form-text">Base rate before GST</div>
                        @error('rate')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="price" class="form-label">Selling Price (₹) <span class="required">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text">₹</span>
                            <input type="number" class="form-control @error('price') is-invalid @enderror" 
                                   id="price" name="price" value="{{ old('price', $product->price) }}" 
                                   min="0" step="0.01" required>
                        </div>
                        <div class="form-text">Final selling price including GST</div>
                        @error('price')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="price" class="form-label">Gauge Difference <span class="required">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text">₹</span>
                            <input type="number" class="form-control @error('gauge_diff') is-invalid @enderror" 
                                   id="gauge_diff" name="gauge_diff" value="{{ old('gauge_diff') }}" 
                                   min="0" step="0.01" readonly>
                        </div>
                       
                        @error('gauge_diff')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <div class="form-group">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="1">{{ old('description', $product->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="status" class="form-label">Status <span class="required">*</span></label>
                        <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
                            <option value="active" {{ old('status', $product->status) == 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ old('status', $product->status) == 'inactive' ? 'selected' : '' }}>Inactive</option>
                        </select>
                        @error('status')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <div class="button-group">
                <a href="{{ route('products.show', $product) }}" class="btn btn-cancel">
                    <i class="fas fa-times"></i> Cancel
                </a>
                <button type="submit" class="btn btn-submit">
                    <i class="fas fa-save"></i> Update Product
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // Auto-calculate price based on rate and GST
    document.getElementById('rate').addEventListener('input', calculatePrice);
    document.getElementById('gst_percentage').addEventListener('input', calculatePrice);

    function calculatePrice() {
        const rate = parseFloat(document.getElementById('rate').value) || 0;
        const gst = parseFloat(document.getElementById('gst_percentage').value) || 0;
        const price = rate + (rate * gst / 100);
        
        if (rate > 0) {
            document.getElementById('price').value = price.toFixed(2);
        }
    }
</script>

<script>
  document.addEventListener('DOMContentLoaded', function () {
    const rateInput = document.getElementById('rate');
    const priceInput = document.getElementById('price');
    const gaugeDiffInput = document.getElementById('gauge_diff');

    function updateGaugeDiff() {
      const rate = parseFloat(rateInput.value) || 0;
      const price = parseFloat(priceInput.value) || 0;
      const diff = price - rate;

      gaugeDiffInput.value = diff.toFixed(2); // Always 2 decimal places
    }

    rateInput.addEventListener('input', updateGaugeDiff);
    priceInput.addEventListener('input', updateGaugeDiff);
  });
</script>
@endpush
