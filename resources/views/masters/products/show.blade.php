@extends('layouts.app')

@section('title', 'Product Details')

@push('styles')
<style>
    .page-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, #224abe 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        margin-bottom: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .page-header h1 {
        font-size: 1.5rem;
        margin-bottom: 5px;
    }

    .page-header p {
        font-size: 0.85rem;
        margin-bottom: 0;
    }

    .details-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .details-header {
        background: var(--primary-color);
        color: white;
        padding: 12px 20px;
        font-weight: 600;
        font-size: 0.95rem;
    }

    .details-body {
        padding: 20px;
    }

    .detail-row {
        display: flex;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid #f1f3f4;
    }

    .detail-row:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .detail-label {
        font-weight: 600;
        color: var(--dark-color);
        width: 150px;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.3px;
    }

    .detail-value {
        flex: 1;
        color: var(--secondary-color);
        font-size: 0.9rem;
    }

    .status-badge {
        padding: 3px 10px;
        border-radius: 12px;
        font-weight: 600;
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.3px;
    }

    .status-active {
        background: rgba(28, 200, 138, 0.1);
        color: var(--success-color);
    }

    .status-inactive {
        background: rgba(133, 135, 150, 0.1);
        color: var(--secondary-color);
    }

    .gst-badge {
        background: rgba(78, 115, 223, 0.1);
        color: var(--primary-color);
        padding: 3px 8px;
        border-radius: 10px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .unit-badge {
        background: rgba(246, 194, 62, 0.1);
        color: var(--warning-color);
        padding: 3px 8px;
        border-radius: 10px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .stock-badge {
        padding: 3px 8px;
        border-radius: 10px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .stock-in {
        background: rgba(28, 200, 138, 0.1);
        color: var(--success-color);
    }

    .stock-low {
        background: rgba(246, 194, 62, 0.1);
        color: var(--warning-color);
    }

    .stock-out {
        background: rgba(231, 74, 59, 0.1);
        color: var(--danger-color);
    }

    .action-buttons {
        display: flex;
        gap: 10px;
        margin-top: 20px;
        padding-top: 15px;
        border-top: 1px solid #e3e6f0;
    }

    .btn-edit {
        background: var(--warning-color);
        border: none;
        color: white;
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        transition: all 0.3s ease;
        font-size: 0.85rem;
        text-decoration: none;
        display: inline-block;
    }

    .btn-edit:hover {
        background: #dda20a;
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(246, 194, 62, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-back {
        background: var(--secondary-color);
        border: none;
        color: white;
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        transition: all 0.3s ease;
        font-size: 0.85rem;
        text-decoration: none;
        display: inline-block;
    }

    .btn-back:hover {
        background: #6c757d;
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(133, 135, 150, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-delete {
        background: var(--danger-color);
        border: none;
        color: white;
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        transition: all 0.3s ease;
        font-size: 0.85rem;
    }

    .btn-delete:hover {
        background: #c82333;
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(231, 74, 59, 0.4);
        color: white;
    }

    .breadcrumb-container {
        background: white;
        border-radius: 6px;
        padding: 10px 15px;
        margin-bottom: 15px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .breadcrumb {
        margin: 0;
        background: none;
        padding: 0;
        font-size: 0.85rem;
    }

    .breadcrumb-item a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
    }

    .breadcrumb-item a:hover {
        text-decoration: underline;
    }

    .breadcrumb-item.active {
        color: var(--secondary-color);
    }

    .price-info {
        background: rgba(78, 115, 223, 0.05);
        padding: 10px;
        border-radius: 6px;
        border-left: 3px solid var(--primary-color);
    }

    .price-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;
        font-size: 0.9rem;
    }

    .price-row:last-child {
        margin-bottom: 0;
        font-weight: 600;
        font-size: 1rem;
        color: var(--primary-color);
    }

    .price-label {
        color: var(--secondary-color);
    }

    .price-value {
        color: var(--dark-color);
        font-weight: 500;
    }
</style>
@endpush

@section('content')
<!-- Breadcrumb -->
<div class="breadcrumb-container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="#">Masters</a></li>
            <li class="breadcrumb-item"><a href="{{ route('products.index') }}">Products</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ $product->name }}</li>
        </ol>
    </nav>
</div>

<!-- Page Header -->
<div class="page-header">
    <h1><i class="fas fa-box"></i> Product Details</h1>
    <p>View complete information for {{ $product->name }}</p>
</div>

<!-- Product Details -->
<div class="details-container">
    <div class="details-header">
        <i class="fas fa-info-circle"></i> Product Information
    </div>
    
    <div class="details-body">
        <div class="row">
            <div class="col-md-8">
                <div class="detail-row">
                    <div class="detail-label">Product Name:</div>
                    <div class="detail-value"><strong>{{ $product->name }}</strong></div>
                </div>



                <div class="detail-row">
                    <div class="detail-label">Group:</div>
                    <div class="detail-value">{{ $product->category->name ?? 'N/A' }}</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">HSN Code:</div>
                    <div class="detail-value">{{ $product->hsn_code ?? 'N/A' }}</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">GST Percentage:</div>
                    <div class="detail-value"><span class="gst-badge">{{ $product->gst_percentage }}%</span></div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">Unit:</div>
                    <div class="detail-value"><span class="unit-badge">{{ $product->unit }}</span></div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">Stock Quantity:</div>
                    <div class="detail-value">
                        @if($product->stock_quantity <= 0)
                            <span class="stock-badge stock-out">{{ $product->stock_quantity }} (Out of Stock)</span>
                        @elseif($product->stock_quantity <= 10)
                            <span class="stock-badge stock-low">{{ $product->stock_quantity }} (Low Stock)</span>
                        @else
                            <span class="stock-badge stock-in">{{ $product->stock_quantity }} (In Stock)</span>
                        @endif
                    </div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">Status:</div>
                    <div class="detail-value">
                        <span class="status-badge status-{{ $product->status }}">
                            {{ ucfirst($product->status) }}
                        </span>
                    </div>
                </div>

                @if($product->description)
                <div class="detail-row">
                    <div class="detail-label">Description:</div>
                    <div class="detail-value">{{ $product->description }}</div>
                </div>
                @endif

                <div class="detail-row">
                    <div class="detail-label">Created:</div>
                    <div class="detail-value">{{ $product->created_at->format('d M Y, h:i A') }}</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">Last Updated:</div>
                    <div class="detail-value">{{ $product->updated_at->format('d M Y, h:i A') }}</div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="price-info">
                    <h6 class="mb-3"><i class="fas fa-rupee-sign"></i> Pricing Information</h6>
                    
                    <div class="price-row">
                        <span class="price-label">Base Rate:</span>
                        <span class="price-value">₹{{ number_format($product->rate, 2) }}</span>
                    </div>
                    
                    <div class="price-row">
                        <span class="price-label">GST ({{ $product->gst_percentage }}%):</span>
                        <span class="price-value">₹{{ number_format($product->rate * $product->gst_percentage / 100, 2) }}</span>
                    </div>
                    
                    <hr style="margin: 8px 0;">
                    
                    <div class="price-row">
                        <span class="price-label">Selling Price:</span>
                        <span class="price-value">₹{{ number_format($product->price, 2) }}</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="action-buttons">
            <a href="{{ route('products.index') }}" class="btn btn-back">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
            <a href="{{ route('products.edit', $product) }}" class="btn btn-edit">
                <i class="fas fa-edit"></i> Edit Product
            </a>
            <form action="{{ route('products.destroy', $product) }}" method="POST" style="display: inline;" 
                  onsubmit="return confirm('Are you sure you want to delete this product?')">
                @csrf
                @method('DELETE')
                <button type="submit" class="btn btn-delete">
                    <i class="fas fa-trash"></i> Delete Product
                </button>
            </form>
        </div>
    </div>
</div>
@endsection
