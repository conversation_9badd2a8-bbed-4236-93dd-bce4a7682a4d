@extends('layouts.app')

@section('title', 'Product Master')

@push('styles')
<style>
    .page-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, #224abe 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        margin-bottom: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .page-header h1 {
        font-size: 1.5rem;
        margin-bottom: 5px;
    }

    .page-header p {
        font-size: 0.85rem;
        margin-bottom: 0;
    }

    .stats-card {
        background: white;
        border-radius: 8px;
        padding: 10px 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        border-left: 3px solid var(--primary-color);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    }

    .stats-number {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 2px;
    }

    .stats-label {
        color: var(--secondary-color);
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 0.7rem;
    }

    .action-buttons {
        display: flex;
        gap: 8px;
        margin-bottom: 10px;
        align-items: center;
    }

    .btn-create {
        background: var(--success-color);
        border: none;
        color: white;
        padding: 6px 12px;
        border-radius: 6px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        transition: all 0.3s ease;
        font-size: 0.8rem;
    }

    .btn-create:hover {
        background: #17a673;
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(28, 200, 138, 0.4);
        color: white;
    }

    .btn-create[style*="info-color"]:hover {
        background: #138496 !important;
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(23, 162, 184, 0.4);
        color: white;
    }

    .search-box {
        background: white;
        border: 1px solid #e3e6f0;
        border-radius: 6px;
        padding: 6px 12px;
        font-size: 0.85rem;
        transition: border-color 0.3s ease;
        width: 200px;
    }

    .search-box:focus {
        border-color: var(--primary-color);
        outline: none;
        box-shadow: 0 0 0 0.15rem rgba(78, 115, 223, 0.2);
    }

    /* Filter Section Styles */
    .filter-section {
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
        border: 1px solid #e3e6f0;
    }

    .search-container {
        position: relative;
    }

    .search-icon {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        z-index: 2;
    }

    .search-input {
        padding-left: 40px;
        border: 1px solid #d1d3e2;
        border-radius: 6px;
        font-size: 0.875rem;
        transition: all 0.2s ease;
    }

    .search-input:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    }

    .filter-select {
        border: 1px solid #d1d3e2;
        border-radius: 6px;
        font-size: 0.875rem;
        transition: all 0.2s ease;
    }

    .filter-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    }

    .filter-btn {
        width: 100%;
        border-radius: 6px;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .filter-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .table-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .table-header {
        background: var(--primary-color);
        color: white;
        padding: 10px 15px;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .custom-table {
        margin: 0;
    }

    .custom-table thead th {
        background: var(--light-color);
        color: var(--dark-color);
        font-weight: 600;
        border: none;
        padding: 8px 12px;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        font-size: 0.75rem;
    }

    .custom-table tbody td {
        padding: 8px 12px;
        vertical-align: middle;
        border-color: #e3e6f0;
        font-size: 0.85rem;
    }

    .custom-table tbody tr:hover {
        background: rgba(78, 115, 223, 0.03);
    }

    .status-badge {
        padding: 2px 8px;
        border-radius: 12px;
        font-weight: 600;
        font-size: 0.7rem;
        text-transform: uppercase;
        letter-spacing: 0.3px;
    }

    .status-active {
        background: rgba(28, 200, 138, 0.1);
        color: var(--success-color);
    }

    .status-inactive {
        background: rgba(133, 135, 150, 0.1);
        color: var(--secondary-color);
    }

    .action-btn {
        padding: 4px 8px;
        border-radius: 4px;
        border: none;
        font-weight: 500;
        font-size: 0.75rem;
        transition: all 0.3s ease;
        margin: 0 1px;
    }

    .btn-view {
        background: var(--info-color);
        color: white;
    }

    .btn-view:hover {
        background: #138496;
        color: white;
    }

    .btn-edit {
        background: var(--warning-color);
        color: white;
    }

    .btn-edit:hover {
        background: #dda20a;
        color: white;
    }

    .btn-delete {
        background: var(--danger-color);
        color: white;
    }

    .btn-delete:hover {
        background: #c82333;
        color: white;
    }

    .pagination-container {
        padding: 15px;
        background: white;
        border-top: 1px solid #e3e6f0;
    }

    .alert-custom {
        border-radius: 8px;
        border: none;
        padding: 10px 15px;
        margin-bottom: 15px;
        font-weight: 500;
        font-size: 0.9rem;
    }

    .breadcrumb-container {
        background: white;
        border-radius: 6px;
        padding: 10px 15px;
        margin-bottom: 15px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .breadcrumb {
        margin: 0;
        background: none;
        padding: 0;
        font-size: 0.85rem;
    }

    .breadcrumb-item a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
    }

    .breadcrumb-item a:hover {
        text-decoration: underline;
    }

    .breadcrumb-item.active {
        color: var(--secondary-color);
    }

    .gst-badge {
        background: rgba(78, 115, 223, 0.1);
        color: var(--primary-color);
        padding: 2px 6px;
        border-radius: 10px;
        font-size: 0.7rem;
        font-weight: 600;
    }

    .unit-badge {
        background: rgba(246, 194, 62, 0.1);
        color: var(--warning-color);
        padding: 2px 6px;
        border-radius: 10px;
        font-size: 0.7rem;
        font-weight: 600;
    }
</style>
@endpush

@section('content')
<!-- Breadcrumb -->
<div class="breadcrumb-container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="#">Masters</a></li>
            <li class="breadcrumb-item active" aria-current="page">Products</li>
        </ol>
    </nav>
</div>

<!-- Page Header -->
<div class="page-header">
    <h1><i class="fas fa-box"></i> Product Master</h1>
    <p>Manage your product inventory with HSN codes, GST rates, and pricing</p>
</div>

<!-- Success/Error Messages -->
@if(session('success'))
    <div class="alert alert-success alert-custom">
        <i class="fas fa-check-circle"></i> {{ session('success') }}
    </div>
@endif

@if(session('error'))
    <div class="alert alert-danger alert-custom">
        <i class="fas fa-exclamation-circle"></i> {{ session('error') }}
    </div>
@endif

<!-- Statistics Cards -->
<div class="row mb-3">
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number">{{ $products->total() }}</div>
            <div class="stats-label">Total Products</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number">{{ $products->where('status', 'active')->count() }}</div>
            <div class="stats-label">Active Products</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number">{{ $products->where('stock_quantity', '>', 0)->count() }}</div>
            <div class="stats-label">In Stock</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number">{{ $products->where('stock_quantity', '<=', 10)->count() }}</div>
            <div class="stats-label">Low Stock</div>
        </div>
    </div>
</div>

<!-- Search and Filter Section -->
<div class="filter-section mb-4">
    <form method="GET" action="{{ route('products.index') }}" class="row g-3">
        <div class="col-md-4">
            <div class="search-container">
                <i class="fas fa-search search-icon"></i>
                <input type="text" class="form-control search-input" name="search"
                       value="{{ request('search') }}" placeholder="Search by name, HSN code...">
            </div>
        </div>
        <div class="col-md-2">
            <select class="form-select filter-select" name="category_id">
                <option value="">All Categories</option>
                @if(isset($categories))
                    @foreach($categories as $category)
                        <option value="{{ $category->id }}" {{ request('category_id') == $category->id ? 'selected' : '' }}>
                            {{ $category->name }}
                        </option>
                    @endforeach
                @endif
            </select>
        </div>
        <div class="col-md-2">
            <select class="form-select filter-select" name="status">
                <option value="">All Status</option>
                <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
            </select>
        </div>
        <div class="col-md-2">
            <button type="submit" class="btn btn-primary filter-btn">
                <i class="fas fa-search me-1"></i>Search
            </button>
        </div>
        <div class="col-md-2">
            <a href="{{ route('products.index') }}" class="btn btn-outline-secondary filter-btn">
                <i class="fas fa-times me-1"></i>Clear
            </a>
        </div>
    </form>
</div>

<!-- Action Buttons -->
<div class="action-buttons">
    @permission('products.create')
    <a href="{{ route('products.create') }}" class="btn btn-create">
        <i class="fas fa-plus"></i> Add New Product
    </a>
    @endpermission

    @permission('customers.create')
    <a href="{{ route('customers.create') }}" class="btn btn-create" style="background: var(--info-color);">
        <i class="fas fa-user-plus"></i> Add New Customer
    </a>
    @endpermission
</div>

<!-- Products Table -->
<div class="table-container">
    <div class="table-header">
        <i class="fas fa-list"></i> Products List
    </div>
    
    <div class="table-responsive">
        <table class="table custom-table">
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Group</th>
                    <th>HSN Code</th>
                    <th>GST %</th>
                    <th>Unit</th>
                    <th>Rate</th>
                    <th>Sale</th>
                    <th>Gauge Diff</th>
                    <th>Stock</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                @forelse($products as $product)
                <tr>
                    <td>
                        <strong>{{ $product->name }}</strong>
                        @if($product->description)
                            <br><small class="text-muted">{{ Str::limit($product->description, 30) }}</small>
                        @endif
                    </td>
                    <td>{{ $product->category->name ?? 'N/A' }}</td>
                    <td>{{ $product->hsn_code ?? 'N/A' }}</td>
                    <td><span class="gst-badge">{{ $product->gst_percentage }}%</span></td>
                    <td><span class="unit-badge">{{ $product->unit }}</span></td>
                    <td>₹{{ number_format($product->rate, 2) }}</td>
                    <td>₹{{ number_format($product->price, 2) }}</td>
                    <td>₹{{ number_format($product->gauge_diff, 2) }}</td>
                    <td>
                        @if($product->stock_quantity <= 0)
                            <span class="badge bg-danger">Out of Stock</span>
                        @elseif($product->stock_quantity <= 10)
                            <span class="badge bg-warning">{{ $product->stock_quantity }}</span>
                        @else
                            <span class="badge bg-success">{{ $product->stock_quantity }}</span>
                        @endif
                    </td>
                    <td>
                        <span class="status-badge status-{{ $product->status }}">
                            {{ ucfirst($product->status) }}
                        </span>
                    </td>
                    <td>
                        @permission('products.view')
                        <a href="{{ route('products.show', $product) }}" class="btn action-btn btn-view" title="View">
                            <i class="fas fa-eye"></i>
                        </a>
                        @endpermission
                          
                        @permission('products.edit')
                        <a href="{{ route('products.edit', $product) }}" class="btn action-btn btn-edit" title="Edit">
                            <i class="fas fa-edit"></i>
                        </a>
                        @endpermission
                           
                        @permission('products.delete')
                        <form action="{{ route('products.destroy', $product) }}" method="POST" style="display: inline;" 
                              onsubmit="return confirm('Are you sure you want to delete this product?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn action-btn btn-delete" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </form>
                        @endpermission
                            
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="10" class="text-center py-4">
                        <i class="fas fa-box fa-3x text-muted mb-3"></i>
                        @permission('products.create')
                        <p class="text-muted">No products found. <a href="{{ route('products.create') }}">Create your first product</a></p>
                        @endpermission
                    </td>
                </tr>
                @endforelse
            </tbody>
        </table>
    </div>
    
    @if($products->hasPages())
    <div class="pagination-container">
        {{ $products->links('pagination.custom') }}
    </div>
    @endif
</div>
@endsection

@push('scripts')
<script>
    // Search functionality
    document.getElementById('searchInput').addEventListener('keyup', function() {
        const searchTerm = this.value.toLowerCase();
        const tableRows = document.querySelectorAll('.custom-table tbody tr');
        
        tableRows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });
</script>
@endpush

@push('scripts')
<script>
    // Search functionality
    document.getElementById('searchInput').addEventListener('keyup', function() {
        const searchTerm = this.value.toLowerCase();
        const tableRows = document.querySelectorAll('.custom-table tbody tr');
        
        tableRows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });
</script>
@endpush
