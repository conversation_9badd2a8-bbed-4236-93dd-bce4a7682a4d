@extends('layouts.app')

@section('title', 'Customer Master')

@push('styles')
<style>
    .page-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, #224abe 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        margin-bottom: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .page-header h1 {
        font-size: 1.5rem;
        margin-bottom: 5px;
    }

    .page-header p {
        font-size: 0.85rem;
        margin-bottom: 0;
    }

    .stats-card {
        background: white;
        border-radius: 8px;
        padding: 10px 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        border-left: 3px solid var(--primary-color);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    }

    .stats-number {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 2px;
    }

    .stats-label {
        color: var(--secondary-color);
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 0.7rem;
    }

    .action-buttons {
        display: flex;
        gap: 8px;
        margin-bottom: 10px;
        align-items: center;
    }

    .btn-create {
        background: var(--success-color);
        border: none;
        color: white;
        padding: 6px 12px;
        border-radius: 6px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        transition: all 0.3s ease;
        font-size: 0.8rem;
    }

    .btn-create:hover {
        background: #17a673;
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(28, 200, 138, 0.4);
        color: white;
    }

    .search-box {
        background: white;
        border: 1px solid #e3e6f0;
        border-radius: 6px;
        padding: 6px 12px;
        font-size: 0.85rem;
        transition: border-color 0.3s ease;
        width: 200px;
    }

    .search-box:focus {
        border-color: var(--primary-color);
        outline: none;
        box-shadow: 0 0 0 0.15rem rgba(78, 115, 223, 0.2);
    }

    .table-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .table-header {
        background: var(--primary-color);
        color: white;
        padding: 10px 15px;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .custom-table {
        margin: 0;
    }

    .custom-table thead th {
        background: var(--light-color);
        color: var(--dark-color);
        font-weight: 600;
        border: none;
        padding: 8px 12px;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        font-size: 0.75rem;
    }

    .custom-table tbody td {
        padding: 8px 12px;
        vertical-align: middle;
        border-color: #e3e6f0;
        font-size: 0.85rem;
    }

    .custom-table tbody tr:hover {
        background: rgba(78, 115, 223, 0.03);
    }

    .status-badge {
        padding: 2px 8px;
        border-radius: 12px;
        font-weight: 600;
        font-size: 0.7rem;
        text-transform: uppercase;
        letter-spacing: 0.3px;
    }

    .status-active {
        background: rgba(28, 200, 138, 0.1);
        color: var(--success-color);
    }

    .status-inactive {
        background: rgba(133, 135, 150, 0.1);
        color: var(--secondary-color);
    }

    .action-btn {
        padding: 4px 8px;
        border-radius: 4px;
        border: none;
        font-weight: 500;
        font-size: 0.75rem;
        transition: all 0.3s ease;
        margin: 0 1px;
    }

    .btn-view {
        background: var(--info-color);
        color: white;
    }

    .btn-view:hover {
        background: #138496;
        color: white;
    }

    .btn-edit {
        background: var(--warning-color);
        color: white;
    }

    .btn-edit:hover {
        background: #dda20a;
        color: white;
    }

    .btn-delete {
        background: var(--danger-color);
        color: white;
    }

    .btn-delete:hover {
        background: #c82333;
        color: white;
    }

    .pagination-container {
        padding: 15px;
        background: white;
        border-top: 1px solid #e3e6f0;
    }

    .alert-custom {
        border-radius: 8px;
        border: none;
        padding: 10px 15px;
        margin-bottom: 15px;
        font-weight: 500;
        font-size: 0.9rem;
    }

    .breadcrumb-container {
        background: white;
        border-radius: 6px;
        padding: 10px 15px;
        margin-bottom: 15px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .breadcrumb {
        margin: 0;
        background: none;
        padding: 0;
        font-size: 0.85rem;
    }

    .breadcrumb-item a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
    }

    .breadcrumb-item a:hover {
        text-decoration: underline;
    }

    .breadcrumb-item.active {
        color: var(--secondary-color);
    }

    .gst-badge {
        background: rgba(78, 115, 223, 0.1);
        color: var(--primary-color);
        padding: 2px 6px;
        border-radius: 10px;
        font-size: 0.7rem;
        font-weight: 600;
    }
</style>
@endpush

@section('content')
<!-- Breadcrumb -->
<div class="breadcrumb-container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="#">Masters</a></li>
            <li class="breadcrumb-item active" aria-current="page">Customers</li>
        </ol>
    </nav>
</div>

<!-- Page Header -->
<div class="page-header">
    <h1><i class="fas fa-user-tie"></i> Customer Master</h1>
    <p>Manage your customer database with contact details and GST information</p>
</div>

<!-- Success/Error Messages -->
@if(session('success'))
    <div class="alert alert-success alert-custom">
        <i class="fas fa-check-circle"></i> {{ session('success') }}
    </div>
@endif

@if(session('error'))
    <div class="alert alert-danger alert-custom">
        <i class="fas fa-exclamation-circle"></i> {{ session('error') }}
    </div>
@endif

<!-- Statistics Cards -->
<div class="row mb-3">
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number">{{ $customers->total() }}</div>
            <div class="stats-label">Total Customers</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number">{{ $customers->where('status', 'active')->count() }}</div>
            <div class="stats-label">Active Customers</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number">{{ $customers->whereNotNull('gst_number')->count() }}</div>
            <div class="stats-label">GST Registered</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number">{{ $customers->whereNull('gst_number')->count() }}</div>
            <div class="stats-label">Non-GST</div>
        </div>
    </div>
</div>

<!-- Action Buttons -->
<div class="action-buttons">
    @permission('customers.create')
    <a href="{{ route('customers.create') }}" class="btn btn-create">
        <i class="fas fa-plus"></i> Add New Customer
    </a>
    @endpermission

    @if(auth()->user()->canAccessAllCompanies() && $accessibleCompanies->count() > 1)
    <select class="form-select" id="companyFilter" style="width: 200px; display: inline-block;">
        <option value="">All Companies</option>
        @foreach($accessibleCompanies as $company)
            <option value="{{ $company->id }}">{{ $company->name }}</option>
        @endforeach
    </select>
    @endif

    <input type="text" class="search-box" placeholder="Search customers..." id="searchInput">
</div>

<!-- Customers Table -->
<div class="table-container">
    <div class="table-header">
        <i class="fas fa-list"></i> Customers List
    </div>
    
    <div class="table-responsive">
        <table class="table custom-table">
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Phone</th>
                    <th>City</th>
                    <th>State</th>
                    @if(auth()->user()->canAccessAllCompanies())
                    <th>Company</th>
                    @endif
                    <th>GST Number</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                @forelse($customers as $customer)
                <tr>
                    <td>
                        <strong>{{ $customer->name }}</strong>
                        @if($customer->address)
                            <br><small class="text-muted">{{ Str::limit($customer->address, 30) }}</small>
                        @endif
                    </td>
                    <td>{{ $customer->email ?? 'N/A' }}</td>
                    <td>{{ $customer->phone }}</td>
                    <td>{{ $customer->city->name ?? 'N/A' }}</td>
                    <td>{{ $customer->state->name ?? 'N/A' }}</td>
                    @if(auth()->user()->canAccessAllCompanies())
                    <td>
                        <span class="badge bg-primary">{{ $customer->company->name ?? 'N/A' }}</span>
                    </td>
                    @endif
                    <td>
                        @if($customer->gst_number)
                            <span class="gst-badge">{{ $customer->gst_number }}</span>
                        @else
                            <span class="text-muted">N/A</span>
                        @endif
                    </td>
                    <td>
                        <span class="status-badge status-{{ $customer->status }}">
                            {{ ucfirst($customer->status) }}
                        </span>
                    </td>
                    <td>
                        @permission('customers.view')
                        <a href="{{ route('customers.show', $customer) }}" class="btn action-btn btn-view" title="View">
                            <i class="fas fa-eye"></i>
                        </a>
                        @endpermission
                         
                        @permission('customers.edit')
                        <a href="{{ route('customers.edit', $customer) }}" class="btn action-btn btn-edit" title="Edit">
                            <i class="fas fa-edit"></i>
                        </a>
                        @endpermission
                        
                        @permission('customers.delete')
                        <form action="{{ route('customers.destroy', $customer) }}" method="POST" style="display: inline;" 
                              onsubmit="return confirm('Are you sure you want to delete this customer?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn action-btn btn-delete" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </form>
                        @endpermission
                           
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="8" class="text-center py-4">
                        <i class="fas fa-user-tie fa-3x text-muted mb-3"></i>
                        @permission('customers.create')
                        <p class="text-muted">No customers found. <a href="{{ route('customers.create') }}">Create your first customer</a></p>
                         @endpermission
                    </td>
                </tr>
                @endforelse
            </tbody>
        </table>
    </div>
    
    @if($customers->hasPages())
    <div class="pagination-container">
        {{ $customers->links('pagination.custom') }}
    </div>
    @endif
</div>
@endsection

@push('scripts')
<script>
    // Search functionality
    document.getElementById('searchInput').addEventListener('keyup', function() {
        const searchTerm = this.value.toLowerCase();
        const tableRows = document.querySelectorAll('.custom-table tbody tr');
        
        tableRows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });
</script>
@endpush

           

@push('scripts')
<script>
    // Search functionality
    document.getElementById('searchInput').addEventListener('keyup', function() {
        const searchTerm = this.value.toLowerCase();
        const tableRows = document.querySelectorAll('.custom-table tbody tr');
        
        tableRows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });
</script>
@endpush
