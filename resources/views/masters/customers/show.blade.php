@extends('layouts.app')

@section('title', 'Customer Details')

@push('styles')
<style>
    .page-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, #224abe 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        margin-bottom: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .page-header h1 {
        font-size: 1.5rem;
        margin-bottom: 5px;
    }

    .page-header p {
        font-size: 0.85rem;
        margin-bottom: 0;
    }

    .details-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .details-header {
        background: var(--primary-color);
        color: white;
        padding: 12px 20px;
        font-weight: 600;
        font-size: 0.95rem;
    }

    .details-body {
        padding: 20px;
    }

    .detail-row {
        display: flex;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid #f1f3f4;
    }

    .detail-row:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .detail-label {
        font-weight: 600;
        color: var(--dark-color);
        width: 150px;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.3px;
    }

    .detail-value {
        flex: 1;
        color: var(--secondary-color);
        font-size: 0.9rem;
    }

    .status-badge {
        padding: 3px 10px;
        border-radius: 12px;
        font-weight: 600;
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.3px;
    }

    .status-active {
        background: rgba(28, 200, 138, 0.1);
        color: var(--success-color);
    }

    .status-inactive {
        background: rgba(133, 135, 150, 0.1);
        color: var(--secondary-color);
    }

    .gst-badge {
        background: rgba(78, 115, 223, 0.1);
        color: var(--primary-color);
        padding: 3px 8px;
        border-radius: 10px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .action-buttons {
        display: flex;
        gap: 10px;
        margin-top: 20px;
        padding-top: 15px;
        border-top: 1px solid #e3e6f0;
    }

    .btn-edit {
        background: var(--warning-color);
        border: none;
        color: white;
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        transition: all 0.3s ease;
        font-size: 0.85rem;
        text-decoration: none;
        display: inline-block;
    }

    .btn-edit:hover {
        background: #dda20a;
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(246, 194, 62, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-back {
        background: var(--secondary-color);
        border: none;
        color: white;
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        transition: all 0.3s ease;
        font-size: 0.85rem;
        text-decoration: none;
        display: inline-block;
    }

    .btn-back:hover {
        background: #6c757d;
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(133, 135, 150, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-delete {
        background: var(--danger-color);
        border: none;
        color: white;
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        transition: all 0.3s ease;
        font-size: 0.85rem;
    }

    .btn-delete:hover {
        background: #c82333;
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(231, 74, 59, 0.4);
        color: white;
    }

    .breadcrumb-container {
        background: white;
        border-radius: 6px;
        padding: 10px 15px;
        margin-bottom: 15px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .breadcrumb {
        margin: 0;
        background: none;
        padding: 0;
        font-size: 0.85rem;
    }

    .breadcrumb-item a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
    }

    .breadcrumb-item a:hover {
        text-decoration: underline;
    }

    .breadcrumb-item.active {
        color: var(--secondary-color);
    }

    .contact-info {
        background: rgba(78, 115, 223, 0.05);
        padding: 10px;
        border-radius: 6px;
        border-left: 3px solid var(--primary-color);
    }

    .contact-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 0.9rem;
    }

    .contact-item:last-child {
        margin-bottom: 0;
    }

    .contact-icon {
        width: 20px;
        color: var(--primary-color);
        margin-right: 8px;
    }
</style>
@endpush

@section('content')
<!-- Breadcrumb -->
<div class="breadcrumb-container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="#">Masters</a></li>
            <li class="breadcrumb-item"><a href="{{ route('customers.index') }}">Customers</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ $customer->name }}</li>
        </ol>
    </nav>
</div>

<!-- Page Header -->
<div class="page-header">
    <h1><i class="fas fa-user-tie"></i> Customer Details</h1>
    <p>View complete information for {{ $customer->name }}</p>
</div>

<!-- Customer Details -->
<div class="details-container">
    <div class="details-header">
        <i class="fas fa-info-circle"></i> Customer Information
    </div>
    
    <div class="details-body">
        <div class="row">
            <div class="col-md-8">
                <div class="detail-row">
                    <div class="detail-label">Customer Name:</div>
                    <div class="detail-value"><strong>{{ $customer->name }}</strong></div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">Email:</div>
                    <div class="detail-value">{{ $customer->email ?? 'N/A' }}</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">Phone:</div>
                    <div class="detail-value">{{ $customer->phone }}</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">GST Number:</div>
                    <div class="detail-value">
                        @if($customer->gst_number)
                            <span class="gst-badge">{{ $customer->gst_number }}</span>
                        @else
                            <span class="text-muted">Not GST Registered</span>
                        @endif
                    </div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">Address:</div>
                    <div class="detail-value">{{ $customer->address }}</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">City:</div>
                    <div class="detail-value">{{ $customer->city->name ?? 'N/A' }}</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">State:</div>
                    <div class="detail-value">{{ $customer->state->name ?? 'N/A' }}</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">Pincode:</div>
                    <div class="detail-value">{{ $customer->pincode }}</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">Status:</div>
                    <div class="detail-value">
                        <span class="status-badge status-{{ $customer->status }}">
                            {{ ucfirst($customer->status) }}
                        </span>
                    </div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">Created:</div>
                    <div class="detail-value">{{ $customer->created_at->format('d M Y, h:i A') }}</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">Last Updated:</div>
                    <div class="detail-value">{{ $customer->updated_at->format('d M Y, h:i A') }}</div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="contact-info">
                    <h6 class="mb-3"><i class="fas fa-address-card"></i> Quick Contact</h6>
                    
                    @if($customer->phone)
                    <div class="contact-item">
                        <i class="fas fa-phone contact-icon"></i>
                        <span>{{ $customer->phone }}</span>
                    </div>
                    @endif
                    
                    @if($customer->email)
                    <div class="contact-item">
                        <i class="fas fa-envelope contact-icon"></i>
                        <span>{{ $customer->email }}</span>
                    </div>
                    @endif
                    
                    <div class="contact-item">
                        <i class="fas fa-map-marker-alt contact-icon"></i>
                        <span>{{ $customer->full_address }}</span>
                    </div>
                    
                    @if($customer->gst_number)
                    <div class="contact-item">
                        <i class="fas fa-file-invoice contact-icon"></i>
                        <span>GST: {{ $customer->gst_number }}</span>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="action-buttons">
            <a href="{{ route('customers.index') }}" class="btn btn-back">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
            <a href="{{ route('customers.edit', $customer) }}" class="btn btn-edit">
                <i class="fas fa-edit"></i> Edit Customer
            </a>
            <form action="{{ route('customers.destroy', $customer) }}" method="POST" style="display: inline;" 
                  onsubmit="return confirm('Are you sure you want to delete this customer?')">
                @csrf
                @method('DELETE')
                <button type="submit" class="btn btn-delete">
                    <i class="fas fa-trash"></i> Delete Customer
                </button>
            </form>
        </div>
    </div>
</div>
@endsection
