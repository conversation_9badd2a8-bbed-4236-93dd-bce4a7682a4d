@extends('layouts.app')

@section('title', 'Edit Customer')

@push('styles')
<style>
    .page-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, #224abe 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        margin-bottom: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .page-header h1 {
        font-size: 1.5rem;
        margin-bottom: 5px;
    }

    .page-header p {
        font-size: 0.85rem;
        margin-bottom: 0;
    }

    .form-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .form-header {
        background: var(--primary-color);
        color: white;
        padding: 12px 20px;
        font-weight: 600;
        font-size: 0.95rem;
    }

    .form-body {
        padding: 20px;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .form-label {
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 5px;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.3px;
    }

    .form-control {
        border: 1px solid #e3e6f0;
        border-radius: 6px;
        padding: 8px 12px;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        background: #f8f9fc;
    }

    .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.15rem rgba(78, 115, 223, 0.2);
        background: white;
        outline: none;
    }

    .form-select {
        border: 1px solid #e3e6f0;
        border-radius: 6px;
        padding: 8px 12px;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        background: #f8f9fc;
    }

    .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.15rem rgba(78, 115, 223, 0.2);
        background: white;
        outline: none;
    }

    .btn-submit {
        background: var(--primary-color);
        border: none;
        color: white;
        padding: 8px 20px;
        border-radius: 6px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        transition: all 0.3s ease;
        font-size: 0.85rem;
    }

    .btn-submit:hover {
        background: #224abe;
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(78, 115, 223, 0.4);
        color: white;
    }

    .btn-cancel {
        background: var(--secondary-color);
        border: none;
        color: white;
        padding: 8px 20px;
        border-radius: 6px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        transition: all 0.3s ease;
        font-size: 0.85rem;
        text-decoration: none;
        display: inline-block;
    }

    .btn-cancel:hover {
        background: #6c757d;
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(133, 135, 150, 0.4);
        color: white;
        text-decoration: none;
    }

    .invalid-feedback {
        display: block;
        color: var(--danger-color);
        font-size: 0.8rem;
        margin-top: 3px;
        font-weight: 500;
    }

    .is-invalid {
        border-color: var(--danger-color) !important;
        box-shadow: 0 0 0 0.15rem rgba(231, 74, 59, 0.2) !important;
    }

    .form-text {
        color: var(--secondary-color);
        font-size: 0.8rem;
        margin-top: 3px;
    }

    .required {
        color: var(--danger-color);
    }

    .button-group {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
        margin-top: 20px;
        padding-top: 15px;
        border-top: 1px solid #e3e6f0;
    }

    .breadcrumb-container {
        background: white;
        border-radius: 6px;
        padding: 10px 15px;
        margin-bottom: 15px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .breadcrumb {
        margin: 0;
        background: none;
        padding: 0;
        font-size: 0.85rem;
    }

    .breadcrumb-item a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
    }

    .breadcrumb-item a:hover {
        text-decoration: underline;
    }

    .breadcrumb-item.active {
        color: var(--secondary-color);
    }

    .row .col-md-6 {
        padding-left: 7.5px;
        padding-right: 7.5px;
    }
</style>
@endpush

@section('content')
<!-- Breadcrumb -->
<div class="breadcrumb-container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="#">Masters</a></li>
            <li class="breadcrumb-item"><a href="{{ route('customers.index') }}">Customers</a></li>
            <li class="breadcrumb-item active" aria-current="page">Edit {{ $customer->name }}</li>
        </ol>
    </nav>
</div>

<!-- Page Header -->
<div class="page-header">
    <h1><i class="fas fa-edit"></i> Edit Customer</h1>
    <p>Update customer information for {{ $customer->name }}</p>
</div>

<!-- Customer Form -->
<div class="form-container">
    <div class="form-header">
        <i class="fas fa-user-tie"></i> Customer Information
    </div>
    
    <div class="form-body">
        <form action="{{ route('customers.update', $customer) }}" method="POST">
            @csrf
            @method('PUT')
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="name" class="form-label">Customer Name <span class="required">*</span></label>
                        <input type="text" class="form-control @error('name') is-invalid @enderror" 
                               id="name" name="name" value="{{ old('name', $customer->name) }}" required>
                        @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" class="form-control @error('email') is-invalid @enderror" 
                               id="email" name="email" value="{{ old('email', $customer->email) }}">
                        @error('email')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="phone" class="form-label">Phone Number <span class="required">*</span></label>
                        <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                               id="phone" name="phone" value="{{ old('phone', $customer->phone) }}" required>
                        @error('phone')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="gst_number" class="form-label">GST Number</label>
                        <input type="text" class="form-control @error('gst_number') is-invalid @enderror" 
                               id="gst_number" name="gst_number" value="{{ old('gst_number', $customer->gst_number) }}">
                        <div class="form-text">15-digit GST registration number</div>
                        @error('gst_number')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <label for="address" class="form-label">Address <span class="required">*</span></label>
                        <textarea class="form-control @error('address') is-invalid @enderror" 
                                  id="address" name="address" rows="3" required>{{ old('address', $customer->address) }}</textarea>
                        @error('address')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="state_id" class="form-label">State <span class="required">*</span></label>
                        <select class="form-select @error('state_id') is-invalid @enderror" 
                                id="state_id" name="state_id" required>
                            <option value="">Select State</option>
                            @foreach($states as $state)
                                <option value="{{ $state->id }}" {{ old('state_id', $customer->state_id) == $state->id ? 'selected' : '' }}>
                                    {{ $state->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('state_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="city_id" class="form-label">City <span class="required">*</span></label>
                        <select class="form-select @error('city_id') is-invalid @enderror" 
                                id="city_id" name="city_id" required>
                            <option value="">Select City</option>
                            @foreach($cities as $city)
                                <option value="{{ $city->id }}" {{ old('city_id', $customer->city_id) == $city->id ? 'selected' : '' }}>
                                    {{ $city->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('city_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="pincode" class="form-label">Pincode <span class="required">*</span></label>
                        <input type="text" class="form-control @error('pincode') is-invalid @enderror" 
                               id="pincode" name="pincode" value="{{ old('pincode', $customer->pincode) }}" required>
                        @error('pincode')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="status" class="form-label">Status <span class="required">*</span></label>
                        <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
                            <option value="active" {{ old('status', $customer->status) == 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ old('status', $customer->status) == 'inactive' ? 'selected' : '' }}>Inactive</option>
                        </select>
                        @error('status')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <div class="button-group">
                <a href="{{ route('customers.show', $customer) }}" class="btn btn-cancel">
                    <i class="fas fa-times"></i> Cancel
                </a>
                <button type="submit" class="btn btn-submit">
                    <i class="fas fa-save"></i> Update Customer
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // Load cities based on selected state
    document.getElementById('state_id').addEventListener('change', function() {
        const stateId = this.value;
        const citySelect = document.getElementById('city_id');

        // Clear existing options except the first one
        citySelect.innerHTML = '<option value="">Select City</option>';

        if (stateId) {
            // Add loading option
            citySelect.innerHTML = '<option value="">Loading cities...</option>';

            fetch(`/get-cities-by-state/${stateId}`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(cities => {
                    // Clear loading message
                    citySelect.innerHTML = '<option value="">Select City</option>';

                    if (cities && cities.length > 0) {
                        cities.forEach(city => {
                            const option = document.createElement('option');
                            option.value = city.id;
                            option.textContent = city.name;
                            citySelect.appendChild(option);
                        });
                    } else {
                        citySelect.innerHTML = '<option value="">No cities found</option>';
                    }
                })
                .catch(error => {
                    console.error('Error loading cities:', error);
                    citySelect.innerHTML = '<option value="">Error loading cities</option>';

                    // Show user-friendly error message
                    alert('Failed to load cities. Please refresh the page and try again.');
                });
        }
    });

    // Format GST number
    document.getElementById('gst_number').addEventListener('input', function() {
        let value = this.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
        if (value.length > 15) {
            value = value.substring(0, 15);
        }
        this.value = value;
    });

    // Format phone number
    document.getElementById('phone').addEventListener('input', function() {
        let value = this.value.replace(/[^0-9]/g, '');
        if (value.length > 10) {
            value = value.substring(0, 10);
        }
        this.value = value;
    });

    // Format pincode
    document.getElementById('pincode').addEventListener('input', function() {
        let value = this.value.replace(/[^0-9]/g, '');
        if (value.length > 6) {
            value = value.substring(0, 6);
        }
        this.value = value;
    });

    // Load cities on page load if state is already selected
    document.addEventListener('DOMContentLoaded', function() {
        const stateSelect = document.getElementById('state_id');
        const citySelect = document.getElementById('city_id');
        const selectedCityId = '{{ old('city_id', $customer->city_id) }}';

        if (stateSelect.value && citySelect.children.length <= 1) {
            // If state is selected but cities aren't loaded, load them
            const stateId = stateSelect.value;

            fetch(`/get-cities-by-state/${stateId}`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(cities => {
                    citySelect.innerHTML = '<option value="">Select City</option>';

                    if (cities && cities.length > 0) {
                        cities.forEach(city => {
                            const option = document.createElement('option');
                            option.value = city.id;
                            option.textContent = city.name;
                            if (city.id == selectedCityId) {
                                option.selected = true;
                            }
                            citySelect.appendChild(option);
                        });
                    } else {
                        citySelect.innerHTML = '<option value="">No cities found</option>';
                    }
                })
                .catch(error => {
                    console.error('Error loading cities:', error);
                    citySelect.innerHTML = '<option value="">Error loading cities</option>';
                });
        }
    });
</script>
@endpush
