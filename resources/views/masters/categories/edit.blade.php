@extends('layouts.app')

@section('title', 'Edit Group - JMD Traders')
@section('page-title', 'Edit Group')

@push('styles')
<style>
    .page-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, #224abe 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        margin-bottom: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .page-header h1 {
        font-size: 1.5rem;
        margin-bottom: 5px;
    }

    .page-header p {
        font-size: 0.85rem;
        margin-bottom: 0;
    }

    .form-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .form-header {
        background: var(--warning-color);
        color: white;
        padding: 12px 20px;
        font-weight: 600;
        font-size: 0.95rem;
    }

    .form-body {
        padding: 20px;
    }

    .form-group {
        margin-bottom: 25px;
    }

    .form-label {
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 8px;
        font-size: 0.95rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .form-control {
        border: 2px solid #e3e6f0;
        border-radius: 10px;
        padding: 15px 20px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: #f8f9fc;
    }

    .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
        background: white;
        outline: none;
    }

    .form-select {
        border: 2px solid #e3e6f0;
        border-radius: 10px;
        padding: 15px 20px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: #f8f9fc;
    }

    .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
        background: white;
        outline: none;
    }

    .btn-update {
        background: var(--warning-color);
        border: none;
        color: white;
        padding: 15px 30px;
        border-radius: 10px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        font-size: 1rem;
    }

    .btn-update:hover {
        background: #dda20a;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(246, 194, 62, 0.4);
        color: white;
    }

    .btn-cancel {
        background: var(--secondary-color);
        border: none;
        color: white;
        padding: 15px 30px;
        border-radius: 10px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        font-size: 1rem;
        text-decoration: none;
        display: inline-block;
    }

    .btn-cancel:hover {
        background: #6c757d;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(133, 135, 150, 0.4);
        color: white;
        text-decoration: none;
    }

    .invalid-feedback {
        display: block;
        color: var(--danger-color);
        font-size: 0.875rem;
        margin-top: 5px;
        font-weight: 500;
    }

    .is-invalid {
        border-color: var(--danger-color) !important;
        box-shadow: 0 0 0 0.2rem rgba(231, 74, 59, 0.25) !important;
    }

    .form-text {
        color: var(--secondary-color);
        font-size: 0.875rem;
        margin-top: 5px;
    }

    .required {
        color: var(--danger-color);
    }

    .button-group {
        display: flex;
        gap: 15px;
        justify-content: flex-end;
        margin-top: 30px;
        padding-top: 30px;
        border-top: 1px solid #e3e6f0;
    }

    .breadcrumb-container {
        background: white;
        border-radius: 10px;
        padding: 15px 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    .breadcrumb {
        margin: 0;
        background: none;
        padding: 0;
    }

    .breadcrumb-item a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
    }

    .breadcrumb-item a:hover {
        text-decoration: underline;
    }

    .breadcrumb-item.active {
        color: var(--secondary-color);
    }

    .info-card {
        background: rgba(246, 194, 62, 0.1);
        border: 1px solid var(--warning-color);
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 25px;
    }

    .info-card .info-text {
        color: var(--warning-color);
        font-weight: 500;
        margin: 0;
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Breadcrumb -->
    <div class="breadcrumb-container">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="{{ route('categories.index') }}">Categories</a></li>
                <li class="breadcrumb-item active">Edit Group</li>
            </ol>
        </nav>
    </div>

    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-0"><i class="fas fa-edit me-3"></i>Edit Group</h1>
                <p class="mb-0 mt-2 opacity-75">Update Group information and settings</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{{ route('categories.index') }}" class="btn btn-light">
                    <i class="fas fa-arrow-left me-2"></i>Back to Categories
                </a>
            </div>
        </div>
    </div>

    <!-- Info Card -->
    <div class="info-card">
        <p class="info-text">
            <i class="fas fa-info-circle me-2"></i>
            You are editing: <strong>{{ $category->name }}</strong> (ID: #{{ $category->id }})
        </p>
    </div>

    <!-- Form Container -->
    <div class="form-container">
        <div class="form-header">
            <i class="fas fa-edit me-2"></i>Update Group Information
        </div>
        
        <div class="form-body">
            <form action="{{ route('categories.update', $category) }}" method="POST">
                @csrf
                @method('PUT')
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="name" class="form-label">
                                Group Name <span class="required">*</span>
                            </label>
                            <input type="text" 
                                   class="form-control @error('name') is-invalid @enderror" 
                                   id="name" 
                                   name="name" 
                                   value="{{ old('name', $category->name) }}" 
                                   placeholder="Enter Group name"
                                   required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Enter a unique name for the category</div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="status" class="form-label">
                                Status <span class="required">*</span>
                            </label>
                            <select class="form-select @error('status') is-invalid @enderror" 
                                    id="status" 
                                    name="status" 
                                    required>
                                <option value="">Select Status</option>
                                <option value="active" {{ old('status', $category->status) == 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ old('status', $category->status) == 'inactive' ? 'selected' : '' }}>Inactive</option>
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Set the Group status</div>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="description" class="form-label">Description</label>
                    <textarea class="form-control @error('description') is-invalid @enderror" 
                              id="description" 
                              name="description" 
                              rows="4" 
                              placeholder="Enter Group description (optional)">{{ old('description', $category->description) }}</textarea>
                    @error('description')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    <div class="form-text">Provide a detailed description of the Group</div>
                </div>

                <div class="button-group">
                    <a href="{{ route('categories.index') }}" class="btn-cancel">
                        <i class="fas fa-times me-2"></i>Cancel
                    </a>
                    <button type="submit" class="btn-update">
                        <i class="fas fa-save me-2"></i>Update Group
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
    // Auto-focus on first input
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('name').focus();
    });

    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        const name = document.getElementById('name').value.trim();
        const status = document.getElementById('status').value;
        
        if (!name) {
            e.preventDefault();
            alert('Please enter a Group name');
            document.getElementById('name').focus();
            return;
        }
        
        if (!status) {
            e.preventDefault();
            alert('Please select a status');
            document.getElementById('status').focus();
            return;
        }
    });
</script>
@endpush
@endsection
