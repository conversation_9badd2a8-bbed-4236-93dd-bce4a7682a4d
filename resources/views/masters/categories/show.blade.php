@extends('layouts.app')

@section('title', 'View Group - JMD Traders')
@section('page-title', 'Group Details')

@push('styles')
<style>
    .page-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, #224abe 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        margin-bottom: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .page-header h1 {
        font-size: 1.5rem;
        margin-bottom: 5px;
    }

    .page-header p {
        font-size: 0.85rem;
        margin-bottom: 0;
    }

    .detail-card {
        background: white;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        margin-bottom: 15px;
    }

    .detail-header {
        background: var(--info-color);
        color: white;
        padding: 12px 20px;
        font-weight: 600;
        font-size: 0.95rem;
    }

    .detail-body {
        padding: 20px;
    }

    .detail-row {
        display: flex;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #e3e6f0;
    }

    .detail-row:last-child {
        border-bottom: none;
    }

    .detail-label {
        font-weight: 600;
        color: var(--dark-color);
        width: 150px;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        font-size: 0.8rem;
    }

    .detail-value {
        color: var(--secondary-color);
        font-size: 0.9rem;
    }

    .status-badge {
        padding: 4px 10px;
        border-radius: 15px;
        font-weight: 600;
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.3px;
    }

    .status-active {
        background: rgba(28, 200, 138, 0.1);
        color: var(--success-color);
    }

    .status-inactive {
        background: rgba(133, 135, 150, 0.1);
        color: var(--secondary-color);
    }

    .action-buttons {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
    }

    .btn-edit {
        background: var(--warning-color);
        border: none;
        color: white;
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        transition: all 0.3s ease;
        text-decoration: none;
        font-size: 0.85rem;
    }

    .btn-edit:hover {
        background: #dda20a;
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(246, 194, 62, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-back {
        background: var(--secondary-color);
        border: none;
        color: white;
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        transition: all 0.3s ease;
        text-decoration: none;
        font-size: 0.85rem;
    }

    .btn-back:hover {
        background: #6c757d;
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(133, 135, 150, 0.4);
        color: white;
        text-decoration: none;
    }

    .breadcrumb-container {
        background: white;
        border-radius: 10px;
        padding: 15px 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    .breadcrumb {
        margin: 0;
        background: none;
        padding: 0;
    }

    .breadcrumb-item a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
    }

    .breadcrumb-item a:hover {
        text-decoration: underline;
    }

    .breadcrumb-item.active {
        color: var(--secondary-color);
    }

    .products-table {
        background: white;
        border-radius: 15px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .table-header {
        background: var(--success-color);
        color: white;
        padding: 20px 25px;
        font-weight: 600;
        font-size: 1.1rem;
    }

    .custom-table {
        margin: 0;
    }

    .custom-table thead th {
        background: var(--light-color);
        color: var(--dark-color);
        font-weight: 600;
        border: none;
        padding: 15px 20px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 0.85rem;
    }

    .custom-table tbody td {
        padding: 20px;
        vertical-align: middle;
        border-color: #e3e6f0;
    }

    .custom-table tbody tr:hover {
        background: rgba(78, 115, 223, 0.05);
    }

    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: var(--secondary-color);
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }

    .empty-state h5 {
        margin-bottom: 10px;
        color: var(--dark-color);
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Breadcrumb -->
    <div class="breadcrumb-container">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="{{ route('categories.index') }}">Categories</a></li>
                <li class="breadcrumb-item active">{{ $category->name }}</li>
            </ol>
        </nav>
    </div>

    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-0"><i class="fas fa-eye me-3"></i>Category Details</h1>
                <p class="mb-0 mt-2 opacity-75">View complete information about this category</p>
            </div>
            <div class="col-md-4 text-end">
                <span class="status-badge status-{{ $category->status }}">
                    {{ ucfirst($category->status) }}
                </span>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
        <a href="{{ route('categories.index') }}" class="btn-back">
            <i class="fas fa-arrow-left me-2"></i>Back to List
        </a>
        <a href="{{ route('categories.edit', $category) }}" class="btn-edit">
            <i class="fas fa-edit me-2"></i>Edit Category
        </a>
    </div>

    <!-- Category Details -->
    <div class="detail-card">
        <div class="detail-header">
            <i class="fas fa-info-circle me-2"></i>Group Information
        </div>
        
        <div class="detail-body">
            <div class="detail-row">
                <div class="detail-label">Group ID:</div>
                <div class="detail-value"><strong>#{{ $category->id }}</strong></div>
            </div>
            
            <div class="detail-row">
                <div class="detail-label">Category Name:</div>
                <div class="detail-value"><strong>{{ $category->name }}</strong></div>
            </div>
            
            <div class="detail-row">
                <div class="detail-label">Description:</div>
                <div class="detail-value">
                    {{ $category->description ?: 'No description provided' }}
                </div>
            </div>
            
            <div class="detail-row">
                <div class="detail-label">Status:</div>
                <div class="detail-value">
                    <span class="status-badge status-{{ $category->status }}">
                        {{ ucfirst($category->status) }}
                    </span>
                </div>
            </div>
            
            <div class="detail-row">
                <div class="detail-label">Total Products:</div>
                <div class="detail-value">
                    <span class="badge bg-info">{{ $category->products->count() }} Products</span>
                </div>
            </div>
            
            <div class="detail-row">
                <div class="detail-label">Created Date:</div>
                <div class="detail-value">{{ $category->created_at->format('d M Y, h:i A') }}</div>
            </div>
            
            <div class="detail-row">
                <div class="detail-label">Last Updated:</div>
                <div class="detail-value">{{ $category->updated_at->format('d M Y, h:i A') }}</div>
            </div>
        </div>
    </div>

    <!-- Associated Products -->
    <div class="products-table">
        <div class="table-header">
            <i class="fas fa-box me-2"></i>Associated Products ({{ $category->products->count() }})
        </div>
        
        @if($category->products->count() > 0)
        <div class="table-responsive">
            <table class="table custom-table">
                <thead>
                    <tr>
                        <th>Product Name</th>
                        <th>Price</th>
                        <th>Stock</th>
                        <th>Status</th>
                        <th>Created Date</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($category->products->take(10) as $product)
                    <tr>
                        <td><strong>{{ $product->name }}</strong></td>
                        <td>₹{{ number_format($product->price, 2) }}</td>
                        <td>{{ $product->stock_quantity }}</td>
                        <td>
                            <span class="status-badge status-{{ $product->status }}">
                                {{ ucfirst($product->status) }}
                            </span>
                        </td>
                        <td>{{ $product->created_at->format('d M Y') }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        @else
        <div class="empty-state">
            <i class="fas fa-box-open"></i>
            <h5>No Products Found</h5>
            <p>This category doesn't have any products associated with it yet.</p>
        </div>
        @endif
    </div>
</div>
@endsection
