@extends('layouts.app')

@section('title', 'Group Master - JMD Traders')
@section('page-title', 'Group Master')

@push('styles')
<style>
    .page-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, #224abe 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        margin-bottom: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .page-header h1 {
        font-size: 1.5rem;
        margin-bottom: 5px;
    }

    .page-header p {
        font-size: 0.85rem;
        margin-bottom: 0;
    }

    .stats-card {
        background: white;
        border-radius: 8px;
        padding: 10px 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        border-left: 3px solid var(--primary-color);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    }

    .stats-number {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 2px;
    }

    .stats-label {
        color: var(--secondary-color);
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 0.7rem;
    }

    .action-buttons {
        display: flex;
        gap: 8px;
        margin-bottom: 10px;
        align-items: center;
    }

    .btn-create {
        background: var(--success-color);
        border: none;
        color: white;
        padding: 6px 12px;
        border-radius: 6px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        transition: all 0.3s ease;
        font-size: 0.8rem;
    }

    .btn-create:hover {
        background: #17a673;
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(28, 200, 138, 0.4);
        color: white;
    }

    .search-box {
        background: white;
        border: 1px solid #e3e6f0;
        border-radius: 6px;
        padding: 6px 12px;
        font-size: 0.85rem;
        transition: border-color 0.3s ease;
        width: 200px;
    }

    .search-box:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
        outline: none;
    }

    .table-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .table-header {
        background: var(--primary-color);
        color: white;
        padding: 10px 15px;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .custom-table {
        margin: 0;
    }

    .custom-table thead th {
        background: var(--light-color);
        color: var(--dark-color);
        font-weight: 600;
        border: none;
        padding: 8px 12px;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        font-size: 0.75rem;
    }

    .custom-table tbody td {
        padding: 8px 12px;
        vertical-align: middle;
        border-color: #e3e6f0;
        font-size: 0.85rem;
    }

    .custom-table tbody tr:hover {
        background: rgba(78, 115, 223, 0.03);
    }

    .status-badge {
        padding: 2px 8px;
        border-radius: 12px;
        font-weight: 600;
        font-size: 0.7rem;
        text-transform: uppercase;
        letter-spacing: 0.3px;
    }

    .status-active {
        background: rgba(28, 200, 138, 0.1);
        color: var(--success-color);
    }

    .status-inactive {
        background: rgba(133, 135, 150, 0.1);
        color: var(--secondary-color);
    }

    .action-btn {
        padding: 4px 8px;
        border-radius: 4px;
        border: none;
        font-weight: 500;
        font-size: 0.75rem;
        transition: all 0.3s ease;
        margin: 0 1px;
    }

    .btn-view {
        background: var(--info-color);
        color: white;
    }

    .btn-view:hover {
        background: #2c9faf;
        transform: translateY(-1px);
        color: white;
    }

    .btn-edit {
        background: var(--warning-color);
        color: white;
    }

    .btn-edit:hover {
        background: #dda20a;
        transform: translateY(-1px);
        color: white;
    }

    .btn-delete {
        background: var(--danger-color);
        color: white;
    }

    .btn-delete:hover {
        background: #c0392b;
        transform: translateY(-1px);
        color: white;
    }

    .pagination-container {
        padding: 15px;
        background: white;
        border-top: 1px solid #e3e6f0;
    }

    .alert-custom {
        border-radius: 8px;
        border: none;
        padding: 10px 15px;
        margin-bottom: 15px;
        font-weight: 500;
        font-size: 0.9rem;
    }

    .alert-success {
        background: rgba(28, 200, 138, 0.1);
        color: var(--success-color);
        border-left: 4px solid var(--success-color);
    }

    .alert-error {
        background: rgba(231, 74, 59, 0.1);
        color: var(--danger-color);
        border-left: 4px solid var(--danger-color);
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="mb-0"><i class="fas fa-tags me-3"></i>Group Master</h1>
                <p class="mb-0 mt-2 opacity-75">Manage product Group and classifications</p>
            </div>
            <div class="col-md-6 text-end">
                <div class="stats-card d-inline-block">
                    <div class="stats-number">{{ $categories->total() }}</div>
                    <div class="stats-label">Total Group</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="alert alert-custom alert-success">
            <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-custom alert-error">
            <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
        </div>
    @endif

    <!-- Action Buttons -->
    <div class="action-buttons">
        @permission('categories.create')
        <a href="{{ route('categories.create') }}" class="btn btn-create">
            <i class="fas fa-plus me-2"></i>Add New Group
        </a>
        @endpermission
        <input type="text" class="search-box" placeholder="Search categories..." id="searchInput">
    </div>

    <!-- Categories Table -->
    <div class="table-container">
        <div class="table-header">
            <i class="fas fa-list me-2"></i>Categories List
        </div>
        
        <div class="table-responsive">
            <table class="table custom-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Group Name</th>
                        <th>Description</th>
                        <th>Products</th>
                        <th>Status</th>
                        <th>Created Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($categories as $category)
                    <tr>
                        <td><strong>#{{ $category->id }}</strong></td>
                        <td>
                            <div class="fw-bold text-dark">{{ $category->name }}</div>
                        </td>
                        <td>
                            <span class="text-muted">
                                {{ $category->description ? Str::limit($category->description, 50) : 'No description' }}
                            </span>
                        </td>
                        <td>
                            <span class="badge bg-info">{{ $category->products_count }} Products</span>
                        </td>
                        <td>
                            <span class="status-badge status-{{ $category->status }}">
                                {{ ucfirst($category->status) }}
                            </span>
                        </td>
                        <td>{{ $category->created_at->format('d M Y') }}</td>
                        <td>
                            @permission('categories.view')
                            <a href="{{ route('categories.show', $category) }}" class="action-btn btn-view" title="View">
                                <i class="fas fa-eye"></i>
                            </a>
                            @endpermission

                            @permission('categories.edit')
                            <a href="{{ route('categories.edit', $category) }}" class="action-btn btn-edit" title="Edit">
                                <i class="fas fa-edit"></i>
                            </a>
                            @endpermission

                            @permission('categories.delete')
                            <form action="{{ route('categories.destroy', $category) }}" method="POST" class="d-inline"
                                  onsubmit="return confirm('Are you sure you want to delete this category?')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="action-btn btn-delete" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                            @endpermission
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="7" class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No categories found</h5>
                            <p class="text-muted">Start by creating your first Group</p>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        @if($categories->hasPages())
        <div class="pagination-container">
            {{ $categories->links('pagination.custom') }}
        </div>
        @endif
    </div>
</div>

@push('scripts')
<script>
    // Search functionality
    document.getElementById('searchInput').addEventListener('keyup', function() {
        const searchTerm = this.value.toLowerCase();
        const tableRows = document.querySelectorAll('.custom-table tbody tr');
        
        tableRows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });
</script>
@endpush
@endsection
