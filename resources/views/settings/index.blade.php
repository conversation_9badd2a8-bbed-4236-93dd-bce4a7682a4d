@extends('layouts.app')

@section('title', 'Settings - JMD Traders')
@section('page-title', 'Settings')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Settings</h1>
    </div>

    <div class="row">
        <!-- Application Settings -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-cog"></i> Application Settings
                    </h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <i class="fas fa-building text-info"></i> Company Profile
                                </h6>
                                <small>Manage company information</small>
                            </div>
                            <p class="mb-1">Update company details, logo, and contact information.</p>
                        </a>
                        
                        <a href="#" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <i class="fas fa-file-invoice text-success"></i> Invoice Settings
                                </h6>
                                <small>Configure invoicing</small>
                            </div>
                            <p class="mb-1">Set invoice templates, numbering, and default terms.</p>
                        </a>
                        
                        <a href="#" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <i class="fas fa-percent text-warning"></i> Tax Settings
                                </h6>
                                <small>Manage tax rates</small>
                            </div>
                            <p class="mb-1">Configure GST rates, TCS, and other tax settings.</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Settings -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-user-cog"></i> User Settings
                    </h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <i class="fas fa-user-edit text-primary"></i> Profile Settings
                                </h6>
                                <small>Update your profile</small>
                            </div>
                            <p class="mb-1">Change your name, email, and profile picture.</p>
                        </a>
                        
                        <a href="#" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <i class="fas fa-key text-danger"></i> Change Password
                                </h6>
                                <small>Security settings</small>
                            </div>
                            <p class="mb-1">Update your account password for security.</p>
                        </a>
                        
                        <a href="#" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <i class="fas fa-bell text-info"></i> Notifications
                                </h6>
                                <small>Notification preferences</small>
                            </div>
                            <p class="mb-1">Configure email and system notifications.</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- System Settings -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-server"></i> System Settings
                    </h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <i class="fas fa-database text-secondary"></i> Backup & Restore
                                </h6>
                                <small>Data management</small>
                            </div>
                            <p class="mb-1">Create backups and restore system data.</p>
                        </a>
                        
                        <a href="#" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <i class="fas fa-download text-success"></i> Export Data
                                </h6>
                                <small>Data export</small>
                            </div>
                            <p class="mb-1">Export customers, products, and sales data.</p>
                        </a>
                        
                        <a href="#" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <i class="fas fa-upload text-primary"></i> Import Data
                                </h6>
                                <small>Data import</small>
                            </div>
                            <p class="mb-1">Import data from Excel or CSV files.</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Integration Settings -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-plug"></i> Integrations
                    </h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <i class="fab fa-whatsapp text-success"></i> WhatsApp Integration
                                </h6>
                                <small>Messaging</small>
                            </div>
                            <p class="mb-1">Configure WhatsApp for sending invoices and updates.</p>
                        </a>
                        
                        <a href="#" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <i class="fas fa-envelope text-primary"></i> Email Settings
                                </h6>
                                <small>Email configuration</small>
                            </div>
                            <p class="mb-1">Configure SMTP settings for email notifications.</p>
                        </a>
                        
                        <a href="#" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <i class="fas fa-credit-card text-warning"></i> Payment Gateway
                                </h6>
                                <small>Payment integration</small>
                            </div>
                            <p class="mb-1">Setup payment gateways for online payments.</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt"></i> Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <button class="btn btn-outline-primary btn-block">
                                <i class="fas fa-sync-alt"></i><br>
                                Clear Cache
                            </button>
                        </div>
                        <div class="col-md-3 mb-3">
                            <button class="btn btn-outline-success btn-block">
                                <i class="fas fa-download"></i><br>
                                Export All Data
                            </button>
                        </div>
                        <div class="col-md-3 mb-3">
                            <button class="btn btn-outline-warning btn-block">
                                <i class="fas fa-tools"></i><br>
                                System Maintenance
                            </button>
                        </div>
                        <div class="col-md-3 mb-3">
                            <button class="btn btn-outline-info btn-block">
                                <i class="fas fa-question-circle"></i><br>
                                Help & Support
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
.list-group-item-action:hover {
    background-color: #f8f9fc;
}

.card {
    border: none;
    border-radius: 0.35rem;
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

.btn-block {
    padding: 1rem;
    height: auto;
}

.btn-block i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}
</style>
@endpush
@endsection
