@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Company Management</h3>
                    
                      
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Logo</th>
                                    <th>Company Name</th>
                                    <th>Email</th>
                                    <th>Phone</th>
                                    <th>City</th>
                                    <th>Status</th>
                                    <th>Users</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($companies as $company)
                                    <tr>
                                        <td>{{ $company->id }}</td>
                                        <td>
                                            @if($company->logo)
                                                <img src="{{ asset('storage/' . $company->logo) }}" 
                                                     alt="{{ $company->name }}" 
                                                     class="img-thumbnail" 
                                                     style="width: 50px; height: 50px;">
                                            @else
                                                <div class="bg-secondary text-white d-flex align-items-center justify-content-center" 
                                                     style="width: 50px; height: 50px; border-radius: 4px;">
                                                    {{ strtoupper(substr($company->name, 0, 2)) }}
                                                </div>
                                            @endif
                                        </td>
                                        <td>
                                            <strong>{{ $company->name }}</strong>
                                            @if($company->website)
                                                <br><small><a href="{{ $company->website }}" target="_blank">{{ $company->website }}</a></small>
                                            @endif
                                        </td>
                                        <td>{{ $company->email }}</td>
                                        <td>{{ $company->phone }}</td>
                                        <td>
                                            {{ $company->city }}
                                            @if($company->state)
                                                <br><small>{{ $company->state }}, {{ $company->country }}</small>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge {{ $company->is_active ? 'bg-success' : 'bg-danger' }}">
                                                {{ $company->is_active ? 'Active' : 'Inactive' }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ $company->users_count }} users</span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                @permission('companies.view')
                                                <a href="{{ route('companies.show', $company) }}" 
                                                   class="btn btn-sm btn-info" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                @endpermission

                                                @permission('companies.edit')
                                                <a href="{{ route('companies.edit', $company) }}" 
                                                   class="btn btn-sm btn-warning" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                @endpermission

                                                @if($company->users_count == 0)
                                                    @permission('companies.delete')
                                                    <form action="{{ route('companies.destroy', $company) }}" 
                                                          method="POST" 
                                                          style="display: inline;"
                                                          onsubmit="return confirm('Are you sure you want to delete this company?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-danger" title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                    @endpermission
                                                      
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="9" class="text-center">No companies found.</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    @if($companies->hasPages())
                        <div class="d-flex justify-content-center">
                            {{ $companies->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
