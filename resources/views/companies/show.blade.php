@extends('layouts.app')

@section('title', 'Company Details - ' . $company->name)

@push('styles')
<style>
    .company-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .stat-card {
        background: #f8f9fa;
        border-left: 4px solid #007bff;
        padding: 15px;
        border-radius: 5px;
    }

    .quotation-preview {
        background: #e3f2fd;
        border: 2px dashed #2196f3;
        padding: 15px;
        border-radius: 8px;
        text-align: center;
    }

    .info-badge {
        font-size: 0.9em;
        padding: 0.5em 0.8em;
    }

    .table-borderless td {
        padding: 0.5rem 0.75rem;
        border: none;
    }

    .btn-xs {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Company Header -->
            <div class="company-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        @if($company->logo)
                            <img src="{{ asset('storage/' . $company->logo) }}"
                                 alt="{{ $company->name }}"
                                 class="rounded me-3"
                                 style="width: 60px; height: 60px; object-fit: cover;">
                        @else
                            <div class="bg-white text-primary d-flex align-items-center justify-content-center me-3 rounded"
                                 style="width: 60px; height: 60px; font-size: 1.5rem; font-weight: bold;">
                                {{ strtoupper(substr($company->name, 0, 2)) }}
                            </div>
                        @endif
                        <div>
                            <h2 class="mb-1">{{ $company->name }}</h2>
                            <p class="mb-0 opacity-75">
                                <i class="fas fa-building me-2"></i>{{ $company->code }}
                                @if($company->gst_number)
                                    <span class="ms-3"><i class="fas fa-file-invoice me-2"></i>{{ $company->gst_number }}</span>
                                @endif
                            </p>
                        </div>
                    </div>
                    <div class="text-end">
                        <div class="btn-group" role="group">
                            <a href="{{ route('companies.edit', $company) }}" class="btn btn-light">
                                <i class="fas fa-edit"></i> Edit Company
                            </a>
                            <a href="{{ route('companies.index') }}" class="btn btn-outline-light">
                                <i class="fas fa-arrow-left"></i> Back to List
                            </a>
                        </div>
                        <div class="mt-2">
                            <span class="badge {{ $company->status === 'active' ? 'bg-success' : 'bg-danger' }} info-badge">
                                <i class="fas fa-{{ $company->status === 'active' ? 'check-circle' : 'times-circle' }}"></i>
                                {{ ucfirst($company->status) }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0"><i class="fas fa-info-circle text-primary"></i> Company Information</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5>Basic Information</h5>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Company Name:</strong></td>
                                            <td>{{ $company->name }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Email:</strong></td>
                                            <td>{{ $company->email ?: 'Not provided' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Phone:</strong></td>
                                            <td>{{ $company->phone ?: 'Not provided' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Website:</strong></td>
                                            <td>
                                                @if($company->website)
                                                    <a href="{{ $company->website }}" target="_blank">{{ $company->website }}</a>
                                                @else
                                                    Not provided
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Status:</strong></td>
                                            <td>
                                                <span class="badge {{ $company->is_active ? 'bg-success' : 'bg-danger' }}">
                                                    {{ $company->is_active ? 'Active' : 'Inactive' }}
                                                </span>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h5>Address Information</h5>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Address:</strong></td>
                                            <td>{{ $company->address ?: 'Not provided' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>City:</strong></td>
                                            <td>{{ $company->city ?: 'Not provided' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>State:</strong></td>
                                            <td>{{ $company->state ?: 'Not provided' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Country:</strong></td>
                                            <td>{{ $company->country ?: 'Not provided' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Postal Code:</strong></td>
                                            <td>{{ $company->postal_code ?: 'Not provided' }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <h5>Tax Information</h5>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>GST Number:</strong></td>
                                            <td>{{ $company->gst_number ?: 'Not provided' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>PAN Number:</strong></td>
                                            <td>{{ $company->pan_number ?: 'Not provided' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>CIN:</strong></td>
                                            <td>{{ $company->cin ?: 'Not provided' }}</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h5>Banking Information</h5>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Bank Name:</strong></td>
                                            <td>{{ $company->bankname ?: 'Not provided' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Account Number:</strong></td>
                                            <td>{{ $company->bankaccount ?: 'Not provided' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>IFSC Code:</strong></td>
                                            <td>{{ $company->ifsccode ?: 'Not provided' }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <!-- Quotation Settings Section -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="mb-0"><i class="fas fa-file-invoice text-primary"></i> Quotation Number Settings</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <table class="table table-borderless">
                                                        <tr>
                                                            <td><strong>Quotation Prefix:</strong></td>
                                                            <td>
                                                                <span class="badge bg-info">
                                                                    {{ $company->quotation_prefix ?: (strtoupper($company->code) . '/QT') }}
                                                                </span>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td><strong>Starting Number:</strong></td>
                                                            <td>{{ $company->quotation_start_number ?: 2007 }}</td>
                                                        </tr>
                                                        <tr>
                                                            <td><strong>Current Counter:</strong></td>
                                                            <td>
                                                                <span class="badge bg-success">
                                                                    {{ $company->current_quotation_number ?: $company->quotation_start_number ?: 2007 }}
                                                                </span>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </div>
                                                <div class="col-md-4">
                                                    <table class="table table-borderless">
                                                        <tr>
                                                            <td><strong>Next Quotation:</strong></td>
                                                            <td>
                                                                <div class="quotation-preview">
                                                                    <i class="fas fa-file-invoice text-primary mb-2"></i>
                                                                    <div class="fw-bold text-primary" style="font-size: 1.1em;">
                                                                        {{ $company->getNextQuotationNumber() }}
                                                                    </div>
                                                                    <small class="text-muted">Next auto-generated number</small>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                       
                                                    </table>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="bg-light p-3 rounded">
                                                        <h6 class="text-primary mb-2">
                                                            <i class="fas fa-chart-line"></i> Statistics
                                                        </h6>
                                                        <div class="row text-center">
                                                            <div class="col-6">
                                                                <div class="border-end">
                                                                    <h4 class="text-success mb-0">{{ $company->salesEntries()->count() }}</h4>
                                                                    <small class="text-muted">Total Quotations</small>
                                                                </div>
                                                            </div>
                                                            <div class="col-6">
                                                                <h4 class="text-info mb-0">{{ $company->current_quotation_number - ($company->quotation_start_number ?: 2007) }}</h4>
                                                                <small class="text-muted">Numbers Used</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                           
                        </div>
                        <div class="col-md-4">
                            <h5>Company Logo</h5>
                            <div class="text-center">
                                @if($company->logo)
                                    <img src="{{ asset('storage/' . $company->logo) }}" 
                                         alt="{{ $company->name }}" 
                                         class="img-fluid border rounded" 
                                         style="max-width: 200px; max-height: 200px;">
                                @else
                                    <div class="bg-secondary text-white d-flex align-items-center justify-content-center mx-auto" 
                                         style="width: 150px; height: 150px; border-radius: 8px; font-size: 2rem;">
                                        {{ strtoupper(substr($company->name, 0, 2)) }}
                                    </div>
                                    <p class="text-muted mt-2">No logo uploaded</p>
                                @endif
                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <!-- Recent Quotations Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0"><i class="fas fa-file-alt text-success"></i> Recent Quotations</h5>
                                    <small class="text-muted">Last 5 quotations from this company</small>
                                </div>
                                <div class="card-body">
                                    @if($company->salesEntries()->count() > 0)
                                        <div class="table-responsive">
                                            <table class="table table-sm table-hover">
                                                <thead class="table-light">
                                                    <tr>
                                                        <th>Quotation No.</th>
                                                        <th>Date</th>
                                                        <th>Customer</th>
                                                        <th>Amount</th>
                                                        <th>Status</th>
                                                        <th>Action</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($company->salesEntries()->latest()->take(5)->get() as $entry)
                                                        <tr>
                                                            <td>
                                                                <code class="small">{{ $entry->quotation_number ?: $entry->invoice_number }}</code>
                                                            </td>
                                                            <td>{{ $entry->quotation_date ? $entry->quotation_date->format('M d, Y') : ($entry->invoice_date ? $entry->invoice_date->format('M d, Y') : 'N/A') }}</td>
                                                            <td>{{ $entry->customer->name ?? $entry->party_name ?? 'N/A' }}</td>
                                                            <td>₹{{ number_format($entry->calculated_net_amount, 2) }}</td>
                                                            <td>
                                                                <span class="badge bg-{{ $entry->status === 'approved' ? 'success' : ($entry->status === 'pending' ? 'warning' : 'secondary') }}">
                                                                    {{ ucfirst($entry->status) }}
                                                                </span>
                                                            </td>
                                                            <td>
                                                                <a href="{{ route('entries.sales.show', $entry->id) }}" class="btn btn-xs btn-outline-primary" title="View Details">
                                                                    <i class="fas fa-eye"></i>
                                                                </a>
                                                            </td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                        @if($company->salesEntries()->count() > 5)
                                            <div class="text-center mt-3">
                                                <a href="{{ route('entries.sales') }}?company={{ $company->id }}" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-list"></i> View All Quotations ({{ $company->salesEntries()->count() }} total)
                                                </a>
                                            </div>
                                        @endif
                                    @else
                                        <div class="text-center py-4">
                                            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                                            <p class="text-muted">No quotations created yet for this company.</p>
                                            <a href="{{ route('entries.sales.create') }}" class="btn btn-primary">
                                                <i class="fas fa-plus"></i> Create First Quotation
                                            </a>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <h5>Company Users</h5>
                            @if($company->users->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th>Name</th>
                                                <th>Email</th>
                                                <th>Role in Company</th>
                                                <th>User Role</th>
                                                <th>Status</th>
                                                <th>Joined</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($company->users as $user)
                                                <tr>
                                                    <td>{{ $user->name }}</td>
                                                    <td>{{ $user->email }}</td>
                                                    <td>
                                                        <span class="badge bg-primary">
                                                            {{ ucfirst($user->pivot->role) }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-info">
                                                            {{ ucfirst($user->role) }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span class="badge {{ $user->pivot->is_active ? 'bg-success' : 'bg-danger' }}">
                                                            {{ $user->pivot->is_active ? 'Active' : 'Inactive' }}
                                                        </span>
                                                    </td>
                                                    <td>{{ $user->pivot->created_at ? $user->pivot->created_at->format('M d, Y') : 'N/A' }}</td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i> No users are currently assigned to this company.
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
