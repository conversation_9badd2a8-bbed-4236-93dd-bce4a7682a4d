@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Edit Company: {{ $company->name }}</h3>
                    <div class="card-tools">
                        <a href="{{ route('companies.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Companies
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form action="{{ route('companies.update', $company) }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Company Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror"
                                           id="name" name="name" value="{{ old('name', $company->name) }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="code" class="form-label">Company Code <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('code') is-invalid @enderror"
                                           id="code" name="code" value="{{ old('code', $company->code) }}" required maxlength="10"
                                           placeholder="e.g., COMP001">
                                    @error('code')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">Unique company identifier (max 10 characters)</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email</label>
                                    <input type="email" class="form-control @error('email') is-invalid @enderror"
                                           id="email" name="email" value="{{ old('email', $company->email) }}">
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">Phone</label>
                                    <input type="text" class="form-control @error('phone') is-invalid @enderror"
                                           id="phone" name="phone" value="{{ old('phone', $company->phone) }}">
                                    @error('phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="mobile" class="form-label">Mobile</label>
                                    <input type="text" class="form-control @error('mobile') is-invalid @enderror"
                                           id="mobile" name="mobile" value="{{ old('mobile', $company->mobile) }}">
                                    @error('mobile')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="website" class="form-label">Website</label>
                                    <input type="url" class="form-control @error('website') is-invalid @enderror"
                                           id="website" name="website" value="{{ old('website', $company->website) }}">
                                    @error('website')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control @error('address') is-invalid @enderror" 
                                      id="address" name="address" rows="3">{{ old('address', $company->address) }}</textarea>
                            @error('address')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="city" class="form-label">City</label>
                                    <input type="text" class="form-control @error('city') is-invalid @enderror" 
                                           id="city" name="city" value="{{ old('city', $company->city) }}">
                                    @error('city')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="state" class="form-label">State</label>
                                    <input type="text" class="form-control @error('state') is-invalid @enderror" 
                                           id="state" name="state" value="{{ old('state', $company->state) }}">
                                    @error('state')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="country" class="form-label">Country</label>
                                    <input type="text" class="form-control @error('country') is-invalid @enderror" 
                                           id="country" name="country" value="{{ old('country', $company->country) }}">
                                    @error('country')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="pincode" class="form-label">Postal Code</label>
                                    <input type="text" class="form-control @error('postal_code') is-invalid @enderror" 
                                           id="pincode" name="pincode" value="{{ old('pincode', $company->pincode) }}">
                                    @error('pincode')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="gst_number" class="form-label">GST Number</label>
                                    <input type="text" class="form-control @error('gst_number') is-invalid @enderror" 
                                           id="gst_number" name="gst_number" value="{{ old('gst_number', $company->gst_number) }}">
                                    @error('gst_number')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="pan_number" class="form-label">PAN Number</label>
                                    <input type="text" class="form-control @error('pan_number') is-invalid @enderror" 
                                           id="pan_number" name="pan_number" value="{{ old('pan_number', $company->pan_number) }}">
                                    @error('pan_number')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="cin" class="form-label">CIN</label>
                                    <input type="text" class="form-control @error('cin') is-invalid @enderror" 
                                           id="cin" name="cin" value="{{ old('cin', $company->cin) }}">
                                    @error('cin')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="bankname" class="form-label">Bank Name</label>
                                    <input type="text" class="form-control @error('bankname') is-invalid @enderror" 
                                           id="bankname" name="bankname" value="{{ old('bankname', $company->bankname) }}">
                                    @error('bankname')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="bankaccount" class="form-label">Bank Account</label>
                                    <input type="text" class="form-control @error('bankaccount') is-invalid @enderror" 
                                           id="bankaccount" name="bankaccount" value="{{ old('bankaccount', $company->bankaccount) }}">
                                    @error('bankaccount')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="branch" class="form-label">Branch</label>
                                    <input type="text" class="form-control @error('branch') is-invalid @enderror" 
                                           id="branch" name="branch" value="{{ old('branch', $company->branch) }}">
                                    @error('branch')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="ifsccode" class="form-label">IFSC Code</label>
                                    <input type="text" class="form-control @error('ifsccode') is-invalid @enderror" 
                                           id="ifsccode" name="ifsccode" value="{{ old('ifsccode', $company->ifsccode) }}">
                                    @error('ifsccode')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Quotation Settings Section -->
                        <div class="card mt-4 mb-4">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-file-invoice"></i> Quotation Number Settings</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="quotation_prefix" class="form-label">Quotation Prefix</label>
                                            <input type="text" class="form-control @error('quotation_prefix') is-invalid @enderror"
                                                   id="quotation_prefix" name="quotation_prefix"
                                                   value="{{ old('quotation_prefix', $company->quotation_prefix) }}"
                                                   placeholder="e.g., JMD/QT">
                                            @error('quotation_prefix')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">Leave empty to auto-generate from company code (e.g., {{ strtoupper($company->code) }}/QT)</small>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="quotation_start_number" class="form-label">Starting Number</label>
                                            <input type="number" class="form-control @error('quotation_start_number') is-invalid @enderror"
                                                   id="quotation_start_number" name="quotation_start_number"
                                                   value="{{ old('quotation_start_number', $company->quotation_start_number ?? 2007) }}" min="1">
                                            @error('quotation_start_number')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">Starting number for quotation sequence (default: 2007)</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="current_quotation_number" class="form-label">Current Number</label>
                                            <input type="number" class="form-control @error('current_quotation_number') is-invalid @enderror"
                                                   id="current_quotation_number" name="current_quotation_number"
                                                   value="{{ old('current_quotation_number', $company->current_quotation_number ?? $company->quotation_start_number ?? 2007) }}" min="1">
                                            @error('current_quotation_number')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">Current counter value (next quotation will use this number)</small>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Next Quotation Number</label>
                                            <div class="form-control-plaintext bg-light p-2 rounded">
                                                <span id="quotation_preview">{{ $company->getNextQuotationNumber() }}</span>
                                            </div>
                                            <small class="form-text text-muted">Preview of next quotation number</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="logo" class="form-label">Company Logo</label>
                                    @if($company->logo)
                                        <div class="mb-2">
                                            <img src="{{ asset('storage/' . $company->logo) }}"
                                                 alt="{{ $company->name }}"
                                                 class="img-thumbnail"
                                                 style="width: 100px; height: 100px;">
                                            <p class="small text-muted">Current logo</p>
                                        </div>
                                    @endif
                                    <input type="file" class="form-control @error('logo') is-invalid @enderror"
                                           id="logo" name="logo" accept="image/*">
                                    @error('logo')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">Accepted formats: JPG, PNG, GIF. Max size: 2MB. Leave empty to keep current logo.</small>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="watermark" class="form-label">Company Watermark</label>
                                   
                                    <input type="text" class="form-control @error('watermarkName') is-invalid @enderror"
                                           id="watermarkName" name="watermarkName" value="{{ old('watermarkName', $company->watermarkName) }}">
                                    @error('watermark')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6 d-none">
                                <div class="mb-3">
                                    <label for="watermark" class="form-label">Company Watermark</label>
                                    @if($company->watermark)
                                        <div class="mb-2">
                                            <img src="{{ asset('storage/' . $company->watermark) }}"
                                                 alt="{{ $company->name }} Watermark"
                                                 class="img-thumbnail"
                                                 style="width: 100px; height: 100px;">
                                            <p class="small text-muted">Current watermark</p>
                                        </div>
                                    @endif
                                    <input type="file" class="form-control @error('watermark') is-invalid @enderror"
                                           id="watermark" name="watermark" accept="image/*">
                                    @error('watermark')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">Watermark for PDF documents. Accepted formats: JPG, PNG, GIF. Max size: 2MB. Leave empty to keep current watermark.</small>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="is_active" class="form-label">Status</label>
                                    <select class="form-control @error('is_active') is-invalid @enderror" 
                                            id="is_active" name="is_active">
                                        <option value="1" {{ old('is_active', $company->is_active) == '1' ? 'selected' : '' }}>Active</option>
                                        <option value="0" {{ old('is_active', $company->is_active) == '0' ? 'selected' : '' }}>Inactive</option>
                                    </select>
                                    @error('is_active')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Company
                            </button>
                            <a href="{{ route('companies.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update quotation preview when inputs change
    function updateQuotationPreview() {
        const code = document.getElementById('code').value || 'COMP';
        const prefix = document.getElementById('quotation_prefix').value || (code.toUpperCase() + '/QT');
        const currentNumber = document.getElementById('current_quotation_number').value || '2007';

        const preview = prefix + '/' + String(currentNumber).padStart(4, '0');

        document.getElementById('quotation_preview').textContent = preview;
    }

    // Add event listeners
    ['code', 'quotation_prefix', 'current_quotation_number'].forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('input', updateQuotationPreview);
        }
    });

    // Initial preview update
    updateQuotationPreview();
});
</script>
@endpush
