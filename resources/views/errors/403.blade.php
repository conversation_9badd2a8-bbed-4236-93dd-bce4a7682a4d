@extends('layouts.app')

@section('title', 'Access Denied - JMD Traders')
@section('page-title', 'Access Denied')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-lg border-0">
                <div class="card-body text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-shield-alt fa-4x text-danger mb-3"></i>
                        <h1 class="display-4 text-danger">403</h1>
                        <h2 class="text-muted">Access Denied</h2>
                    </div>
                   
                    <div class="alert alert-danger">
                        <h5 class="alert-heading">
                            <i class="fas fa-lock"></i>
                            Insufficient Permissions
                        </h5>
                        <p class="mb-3">
                            You don't have the required permissions to access this resource.
                            This could be due to one of the following reasons:
                        </p>
                        
                        <ul class="list-unstyled text-left">
                            <li class="mb-2">
                                <i class="fas fa-times-circle text-danger"></i>
                                Your role doesn't include the necessary permissions
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-times-circle text-danger"></i>
                                You're not assigned to the required company
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-times-circle text-danger"></i>
                                Your account access has been restricted
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-times-circle text-danger"></i>
                                The resource requires higher-level permissions
                            </li>
                        </ul>
                        
                        <hr>
                        <p class="mb-0">
                            <strong>Need access?</strong> Contact your system administrator to request the appropriate permissions.
                        </p>
                    </div>
                    
                    @auth
                    <div class="mt-4">
                        <h6 class="text-muted mb-3">Your Current Access:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="fas fa-user"></i> User Info
                                        </h6>
                                        <p class="card-text">
                                            <strong>Name:</strong> {{ auth()->user()->name }}<br>
                                            <strong>Email:</strong> {{ auth()->user()->email }}<br>
                                            <strong>Current Role:</strong>
                                            @if(auth()->user()->userRole)
                                                <span class="badge badge-primary">{{ auth()->user()->userRole->name }}</span>
                                            @else
                                                <span class="text-muted">No role in current company</span>
                                            @endif
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="fas fa-building"></i> Company Access
                                        </h6>
                                        <p class="card-text">
                                            <strong>Current Company:</strong><br>
                                            @if(auth()->user()->currentCompany)
                                                <span class="badge badge-success">{{ auth()->user()->currentCompany->name }}</span>
                                            @else
                                                <span class="badge badge-warning">No company selected</span>
                                            @endif
                                            <br><br>
                                            <strong>Company Access:</strong><br>
                                            @hascompany
                                                <span class="badge badge-success">
                                                    <i class="fas fa-check"></i> Active
                                                </span>
                                            @else
                                                <span class="badge badge-danger">
                                                    <i class="fas fa-times"></i> No Access
                                                </span>
                                            @endhascompany
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endauth
                    
                    <div class="mt-4">
                        <h6 class="text-muted mb-3">What can you do?</h6>
                        <div class="d-flex justify-content-center gap-3 flex-wrap">
                            @auth
                                @permission('dashboard.view')
                                <a href="{{ route('dashboard') }}" class="btn btn-primary">
                                    <i class="fas fa-tachometer-alt"></i> Go to Dashboard
                                </a>
                                @endpermission
                                
                               
                                
                                <a href="{{ route('profile.edit') }}" class="btn btn-outline-primary">
                                    <i class="fas fa-user"></i> View Profile
                                </a>
                            @endauth
                            
                            <a href="javascript:history.back()" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left"></i> Go Back
                            </a>
                            
                            @auth
                            <a href="{{ route('logout') }}" class="btn btn-outline-danger"
                               onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                            @else
                            <a href="{{ route('login') }}" class="btn btn-outline-primary">
                                <i class="fas fa-sign-in-alt"></i> Login
                            </a>
                            @endauth
                        </div>
                    </div>
                    
                    <div class="mt-5 text-muted">
                        <small>
                            <i class="fas fa-info-circle"></i>
                            If you believe you should have access to this resource, please contact your system administrator with the details of what you were trying to access.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .card {
        border-radius: 15px;
    }
    
    .display-4 {
        font-weight: 700;
    }
    
    .gap-3 {
        gap: 1rem;
    }
    
    .list-unstyled li {
        padding: 0.25rem 0;
    }
    
    .badge {
        font-size: 0.8em;
    }
    
    .btn {
        border-radius: 8px;
        padding: 0.5rem 1rem;
    }
    
    .card-body {
        padding: 1.5rem;
    }
    
    .alert {
        border-radius: 10px;
    }
</style>
@endpush
