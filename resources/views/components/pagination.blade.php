@if ($paginator->hasPages())
<div class="pagination-wrapper">
    <div class="pagination-info">
        <span class="text-muted">
            Showing {{ $paginator->firstItem() }} to {{ $paginator->lastItem() }} of {{ $paginator->total() }} results
        </span>
    </div>
    
    <div class="pagination-controls">
        <nav aria-label="Pagination Navigation">
            <ul class="pagination pagination-sm mb-0">
                {{-- Previous Page Link --}}
                @if ($paginator->onFirstPage())
                    <li class="page-item disabled">
                        <span class="page-link">
                            <i class="fas fa-chevron-left"></i>
                        </span>
                    </li>
                @else
                    <li class="page-item">
                        <a class="page-link" href="{{ $paginator->previousPageUrl() }}" rel="prev">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                @endif

                {{-- Pagination Elements --}}
                @foreach ($elements as $element)
                    {{-- "Three Dots" Separator --}}
                    @if (is_string($element))
                        <li class="page-item disabled">
                            <span class="page-link">{{ $element }}</span>
                        </li>
                    @endif

                    {{-- Array Of Links --}}
                    @if (is_array($element))
                        @foreach ($element as $page => $url)
                            @if ($page == $paginator->currentPage())
                                <li class="page-item active">
                                    <span class="page-link">{{ $page }}</span>
                                </li>
                            @else
                                <li class="page-item">
                                    <a class="page-link" href="{{ $url }}">{{ $page }}</a>
                                </li>
                            @endif
                        @endforeach
                    @endif
                @endforeach

                {{-- Next Page Link --}}
                @if ($paginator->hasMorePages())
                    <li class="page-item">
                        <a class="page-link" href="{{ $paginator->nextPageUrl() }}" rel="next">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                @else
                    <li class="page-item disabled">
                        <span class="page-link">
                            <i class="fas fa-chevron-right"></i>
                        </span>
                    </li>
                @endif
            </ul>
        </nav>
    </div>

    <div class="pagination-controls-right">
        <div class="pagination-per-page">
            <select class="form-select form-select-sm" onchange="changePerPage(this.value)" style="width: auto;">
                <option value="10" {{ request('per_page') == 10 ? 'selected' : '' }}>10 per page</option>
                <option value="25" {{ request('per_page', 25) == 25 ? 'selected' : '' }}>25 per page</option>
                <option value="50" {{ request('per_page') == 50 ? 'selected' : '' }}>50 per page</option>
                <option value="100" {{ request('per_page') == 100 ? 'selected' : '' }}>100 per page</option>
            </select>
        </div>

        @if(isset($showExport) && $showExport)
        <div class="pagination-export">
            <div class="dropdown">
                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-download me-1"></i>
                    Export
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" onclick="exportData('csv')">
                        <i class="fas fa-file-csv me-2"></i>CSV
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="exportData('excel')">
                        <i class="fas fa-file-excel me-2"></i>Excel
                    </a></li>
                </ul>
            </div>
        </div>
        @endif
    </div>
</div>

<style>
.pagination-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #f8f9fc;
    border-top: 1px solid #e3e6f0;
    border-radius: 0 0 8px 8px;
    flex-wrap: wrap;
    gap: 15px;
}

.pagination-info {
    font-size: 0.875rem;
    color: #6c757d;
}

.pagination-controls .pagination {
    margin: 0;
}

.pagination-controls .page-link {
    border: 1px solid #dee2e6;
    color: #495057;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.pagination-controls .page-link:hover {
    background-color: #e9ecef;
    border-color: #dee2e6;
    color: #495057;
}

.pagination-controls .page-item.active .page-link {
    background-color: #4e73df;
    border-color: #4e73df;
    color: white;
}

.pagination-controls .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
}

.pagination-controls-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.pagination-per-page .form-select {
    border: 1px solid #d1d3e2;
    font-size: 0.875rem;
    padding: 0.25rem 0.5rem;
    min-width: 120px;
}

.pagination-export .btn {
    font-size: 0.875rem;
    padding: 0.25rem 0.75rem;
}

@media (max-width: 768px) {
    .pagination-wrapper {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    .pagination-info {
        order: 3;
    }
    
    .pagination-controls {
        order: 1;
    }
    
    .pagination-controls-right {
        order: 2;
        flex-direction: column;
        gap: 10px;
    }
}
</style>

<script>
function changePerPage(perPage) {
    const url = new URL(window.location);
    url.searchParams.set('per_page', perPage);
    url.searchParams.delete('page'); // Reset to first page
    window.location.href = url.toString();
}

function exportData(format) {
    const url = new URL(window.location);
    const currentPath = url.pathname;

    // Determine export endpoint based on current path
    let exportUrl;
    if (currentPath.includes('/sales')) {
        exportUrl = currentPath.replace('/sales', '/sales/export');
    } else if (currentPath.includes('/transport')) {
        exportUrl = currentPath.replace('/transport', '/transport/export');
    } else {
        exportUrl = currentPath + '/export';
    }

    // Add format and current filters to export URL
    url.pathname = exportUrl;
    url.searchParams.set('format', format);

    // Open export in new tab
    window.open(url.toString(), '_blank');
}
</script>
@endif
