<div class="filter-card">
    <div class="filter-header">
        <h6 class="mb-0">
            <i class="fas fa-filter me-2"></i>
            Search & Filters
        </h6>
        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearFilters()">
            <i class="fas fa-times me-1"></i>
            Clear All
        </button>
    </div>
    
    <form method="GET" action="{{ route('entries.sales') }}" id="filterForm">
        <div class="filter-body">
            <!-- Search Bar -->
            <div class="search-section">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" name="search" 
                           placeholder="Search by quotation number, customer name, email, phone..." 
                           value="{{ request('search') }}">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>
                        Search
                    </button>
                </div>
            </div>

            <!-- Advanced Filters -->
            <div class="advanced-filters" id="advancedFilters" style="display: none;">
                <div class="row g-3">
                    <!-- Customer Filter -->
                    <div class="col-md-3">
                        <label class="form-label">Customer</label>
                        <select class="form-select" name="customer_id">
                            <option value="">All Customers</option>
                            @foreach($customers as $customer)
                                <option value="{{ $customer->id }}" 
                                        {{ request('customer_id') == $customer->id ? 'selected' : '' }}>
                                    {{ $customer->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Status Filter -->
                    <div class="col-md-3">
                        <label class="form-label">Status</label>
                        <select class="form-select" name="status">
                            <option value="">All Status</option>
                            <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                            <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>Approved</option>
                            <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>Rejected</option>
                        </select>
                    </div>

                    <!-- Date From -->
                    <div class="col-md-3">
                        <label class="form-label">Date From</label>
                        <input type="date" class="form-control" name="date_from" 
                               value="{{ request('date_from') }}">
                    </div>

                    <!-- Date To -->
                    <div class="col-md-3">
                        <label class="form-label">Date To</label>
                        <input type="date" class="form-control" name="date_to" 
                               value="{{ request('date_to') }}">
                    </div>

                    @if(auth()->user()->hasRole('super_admin') && isset($accessibleCompanies))
                    <!-- Company Filter (Super Admin Only) -->
                    <div class="col-md-3">
                        <label class="form-label">Company</label>
                        <select class="form-select" name="company_id">
                            <option value="">All Companies</option>
                            @foreach($accessibleCompanies as $company)
                                <option value="{{ $company->id }}" 
                                        {{ request('company_id') == $company->id ? 'selected' : '' }}>
                                    {{ $company->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    @endif
                </div>

                <div class="filter-actions mt-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i>
                        Apply Filters
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                        <i class="fas fa-times me-1"></i>
                        Clear Filters
                    </button>
                </div>
            </div>

            <!-- Toggle Advanced Filters -->
            <div class="filter-toggle">
                <button type="button" class="btn btn-sm btn-outline-primary" onclick="toggleAdvancedFilters()">
                    <i class="fas fa-sliders-h me-1"></i>
                    <span id="toggleText">Show Advanced Filters</span>
                </button>
            </div>
        </div>
    </form>
</div>

<style>
.filter-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    overflow: hidden;
}

.filter-header {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.filter-header .btn-outline-secondary {
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
    font-size: 0.75rem;
}

.filter-header .btn-outline-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
}

.filter-body {
    padding: 20px;
}

.search-section {
    margin-bottom: 15px;
}

.search-section .input-group-text {
    background: #f8f9fc;
    border-color: #d1d3e2;
    color: #6c757d;
}

.search-section .form-control {
    border-color: #d1d3e2;
}

.search-section .form-control:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.advanced-filters {
    border-top: 1px solid #e3e6f0;
    padding-top: 20px;
    margin-top: 15px;
}

.filter-toggle {
    text-align: center;
    margin-top: 15px;
}

.filter-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-start;
}

.form-label {
    font-weight: 500;
    color: #495057;
    font-size: 0.875rem;
    margin-bottom: 5px;
}

.form-select, .form-control {
    border: 1px solid #d1d3e2;
    border-radius: 4px;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.form-select:focus, .form-control:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

@media (max-width: 768px) {
    .filter-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .search-section .input-group {
        flex-direction: column;
    }
    
    .search-section .input-group .btn {
        border-radius: 0.375rem;
        margin-top: 10px;
    }
    
    .filter-actions {
        flex-direction: column;
    }
}
</style>

<script>
function toggleAdvancedFilters() {
    const filters = document.getElementById('advancedFilters');
    const toggleText = document.getElementById('toggleText');
    
    if (filters.style.display === 'none') {
        filters.style.display = 'block';
        toggleText.textContent = 'Hide Advanced Filters';
    } else {
        filters.style.display = 'none';
        toggleText.textContent = 'Show Advanced Filters';
    }
}

function clearFilters() {
    // Clear all form inputs
    document.querySelectorAll('#filterForm input, #filterForm select').forEach(input => {
        if (input.type === 'text' || input.type === 'date') {
            input.value = '';
        } else if (input.type === 'select-one') {
            input.selectedIndex = 0;
        }
    });
    
    // Submit the form to clear filters
    document.getElementById('filterForm').submit();
}

// Show advanced filters if any advanced filter is active
document.addEventListener('DOMContentLoaded', function() {
    const hasAdvancedFilters = {{ 
        request()->filled(['customer_id', 'status', 'date_from', 'date_to', 'company_id']) ? 'true' : 'false' 
    }};
    
    if (hasAdvancedFilters) {
        toggleAdvancedFilters();
    }
});
</script>
