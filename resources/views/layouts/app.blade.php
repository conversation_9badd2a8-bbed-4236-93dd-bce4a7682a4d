<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <title>@yield('title', 'JMD Traders Dashboard')</title>
    
    <!-- Custom fonts for this template-->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" type="text/css">
    <link href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Flatpickr CSS for Date Picker -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">


<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />




    <style>
        :root {
            --primary-color: #4e73df;
            --secondary-color: #858796;
            --success-color: #1cc88a;
            --info-color: #36b9cc;
            --warning-color: #f6c23e;
            --danger-color: #e74a3b;
            --light-color: #f8f9fc;
            --dark-color: #5a5c69;
            --sidebar-width: 280px;
            --topbar-height: 70px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            background-color: var(--light-color);
            color: var(--dark-color);
        }

        /* Datepicker Styles */
        .flatpickr-calendar {
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            border-radius: 10px;
            border: none;
            font-family: inherit;
        }

        .flatpickr-day.selected {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }

        .flatpickr-day.today {
            border-color: var(--success-color);
            color: var(--success-color);
        }

        .flatpickr-day:hover {
            background: var(--light-color);
            border-color: var(--primary-color);
        }

        .flatpickr-months .flatpickr-month {
            background: var(--primary-color);
            color: white;
        }

        .flatpickr-current-month .flatpickr-monthDropdown-months {
            background: var(--primary-color);
            color: white;
        }

        .flatpickr-weekdays {
            background: var(--light-color);
        }

        .flatpickr-weekday {
            color: var(--dark-color);
            font-weight: 600;
        }

        /* Date input with icon styling */
        .form-control[type="date"] {
            padding-right: 40px;
        }

        .date-input-wrapper {
            position: relative;
        }

        .date-input-wrapper .calendar-icon {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            pointer-events: none;
            z-index: 10;
        }

        /* Select2 Custom Styles */
        .select2-container {
            width: 100% !important;
        }

        .select2-container--default .select2-selection--single {
            height: 38px;
            border: 1px solid #d1d3e2;
            border-radius: 0.35rem;
            padding: 6px 12px;
            background-color: #fff;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            color: #5a5c69;
            line-height: 24px;
            padding-left: 0;
            padding-right: 20px;
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 36px;
            right: 10px;
        }

        .select2-container--default .select2-selection--multiple {
            border: 1px solid #d1d3e2;
            border-radius: 0.35rem;
            background-color: #fff;
            min-height: 38px;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice {
            background-color: var(--primary-color);
            border: 1px solid var(--primary-color);
            border-radius: 0.25rem;
            color: white;
            font-size: 0.875rem;
            padding: 2px 8px;
            margin: 2px;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
            color: white;
            margin-right: 5px;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
            color: #fff;
            background-color: rgba(255,255,255,0.2);
            border-radius: 2px;
        }

        .select2-container--default.select2-container--focus .select2-selection--single,
        .select2-container--default.select2-container--focus .select2-selection--multiple {
            border-color: #80bdff;
            box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
        }

        .select2-dropdown {
            border: 1px solid #d1d3e2;
            border-radius: 0.35rem;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }

        .select2-container--default .select2-results__option--highlighted[aria-selected] {
            background-color: var(--primary-color);
            color: white;
        }

        .select2-container--default .select2-results__option[aria-selected=true] {
            background-color: var(--light-color);
            color: var(--dark-color);
        }

        .select2-container--default .select2-search--dropdown .select2-search__field {
            border: 1px solid #d1d3e2;
            border-radius: 0.25rem;
            padding: 6px 12px;
            font-size: 0.875rem;
        }

        .select2-container--default .select2-search--dropdown .select2-search__field:focus {
            border-color: #80bdff;
            box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
            outline: none;
        }

        /* Select2 with validation states */
        .is-invalid + .select2-container .select2-selection {
            border-color: #e74a3b;
        }

        .is-valid + .select2-container .select2-selection {
            border-color: #1cc88a;
        }

        /* Select2 sizing */
        .select2-container--default .select2-selection--single.select2-selection--sm {
            height: 31px;
            font-size: 0.875rem;
        }

        .select2-container--default .select2-selection--single.select2-selection--lg {
            height: 48px;
            font-size: 1.25rem;
            padding: 8px 16px;
        }

        /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: var(--sidebar-width);
            height: 100vh;
            background: linear-gradient(135deg, var(--primary-color) 0%, #224abe 100%);
            color: white;
            z-index: 1000;
            overflow-y: auto;
            transition: all 0.3s ease;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }

        .sidebar-header {
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .sidebar-header h3 {
            color: white;
            margin: 0;
            font-size: 1.3rem;
            font-weight: 600;
        }

        .sidebar-nav {
            padding: 10px 0;
        }

        .nav-item {
            margin-bottom: 2px;
            position: relative;
            list-style: none;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 8px 15px;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            border-radius: 8px;
            margin: 0 8px;
            font-size: 0.9rem;
        }

        .nav-link:hover {
            background: rgba(255,255,255,0.1);
            color: white;
            transform: translateX(3px);
        }

        .nav-link.active {
            background: rgba(255,255,255,0.2);
            color: white;
            border-radius: 8px;
            margin: 0 8px;
        }

        .nav-link i {
            width: 18px;
            margin-right: 12px;
            text-align: center;
            font-size: 0.85rem;
        }

        /* Submenu Styles */
        .nav-item.has-submenu > .nav-link {
            position: relative;
        }

        .nav-item.has-submenu > .nav-link::after {
            content: '\f107';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            right: 20px;
            transition: transform 0.3s ease;
        }

        .nav-item.has-submenu.open > .nav-link::after {
            transform: rotate(180deg);
        }

        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: rgba(0,0,0,0.2);
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .submenu.open {
            max-height: 400px;
            padding: 5px 0;
        }

        .submenu .nav-item {
            margin-bottom: 1px;
            list-style: none;
        }

        .submenu .nav-link {
            padding: 6px 15px 6px 45px;
            font-size: 0.85rem;
            margin: 0 8px;
            border-radius: 6px;
        }

        /* Main Content */
        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
            transition: all 0.3s ease;
        }

        /* Top Bar */
        .topbar {
            background: white;
            height: var(--topbar-height);
            padding: 0 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 15px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 999;
            border-radius: 0 0 15px 15px;
        }

        .topbar-left h4 {
            color: var(--dark-color);
            margin: 0;
            font-weight: 700;
        }

        .topbar-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .notification-icon {
            position: relative;
            color: var(--secondary-color);
            font-size: 1.2rem;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .notification-icon:hover {
            color: var(--primary-color);
        }

        .notification-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: var(--danger-color);
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Content Area */
        .content-area {
            padding: 20px;
        }

        /* Cards */
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            background: white;
            border-bottom: 1px solid #e3e6f0;
            padding: 15px;
            border-radius: 15px 15px 0 0 !important;
        }

        .card-body {
            padding: 15px;
        }

        /* Dashboard Card Borders */
        .border-left-primary {
            border-left: 4px solid var(--primary-color) !important;
        }

        .border-left-success {
            border-left: 4px solid var(--success-color) !important;
        }

        .border-left-info {
            border-left: 4px solid var(--info-color) !important;
        }

        .border-left-warning {
            border-left: 4px solid var(--warning-color) !important;
        }

        .border-left-danger {
            border-left: 4px solid var(--danger-color) !important;
        }

        /* Text Colors */
        .text-primary {
            color: var(--primary-color) !important;
        }

        .text-success {
            color: var(--success-color) !important;
        }

        .text-info {
            color: var(--info-color) !important;
        }

        .text-warning {
            color: var(--warning-color) !important;
        }

        .text-danger {
            color: var(--danger-color) !important;
        }

        .text-gray-800 {
            color: var(--dark-color) !important;
        }

        .text-gray-300 {
            color: var(--secondary-color) !important;
        }

        /* Buttons */
        .btn {
            border-radius: 8px;
            padding: 8px 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.85rem;
        }

        .btn-primary {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background: #224abe;
            border-color: #224abe;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(78, 115, 223, 0.4);
        }

        /* Tables */
        .table {
            border-radius: 15px;
            overflow: hidden;
        }

        .table thead th {
            background: var(--primary-color);
            color: white;
            border: none;
            font-weight: 600;
            padding: 12px;
            font-size: 0.9rem;
        }

        .table tbody td {
            padding: 12px;
            vertical-align: middle;
            border-color: #e3e6f0;
            font-size: 0.9rem;
        }

        .table tbody tr:hover {
            background: rgba(78, 115, 223, 0.05);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
        }

        /* Notification Styles */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 9999;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: var(--success-color);
        }

        .notification.error {
            background: var(--danger-color);
        }

        .notification.info {
            background: var(--info-color);
        }

        .notification.warning {
            background: var(--warning-color);
        }
    </style>
    <style>
        :root {
            --primary-color: #4e73df;
            --secondary-color: #858796;
            --success-color: #1cc88a;
            --info-color: #36b9cc;
            --warning-color: #f6c23e;
            --danger-color: #e74a3b;
            --light-color: #f8f9fc;
            --dark-color: #5a5c69;
            --transport-color: #28a745;
        }

        .compact-container {
            /* max-width: 1400px; */
            margin: 0 auto;
            padding: 15px;
        }

        .header-card {
            background: linear-gradient(135deg, var(--primary-color) 0%, #224abe 100%);
            color: white;
            padding: 20px 25px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 4px 12px rgba(78, 115, 223, 0.15);
            border: none;
        }

        .header-card h3 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 12px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .stats-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            overflow: hidden;
            border: 1px solid #e3e6f0;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 0;
        }

        .stat-item {
            padding: 18px 20px;
            border-right: 1px solid #e3e6f0;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-item:last-child {
            border-right: none;
        }

        .stat-item:hover {
            background: var(--light-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 6px;
            color: var(--dark-color);
        }

        .stat-label {
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: 600;
            color: var(--secondary-color);
        }

        .stat-icon {
            font-size: 1.4rem;
            margin-bottom: 8px;
            opacity: 0.8;
        }

        /* Status-specific colors */
        .stat-item.stat-total .stat-number { color: var(--primary-color); }
        .stat-item.stat-total .stat-icon { color: var(--primary-color); }
        
        .stat-item.stat-scheduled .stat-number { color: var(--warning-color); }
        .stat-item.stat-scheduled .stat-icon { color: var(--warning-color); }
        
        .stat-item.stat-transit .stat-number { color: var(--info-color); }
        .stat-item.stat-transit .stat-icon { color: var(--info-color); }
        
        .stat-item.stat-delivered .stat-number { color: var(--success-color); }
        .stat-item.stat-delivered .stat-icon { color: var(--success-color); }

        .filters-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            padding: 18px 25px;
            border: 1px solid #e3e6f0;
        }

        .filter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .table-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .table-header {
            background: linear-gradient(135deg, var(--light-color) 0%, #e3e6f0 100%);
            padding: 18px 25px;
            border-bottom: 2px solid var(--primary-color);
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        .custom-table {
            margin: 0;
            font-size: 0.875rem;
            border: none;
        }

        .custom-table th {
            background: linear-gradient(135deg, var(--primary-color) 0%, #224abe 100%);
            color: white;
            font-weight: 600;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            padding: 15px 18px;
            border: none;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .custom-table td {
            padding: 15px 18px;
            vertical-align: middle;
            border-top: 1px solid #e3e6f0;
            color: var(--dark-color);
        }

        .custom-table tbody tr {
            transition: all 0.3s ease;
        }

        .custom-table tbody tr:hover {
            background: var(--light-color);
            transform: translateX(2px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: var(--dark-color);
            font-size: 0.75rem;
            margin-bottom: 6px;
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }

        .form-control, .form-select {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #e3e6f0;
            border-radius: 6px;
            font-size: 0.875rem;
            background: white;
            transition: all 0.3s ease;
            color: var(--dark-color);
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
            background: white;
            transition: all 0.2s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: #28a745;
            background: white;
            outline: none;
            box-shadow: 0 0 0 0.1rem rgba(40, 167, 69, 0.2);
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            font-weight: 600;
            font-size: 0.8rem;
        }

        /* Buttons */
        .btn {
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: 600;
            font-size: 0.875rem;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.3px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, #224abe 100%);
            color: white;
            box-shadow: 0 2px 4px rgba(78, 115, 223, 0.2);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #224abe 0%, #1a365c 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(78, 115, 223, 0.3);
            color: white;
            text-decoration: none;
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #17a673 100%);
            color: white;
            box-shadow: 0 2px 4px rgba(28, 200, 138, 0.2);
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #17a673 0%, #138f63 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(28, 200, 138, 0.3);
            color: white;
            text-decoration: none;
        }

        .btn-outline-secondary {
            border: 2px solid #e3e6f0;
            color: var(--secondary-color);
            background: white;
            transition: all 0.3s ease;
        }

        .btn-outline-secondary:hover {
            background: var(--light-color);
            border-color: var(--primary-color);
            color: var(--primary-color);
            transform: translateY(-2px);
            text-decoration: none;
        }

        .btn-light {
            background: linear-gradient(135deg, var(--light-color) 0%, #e3e6f0 100%);
            color: var(--primary-color);
            border: 1px solid #e3e6f0;
        }

        .btn-light:hover {
            background: linear-gradient(135deg, var(--primary-color) 0%, #224abe 100%);
            color: white;
            transform: translateY(-2px);
            text-decoration: none;
        }

        /* Badges */
        .badge {
            font-size: 0.7rem;
            padding: 6px 12px;
            border-radius: 15px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.3px;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .bg-primary { background: var(--primary-color) !important; color: white; }
        .bg-info { background: var(--info-color) !important; color: white; }
        .bg-success { background: var(--success-color) !important; color: white; }
        .bg-warning { background: var(--warning-color) !important; color: #212529; }
        .bg-danger { background: var(--danger-color) !important; color: white; }

        .status-scheduled { background: var(--warning-color); color: #856404; border: 2px solid #ffeaa7; }
        .status-in_transit { background: var(--info-color); color: white; border: 2px solid #bee5eb; }
        .status-delivered { background: var(--success-color); color: white; border: 2px solid #c3e6cb; }
        .status-delayed { background: var(--danger-color); color: white; border: 2px solid #f5c6cb; }

        .action-buttons {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
            align-items: center;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.75rem;
            min-width: auto;
        }

        .empty-state {
            text-align: center;
            padding: 50px 20px;
            color: var(--secondary-color);
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.3;
        }

        @media (max-width: 768px) {
            .compact-container {
                padding: 10px;
            }
            
            .filter-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .custom-table {
                font-size: 0.75rem;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>

    @stack('styles')
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h3><i class="fas fa-chart-line"></i>
                @if(isset($currentCompany))
                    {{ $currentCompany->name }}
                @else
                    JMD Traders
                @endif
            </h3>
            @if(isset($currentCompany))
                <small class="text-light opacity-75">{{ $currentCompany->city ?? 'Multi-Company System' }}</small>
            @endif
        </div>
        
        <nav class="sidebar-nav">
            <ul class="nav flex-column">
                @permission('dashboard.view')
                <li class="nav-item">
                    <a href="{{ route('dashboard') }}" class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}">
                        <i class="fas fa-tachometer-alt"></i>
                        Dashboard
                    </a>
                </li>
                @endpermission
                
                <!-- Masters Menu with Submenu -->
                @anypermission(['categories.view', 'products.view', 'customers.view', 'users.view', 'states.view', 'cities.view'])
                <li class="nav-item has-submenu {{ request()->routeIs('masters.*') ? 'open' : '' }}">
                    <a href="#" class="nav-link" onclick="toggleSubmenu(this)">
                        <i class="fas fa-database"></i>
                        Masters
                    </a>
                    <ul class="submenu {{ request()->routeIs('masters.*') || request()->routeIs('categories.*') || request()->routeIs('products.*') || request()->routeIs('customers.*') || request()->routeIs('users.*') ? 'open' : '' }}">
                        @permission('categories.view')
                        <li class="nav-item">
                            <a href="{{ route('categories.index') }}" class="nav-link {{ request()->routeIs('categories.*') ? 'active' : '' }}">
                                <i class="fas fa-tags"></i>
                                Group Master
                            </a>
                        </li>
                        @endpermission

                        @permission('products.view')
                        <li class="nav-item">
                            <a href="{{ route('products.index') }}" class="nav-link {{ request()->routeIs('products.*') ? 'active' : '' }}">
                                <i class="fas fa-box"></i>
                                Product Master
                            </a>
                        </li>
                        @endpermission

                        @permission('customers.view')
                        <li class="nav-item">
                            <a href="{{ route('customers.index') }}" class="nav-link {{ request()->routeIs('customers.*') ? 'active' : '' }}">
                                <i class="fas fa-user-tie"></i>
                                Customer Master
                            </a>
                        </li>
                        @endpermission

                        @permission('users.view')
                        <li class="nav-item">
                            <a href="{{ route('users.index') }}" class="nav-link {{ request()->routeIs('users.*') ? 'active' : '' }}">
                                <i class="fas fa-users-cog"></i>
                                User Master
                            </a>
                        </li>
                        @endpermission

                        @permission('states.view')
                        <li class="nav-item">
                            <a href="{{ route('masters.state') }}" class="nav-link {{ request()->routeIs('masters.state') ? 'active' : '' }}">
                                <i class="fas fa-map"></i>
                                State Master
                            </a>
                        </li>
                        @endpermission

                        @permission('cities.view')
                        <li class="nav-item">
                            <a href="{{ route('masters.city') }}" class="nav-link {{ request()->routeIs('masters.city') ? 'active' : '' }}">
                                <i class="fas fa-city"></i>
                                City Master
                            </a>
                        </li>
                        @endpermission


                    </ul>
                </li>
                @endanypermission

                @permission('sales.view')
                <li class="nav-item">
                    <a href="{{ route('entries.sales') }}" class="nav-link {{ request()->routeIs('entries.sales') ? 'active' : '' }}">
                        <i class="fas fa-file-invoice-dollar"></i>
                        Quatation Entry
                    </a>
                </li>
                @endpermission

                @permission('transport.view')
                <li class="nav-item">
                    <a href="{{ route('entries.transport') }}" class="nav-link {{ request()->routeIs('entries.transport') ? 'active' : '' }}">
                        <i class="fas fa-truck"></i>
                        Transport Entry
                    </a>
                </li>
                @endpermission

               
                <!-- Reports Menu with Submenu -->
                @anypermission(['reports.view', 'reports.daily', 'reports.monthly'])
                <li class="nav-item has-submenu {{ request()->routeIs('reports.*') ? 'open' : '' }}">
                    <a href="#" class="nav-link" onclick="toggleSubmenu(this)">
                        <i class="fas fa-chart-bar"></i>
                        Reports
                    </a>
                    <ul class="submenu {{ request()->routeIs('reports.*') ? 'open' : '' }}">
                        @permission('reports.daily')
                        <li class="nav-item">
                            <a href="{{ route('reports.daily') }}" class="nav-link {{ request()->routeIs('reports.daily') ? 'active' : '' }}">
                                <i class="fas fa-calendar-day"></i>
                                Daily Report
                            </a>
                        </li>
                        @endpermission

                        @permission('reports.monthly')
                        <li class="nav-item">
                            <a href="{{ route('reports.monthly') }}" class="nav-link {{ request()->routeIs('reports.monthly') ? 'active' : '' }}">
                                <i class="fas fa-calendar-alt"></i>
                                Monthly Report
                            </a>
                        </li>
                        @endpermission

                        @permission('reports.view')
                        <li class="nav-item">
                            <a href="{{ route('reports.quotation') }}" class="nav-link {{ request()->routeIs('reports.quotation') ? 'active' : '' }}">
                                <i class="fas fa-file-invoice"></i>
                                Quotation Report
                            </a>
                        </li>
                        @endpermission

                        @permission('reports.view')
                        <li class="nav-item">
                            <a href="{{ route('reports.sales') }}" class="nav-link {{ request()->routeIs('reports.sales') ? 'active' : '' }}">
                                <i class="fas fa-chart-line"></i>
                                Sales Report
                            </a>
                        </li>
                        @endpermission

                        @permission('reports.view')
                        <li class="nav-item">
                            <a href="{{ route('reports.customer') }}" class="nav-link {{ request()->routeIs('reports.customer') ? 'active' : '' }}">
                                <i class="fas fa-users"></i>
                                Customer Report
                            </a>
                        </li>
                        @endpermission
                    </ul>
                </li>
                @endanypermission
                
                <!-- Admin Menu with Submenu -->
                @anypermission(['roles.view', 'permissions.view', 'companies.view'])
                <li class="nav-item has-submenu {{ request()->routeIs('admin.roles.*') || request()->routeIs('admin.permissions.*') || request()->routeIs('companies.*') ? 'open' : '' }}">
                    <a href="#" class="nav-link" onclick="toggleSubmenu(this)">
                        <i class="fas fa-shield-alt"></i>
                        Administration
                    </a>
                    <ul class="submenu {{ request()->routeIs('admin.roles.*') || request()->routeIs('admin.permissions.*') || request()->routeIs('companies.*') ? 'open' : '' }}">
                        @permission('roles.view')
                        <li class="nav-item">
                            <a href="{{ route('admin.roles.index') }}" class="nav-link {{ request()->routeIs('admin.roles.*') ? 'active' : '' }}">
                                <i class="fas fa-user-tag"></i>
                                Roles Management
                            </a>
                        </li>
                        @endpermission

                        @permission('permissions.view')
                        <li class="nav-item">
                            <a href="{{ route('admin.permissions.index') }}" class="nav-link {{ request()->routeIs('admin.permissions.*') ? 'active' : '' }}">
                                <i class="fas fa-key"></i>
                                Permissions Management
                            </a>
                        </li>
                        @endpermission

                        @permission('companies.view')
                        <li class="nav-item">
                            <a href="{{ route('companies.index') }}" class="nav-link {{ request()->routeIs('companies.*') ? 'active' : '' }}">
                                <i class="fas fa-building"></i>
                                Companies Management
                            </a>
                        </li>
                        @endpermission

                        
                    </ul>
                </li>
                @endanypermission

                @permission('settings.view')
                <!-- <li class="nav-item">
                    <a href="{{ route('settings') }}" class="nav-link {{ request()->routeIs('settings') ? 'active' : '' }}">
                        <i class="fas fa-cog"></i>
                        Settings
                    </a>
                </li> -->
                @endpermission
                <li class="nav-item">
                    <form action="{{ route('logout') }}" method="POST" style="display: inline;">
                        @csrf
                        <button type="submit" class="nav-link" style="border: none; background: none; width: 100%; text-align: left;">
                            <i class="fas fa-sign-out-alt"></i>
                            Logout
                        </button>
                    </form>
                </li>
            </ul>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="topbar">
            <div class="topbar-left">
                <h4>@yield('page-title', 'Dashboard')</h4>
            </div>
            <div class="topbar-right">
                <!-- Company Switcher -->
                @if(isset($userCompanies) && $userCompanies->count() > 1)
                <div class="dropdown me-3">
                    <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-building"></i>
                        @if(isset($currentCompany))
                            {{ $currentCompany->name }}
                            @if(auth()->user()->canAccessAllCompanies())
                                <small class="d-block text-warning" style="font-size: 0.7rem;">
                                    <i class="fas fa-crown"></i> Super Admin - All Data
                                </small>
                            @endif
                        @else
                            Select Company
                        @endif
                    </button>
                    <ul class="dropdown-menu">
                        @foreach($userCompanies as $company)
                            <li>
                                <form action="{{ route('company.switch') }}" method="POST" style="display: inline;">
                                    @csrf
                                    <input type="hidden" name="company_id" value="{{ $company->id }}">
                                    <button type="submit" class="dropdown-item {{ isset($currentCompany) && $currentCompany->id == $company->id ? 'active' : '' }}">
                                        <i class="fas fa-building"></i> {{ $company->name }}
                                        @if(isset($currentCompany) && $currentCompany->id == $company->id)
                                            <i class="fas fa-check text-success ms-2"></i>
                                        @endif
                                    </button>
                                </form>
                            </li>
                        @endforeach
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ route('companies.index') }}"><i class="fas fa-cog"></i> Manage Companies</a></li>
                    </ul>
                </div>
                @endif

                

                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user"></i> {{ auth()->user()->name ?? 'Admin' }}
                        @if(isset($currentCompany))
                            <small class="d-block text-muted" style="font-size: 0.7rem;">{{ auth()->user()->getCurrentCompanyRole() }}</small>
                        @endif
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ route('profile.show') }}"><i class="fas fa-user-edit"></i> Profile</a></li>
                        <!-- <li><a class="dropdown-item" href="{{ route('settings') }}"><i class="fas fa-cog"></i> Settings</a></li> -->
                        <li><a class="dropdown-item" href="{{ route('companies.index') }}"><i class="fas fa-building"></i> Companies</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form action="{{ route('logout') }}" method="POST">
                                @csrf
                                <button type="submit" class="dropdown-item">
                                    <i class="fas fa-sign-out-alt"></i> Logout
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            @yield('content')
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Flatpickr JS for Date Picker -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <!-- Global Datepicker Initialization -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize datepickers for all date inputs
            initializeDatepickers();

            // Initialize Select2 for all select inputs
            initializeSelect2();

            // Re-initialize components when new content is added dynamically
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.addedNodes.length > 0) {
                        initializeDatepickers();
                        initializeSelect2();
                    }
                });
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        });

        function initializeDatepickers() {
            // Basic date inputs
            const dateInputs = document.querySelectorAll('input[type="date"]:not(.flatpickr-input)');
            dateInputs.forEach(function(input) {
                flatpickr(input, {
                    dateFormat: "Y-m-d",
                    allowInput: true,
                    clickOpens: true,
                    theme: "material_blue",
                    locale: {
                        firstDayOfWeek: 1 // Monday
                    },
                    onReady: function(selectedDates, dateStr, instance) {
                        // Add calendar icon
                        const calendarIcon = document.createElement('i');
                        calendarIcon.className = 'fas fa-calendar-alt';
                        calendarIcon.style.cssText = 'position: absolute; right: 10px; top: 50%; transform: translateY(-50%); color: #6c757d; pointer-events: none; z-index: 10;';

                        // Make parent position relative if not already
                        const parent = input.parentElement;
                        if (getComputedStyle(parent).position === 'static') {
                            parent.style.position = 'relative';
                        }
                        parent.appendChild(calendarIcon);
                    }
                });
            });

            // Date range inputs (if any)
            const dateRangeInputs = document.querySelectorAll('.date-range:not(.flatpickr-input)');
            dateRangeInputs.forEach(function(input) {
                flatpickr(input, {
                    mode: "range",
                    dateFormat: "Y-m-d",
                    allowInput: true,
                    theme: "material_blue",
                    locale: {
                        firstDayOfWeek: 1
                    }
                });
            });

            // DateTime inputs (if any)
            const datetimeInputs = document.querySelectorAll('.datetime-picker:not(.flatpickr-input)');
            datetimeInputs.forEach(function(input) {
                flatpickr(input, {
                    enableTime: true,
                    dateFormat: "Y-m-d H:i",
                    allowInput: true,
                    theme: "material_blue",
                    time_24hr: true,
                    locale: {
                        firstDayOfWeek: 1
                    }
                });
            });

            // Month picker (if any)
            const monthInputs = document.querySelectorAll('.month-picker:not(.flatpickr-input)');
            monthInputs.forEach(function(input) {
                flatpickr(input, {
                    plugins: [new monthSelectPlugin({
                        shorthand: true,
                        dateFormat: "Y-m",
                        altFormat: "F Y"
                    })],
                    theme: "material_blue"
                });
            });
        }

        // Utility function to set date value
        function setDateValue(selector, date) {
            const input = document.querySelector(selector);
            if (input && input._flatpickr) {
                input._flatpickr.setDate(date);
            } else if (input) {
                input.value = date;
            }
        }

        // Utility function to get date value
        function getDateValue(selector) {
            const input = document.querySelector(selector);
            if (input && input._flatpickr) {
                return input._flatpickr.selectedDates[0];
            } else if (input) {
                return new Date(input.value);
            }
            return null;
        }

       
    </script>
    
    <script>
        // Toggle submenu functionality
        function toggleSubmenu(element) {
            event.preventDefault();
            const navItem = element.parentElement;
            const submenu = navItem.querySelector('.submenu');
            
            // Close other open submenus
            document.querySelectorAll('.nav-item.has-submenu.open').forEach(item => {
                if (item !== navItem) {
                    item.classList.remove('open');
                    item.querySelector('.submenu').classList.remove('open');
                }
            });
            
            // Toggle current submenu
            navItem.classList.toggle('open');
            submenu.classList.toggle('open');
        }

        // Show notification function
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);
            
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // CSRF token setup for AJAX (if jQuery is available)
        if (typeof $ !== 'undefined') {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
        }
    </script>
    <script>
        $(document).ready(function() {
            $('.myselect').select2({
            placeholder: "Search or select an option"
            });
        });
    </script>
    
    @stack('scripts')
</body>
</html>
