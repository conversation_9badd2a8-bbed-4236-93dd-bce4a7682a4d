# Dompdf "Frame not found in cellmap" Error Fix

## Problem Description
The application was encountering a Dompdf exception: "Frame not found in cellmap" when generating PDF documents from sales and transport entries. This error typically occurs when Dompdf encounters HTML/CSS structures it cannot properly parse, particularly:

- CSS Grid layouts (`display: grid`)
- CSS Flexbox layouts (`display: flex`)
- Complex table structures with advanced CSS
- Unsupported CSS properties

## Root Cause
The error was occurring in the `EntryController.php` at line 242 in the `printSales()` method when calling `$dompdf->render()`. The issue was caused by the `entries.sales.print` template using CSS Grid and Flexbox layouts that Dompdf doesn't handle well.

## Solution Implemented

### 1. Modified EntryController.php
- **Changed template usage**: Switched from `entries.sales.print` to `entries.sales.print-simple` template
- **Added fallback mechanism**: If the simple template fails, falls back to `entries.sales.print-basic`
- **Enhanced error handling**: Added try-catch blocks around template rendering and PDF generation
- **Improved Dompdf configuration**: Added security and compatibility options

### 2. Created print-basic.blade.php
- **Dompdf-compatible template**: Uses only table-based layouts and basic CSS
- **No CSS Grid/Flexbox**: Avoids problematic CSS features
- **Comprehensive fallback**: Includes all necessary sales entry information
- **Simple styling**: Uses only CSS properties well-supported by Dompdf

### 3. Enhanced Error Handling
- **Template-level fallbacks**: Multiple template options to ensure PDF generation
- **Detailed logging**: Logs errors for debugging while providing fallbacks
- **Graceful degradation**: System continues to work even if advanced templates fail

## Files Modified

### app/Http/Controllers/EntryController.php
- `printSales()` method (lines ~230-262)
- `downloadSharedSalesPdf()` method (lines ~505-531)
- `downloadSalesPdf()` method (lines ~329-354)
- `generateSalesPdfUrl()` method (lines ~377-402)
- `downloadSalesForPrint()` method (lines ~653-678)
- `downloadTransportForPrint()` method (lines ~721-751)
- `downloadTransportPdf()` method (lines ~1346-1378)

### New Files Created
- `resources/views/entries/sales/print-basic.blade.php` - Dompdf-compatible fallback template
- `test_pdf_fix.php` - Test script to verify the fix
- `DOMPDF_FIX_SUMMARY.md` - This documentation file

## Key Changes Made

### 1. Template Strategy
```php
// Before
$html = view('entries.sales.print', compact('salesEntry', 'company', 'pdfSettings', 'watermark'))->render();

// After
try {
    $html = view('entries.sales.print-simple', compact('salesEntry', 'company', 'pdfSettings', 'watermark'))->render();
} catch (\Exception $e) {
    \Log::warning('Print template error, using fallback: ' . $e->getMessage());
    $html = view('entries.sales.print-basic', compact('salesEntry'))->render();
}
```

### 2. Enhanced Dompdf Options
```php
$options = new \Dompdf\Options();
$options->set('defaultFont', $pdfSettings['font_family'] ?? 'Arial');
$options->set('isRemoteEnabled', true);
$options->set('isHtml5ParserEnabled', true);
$options->set('isPhpEnabled', false); // Security improvement
$options->set('debugKeepTemp', false);
$options->set('debugCss', false);
$options->set('debugLayout', false);
```

### 3. Error Handling
```php
try {
    $dompdf->loadHtml($html);
    $dompdf->setPaper($pdfSettings['paper_size'] ?? 'A4', $pdfSettings['orientation'] ?? 'portrait');
    $dompdf->render();
} catch (\Exception $e) {
    \Log::error('Dompdf render error: ' . $e->getMessage());
    throw new \Exception('PDF generation failed: ' . $e->getMessage());
}
```

## Testing the Fix

1. **Try generating a PDF** from any sales entry
2. **Check Laravel logs** for any remaining errors
3. **Verify fallback behavior** by temporarily renaming the print-simple template
4. **Test different entry types** (sales, transport) to ensure all work

## Recommendations

### 1. Template Best Practices for Dompdf
- Use table-based layouts instead of CSS Grid/Flexbox
- Avoid complex CSS selectors and pseudo-elements
- Use inline styles when possible for critical styling
- Test templates with Dompdf before deploying

### 2. Monitoring
- Monitor Laravel logs for PDF generation errors
- Set up alerts for repeated PDF generation failures
- Consider implementing metrics for PDF generation success rates

### 3. Future Improvements
- Consider migrating to a more modern PDF library like Puppeteer or wkhtmltopdf
- Implement PDF generation as a background job for large documents
- Add PDF caching to improve performance

## Troubleshooting

If PDF generation still fails:

1. **Check Laravel logs** for specific error messages
2. **Verify template exists** - ensure print-simple and print-basic templates are present
3. **Test with basic template** - temporarily force use of print-basic template
4. **Check Dompdf version** - ensure you're using a compatible version
5. **Memory limits** - PDF generation can be memory-intensive

## Security Notes

- Disabled PHP execution in Dompdf templates (`isPhpEnabled: false`)
- Disabled debug options in production
- Using safe default fonts (Arial instead of custom fonts)
- Proper input validation should be maintained in templates

## Performance Considerations

- The fallback mechanism adds minimal overhead
- Basic template renders faster than complex templates
- Consider implementing PDF caching for frequently accessed documents
- Monitor memory usage during PDF generation

This fix ensures robust PDF generation while maintaining backward compatibility and providing clear error handling.
