<?php

/**
 * Indian States and Cities Import Script
 * 
 * This script imports all Indian states and major cities into the database.
 * Run this from the Laravel project root directory.
 * 
 * Usage:
 * php import_indian_states_cities.php
 */

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

// Bootstrap Laravel
$app = Application::configure(basePath: __DIR__)
    ->withRouting(
        web: __DIR__.'/routes/web.php',
        commands: __DIR__.'/routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        //
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);

echo "🇮🇳 Indian States and Cities Import Tool\n";
echo "========================================\n\n";

echo "🚀 Starting import process...\n\n";

// Run the import command
$exitCode = $kernel->call('import:indian-states-cities', [
    '--force' => true  // Force update existing records
]);

if ($exitCode === 0) {
    echo "\n✅ Import completed successfully!\n\n";
    
    echo "📋 What was imported:\n";
    echo "• All 28 Indian States\n";
    echo "• 8 Union Territories\n";
    echo "• 300+ Major Cities across India\n";
    echo "• Complete state-city relationships\n\n";
    
    echo "🎯 Key Features:\n";
    echo "• Proper state codes (AP, MH, GJ, etc.)\n";
    echo "• Active status for all records\n";
    echo "• Soft delete support\n";
    echo "• Indexed for fast queries\n";
    echo "• Ready for customer/company forms\n\n";
    
    echo "📊 Usage Examples:\n";
    echo "• Customer registration forms\n";
    echo "• Company address management\n";
    echo "• Shipping and billing addresses\n";
    echo "• Location-based filtering\n";
    echo "• State-wise reports\n\n";
    
    echo "🔧 Database Tables Updated:\n";
    echo "• states - All Indian states and UTs\n";
    echo "• cities - Major cities with state relationships\n\n";
    
    echo "🎉 Your application now has complete Indian location data!\n";
    echo "You can now use State and City models in your forms and reports.\n\n";
    
} else {
    echo "\n❌ Import failed with exit code: {$exitCode}\n";
    echo "Please check the error messages above and try again.\n\n";
}

echo "📚 Next Steps:\n";
echo "1. Update your customer/company forms to use the new data\n";
echo "2. Add state/city dropdowns in your UI\n";
echo "3. Test the relationships in your models\n";
echo "4. Consider adding more cities if needed\n\n";

echo "💡 Pro Tips:\n";
echo "• Use State::active()->get() to get active states\n";
echo "• Use City::with('state')->active()->get() for cities with states\n";
echo "• Filter cities by state: \$state->cities()->active()->get()\n";
echo "• The data includes major commercial and industrial cities\n\n";

echo "Thank you for using the Indian States and Cities Import Tool! 🙏\n";
