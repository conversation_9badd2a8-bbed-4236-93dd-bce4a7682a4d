<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Location API Routes
use App\Http\Controllers\Api\LocationController;

Route::prefix('locations')->group(function () {
    Route::get('/states', [LocationController::class, 'getStates']);
    Route::get('/cities', [LocationController::class, 'getAllCities']);
    Route::get('/cities/state/{stateId}', [LocationController::class, 'getCitiesByState']);
    Route::get('/stats', [LocationController::class, 'getLocationStats']);
    Route::get('/search', [LocationController::class, 'searchLocations']);
});
