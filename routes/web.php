<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\UserMasterController;
use App\Http\Controllers\EntryController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\CompanyController;
use App\Http\Controllers\MasterController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\PermissionController;
use App\Http\Controllers\ProfileController;

// Public routes
Route::get('/', [LoginController::class, 'showLoginForm'])->name('login');
Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');

Route::post('/login', [LoginController::class, 'login'])->name('login.post');


// Authenticated routes
Route::middleware(['auth', 'refresh.permissions'])->group(function () {
    
    // Home page
    
    Route::get('/home', [DashboardController::class, 'index'])
        ->middleware('permission:dashboard.view')
        ->name('home');

    // Logout
    Route::post('/logout', [LoginController::class, 'logout'])->name('logout');
    
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])
        ->middleware('permission:dashboard.view')
        ->name('dashboard');
    Route::get('/dashboard/data', [DashboardController::class, 'getDashboardData'])
        ->middleware('permission:dashboard.view')
        ->name('dashboard.data');
    
    // Application routes

    // Users
    Route::get('/users', [UserMasterController::class, 'index'])
        ->middleware('permission:users.view')->name('users.index');
    Route::get('/users/create', [UserMasterController::class, 'create'])
        ->middleware('permission:users.create')->name('users.create');
    Route::post('/users', [UserMasterController::class, 'store'])
        ->middleware('permission:users.create')->name('users.store');
    Route::get('/users/{user}', [UserMasterController::class, 'show'])
        ->middleware('permission:users.view')->name('users.show');
    Route::get('/users/{user}/edit', [UserMasterController::class, 'edit'])
        ->middleware('permission:users.edit')->name('users.edit');
    Route::put('/users/{user}', [UserMasterController::class, 'update'])
        ->middleware('permission:users.edit')->name('users.update');
    Route::delete('/users/{user}', [UserMasterController::class, 'destroy'])
        ->middleware('permission:users.delete')->name('users.destroy');

    // Categories
    Route::get('/categories', [CategoryController::class, 'index'])
        ->middleware('permission:categories.view')->name('categories.index');
    Route::get('/categories/create', [CategoryController::class, 'create'])
        ->middleware('permission:categories.create')->name('categories.create');
    Route::post('/categories', [CategoryController::class, 'store'])
        ->middleware('permission:categories.create')->name('categories.store');
    Route::get('/categories/{category}', [CategoryController::class, 'show'])
        ->middleware('permission:categories.view')->name('categories.show');
    Route::get('/categories/{category}/edit', [CategoryController::class, 'edit'])
        ->middleware('permission:categories.edit')->name('categories.edit');
    Route::put('/categories/{category}', [CategoryController::class, 'update'])
        ->middleware('permission:categories.edit')->name('categories.update');
    Route::delete('/categories/{category}', [CategoryController::class, 'destroy'])
        ->middleware('permission:categories.delete')->name('categories.destroy');

    // Products
    Route::get('/products', [ProductController::class, 'index'])
        ->middleware('permission:products.view')->name('products.index');
    Route::get('/products/create', [ProductController::class, 'create'])
        ->middleware('permission:products.create')->name('products.create');
    Route::post('/products', [ProductController::class, 'store'])
        ->middleware('permission:products.create')->name('products.store');
    Route::get('/products/{product}', [ProductController::class, 'show'])
        ->middleware('permission:products.view')->name('products.show');
    Route::get('/products/{product}/edit', [ProductController::class, 'edit'])
        ->middleware('permission:products.edit')->name('products.edit');
    Route::put('/products/{product}', [ProductController::class, 'update'])
        ->middleware('permission:products.edit')->name('products.update');
    Route::delete('/products/{product}', [ProductController::class, 'destroy'])
        ->middleware('permission:products.delete')->name('products.destroy');

    // Customers
    Route::get('/customers', [CustomerController::class, 'index'])
        ->middleware('permission:customers.view')->name('customers.index');
    Route::get('/customers/create', [CustomerController::class, 'create'])
        ->middleware('permission:customers.create')->name('customers.create');
    Route::post('/customers', [CustomerController::class, 'store'])
        ->middleware('permission:customers.create')->name('customers.store');
    Route::get('/customers/{customer}', [CustomerController::class, 'show'])
        ->middleware('permission:customers.view')->name('customers.show');
    Route::get('/customers/{customer}/edit', [CustomerController::class, 'edit'])
        ->middleware('permission:customers.edit')->name('customers.edit');
    Route::put('/customers/{customer}', [CustomerController::class, 'update'])
        ->middleware('permission:customers.edit')->name('customers.update');
    Route::delete('/customers/{customer}', [CustomerController::class, 'destroy'])
        ->middleware('permission:customers.delete')->name('customers.destroy');

    // AJAX Helper Routes
    Route::get('/get-cities-by-state/{state}', [CustomerController::class, 'getCitiesByState'])
        ->name('get-cities-by-state');

    // Test route for city dropdown functionality
    Route::get('/test-city-dropdown', function() {
        $states = \App\Models\State::where('status', 'active')->orderBy('name')->get();
        return view('test-city-dropdown', compact('states'));
    })->name('test.city.dropdown');

    // Test pagination route
    Route::get('/test-pagination', function() {
        $states = \App\Models\State::paginate(5);
        $cities = \App\Models\City::with('state')->paginate(10);
        return view('test-pagination', compact('states', 'cities'));
    })->name('test.pagination');

    // Companies
    Route::get('/companies', [CompanyController::class, 'index'])
        ->middleware('permission:companies.view')->name('companies.index');
    Route::get('/companies/create', [CompanyController::class, 'create'])
        ->middleware('permission:companies.create')->name('companies.create');
    Route::post('/companies', [CompanyController::class, 'store'])
        ->middleware('permission:companies.create')->name('companies.store');
    Route::get('/companies/{company}', [CompanyController::class, 'show'])
        ->middleware('permission:companies.view')->name('companies.show');
    Route::get('/companies/{company}/edit', [CompanyController::class, 'edit'])
        ->middleware('permission:companies.edit')->name('companies.edit');
    Route::put('/companies/{company}', [CompanyController::class, 'update'])
        ->middleware('permission:companies.edit')->name('companies.update');
    Route::delete('/companies/{company}', [CompanyController::class, 'destroy'])
        ->middleware('permission:companies.delete')->name('companies.destroy');

    // Sales Entries
    Route::get('/entries/sales', [EntryController::class, 'sales'])
        ->middleware('permission:sales.view')->name('entries.sales');
    Route::get('/entries/sales/create', [EntryController::class, 'createSales'])
        ->middleware('permission:sales.create')->name('entries.sales.create');
    Route::get('/entries/sales/export', [EntryController::class, 'exportSales'])
        ->middleware('permission:sales.view')->name('entries.sales.export');
    Route::get('/entries/sales/export-csv', [EntryController::class, 'exportSalesCSV'])
        ->middleware('permission:sales.view')->name('entries.sales.export.csv');
    Route::post('/entries/sales', [EntryController::class, 'storeSales'])
        ->middleware('permission:sales.create')->name('entries.sales.store');
    Route::get('/entries/sales/{id}', [EntryController::class, 'showSales'])
        ->middleware('permission:sales.view')->name('entries.sales.show');
    Route::get('/entries/sales/{id}/edit', [EntryController::class, 'editSales'])
        ->middleware('permission:sales.edit')->name('entries.sales.edit');
    Route::put('/entries/sales/{id}', [EntryController::class, 'updateSales'])
        ->middleware('permission:sales.edit')->name('entries.sales.update');
    Route::delete('/entries/sales/{id}', [EntryController::class, 'deleteSales'])
        ->middleware('permission:sales.delete')->name('entries.sales.destroy');
    Route::get('/entries/sales/{id}/print', [EntryController::class, 'printSalesDynamic'])
        ->middleware('permission:sales.print')->name('entries.sales.print');
    Route::get('/entries/sales/{id}/pdf', [EntryController::class, 'downloadSalesPdf'])
        ->middleware('permission:sales.print')->name('entries.sales.pdf');
    Route::get('/api/next-quotation-number', [EntryController::class, 'getNextQuotationNumber'])
        ->middleware('permission:sales.create')->name('api.next-quotation-number');



    // Simple public routes for sharing (no authentication required)
    Route::get('/entries/sales/{id}/print-dynamic', [EntryController::class, 'printSalesDynamic'])->name('entries.sales.print-dynamic');
    Route::get('/entries/sales/{id}/print-pdf', [EntryController::class, 'downloadSalesForPrint'])->name('entries.sales.print-pdf');

    // Transport sharing routes (public access)
    Route::get('/entries/transport/{id}/view-print', [EntryController::class, 'viewTransportForPrint'])->name('entries.transport.view-print');
    Route::get('/entries/transport/{id}/print-pdf', [EntryController::class, 'downloadTransportForPrint'])->name('entries.transport.print-pdf');



    // Transport Entries
    Route::get('/entries/transport', [EntryController::class, 'transport'])
        ->middleware('permission:transport.view')->name('entries.transport');
    Route::get('/entries/transport/create', [EntryController::class, 'createTransport'])
        ->middleware('permission:transport.create')->name('entries.transport.create');
    Route::get('/entries/transport/export', [EntryController::class, 'exportTransport'])
        ->middleware('permission:transport.view')->name('entries.transport.export');
    Route::get('/entries/transport/export-csv', [EntryController::class, 'exportTransportCSV'])
        ->middleware('permission:transport.view')->name('entries.transport.export.csv');
    Route::post('/entries/transport', [EntryController::class, 'storeTransport'])
        ->middleware('permission:transport.create')->name('entries.transport.store');
    Route::get('/entries/transport/{id}', [EntryController::class, 'showTransport'])
        ->middleware('permission:transport.view')->name('entries.transport.show');
    Route::get('/entries/transport/{id}/edit', [EntryController::class, 'editTransport'])
        ->middleware('permission:transport.edit')->name('entries.transport.edit');
    Route::put('/entries/transport/{id}', [EntryController::class, 'updateTransport'])
        ->middleware('permission:transport.edit')->name('entries.transport.update');
    Route::delete('/entries/transport/{id}', [EntryController::class, 'deleteTransport'])
        ->middleware('permission:transport.delete')->name('entries.transport.destroy');
    Route::patch('/entries/transport/{id}/status', [EntryController::class, 'updateTransportStatus'])
        ->middleware('permission:transport.edit')->name('entries.transport.status');
    Route::get('/entries/transport/{id}/print', [EntryController::class, 'printTransport'])
        ->middleware('permission:transport.print')->name('entries.transport.print');
    Route::get('/entries/transport/{id}/pdf', [EntryController::class, 'downloadTransportPdf'])
        ->middleware('permission:transport.print')->name('entries.transport.pdf');

    // Reports
    Route::get('/reports', [ReportController::class, 'index'])
        ->middleware('permission:reports.view')->name('reports.index');


    Route::get('/reports/daily', [ReportController::class, 'daily'])
        ->middleware('permission:reports.daily')->name('reports.daily');
    Route::get('/reports/monthly', [ReportController::class, 'monthly'])
        ->middleware('permission:reports.monthly')->name('reports.monthly');
    Route::get('/reports/quotation', [ReportController::class, 'quotation'])
        ->middleware('permission:reports.view')->name('reports.quotation');
    Route::get('/reports/sales', [ReportController::class, 'sales'])
        ->middleware('permission:reports.view')->name('reports.sales');
    Route::get('/reports/customer', [ReportController::class, 'customer'])
        ->middleware('permission:reports.view')->name('reports.customer');
    Route::get('/reports/customers', [ReportController::class, 'customers'])
        ->middleware('permission:reports.view')->name('reports.customers');
    Route::get('/reports/products', [ReportController::class, 'products'])
        ->middleware('permission:reports.view')->name('reports.products');

    // User Management
    Route::get('/user-management', [DashboardController::class, 'userManagement'])
        ->middleware('permission:users.view')->name('user.management');

    // Settings
    Route::get('/settings', function () {
        return view('settings.index');
    })->middleware('permission:settings.view')->name('settings');

    // Profile Management
    Route::get('/profile', [ProfileController::class, 'show'])->name('profile.show');
    Route::get('/profile/edit', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::put('/profile', [ProfileController::class, 'update'])->name('profile.update');

    // Refresh Permissions (for testing)
    Route::post('/refresh-permissions', function () {
        if (auth()->check()) {
            auth()->user()->refreshPermissions();
            session(['last_permission_refresh' => time()]);
            return response()->json(['success' => true, 'message' => 'Permissions refreshed successfully']);
        }
        return response()->json(['success' => false, 'message' => 'Not authenticated'], 401);
    })->name('refresh.permissions');

    // Company switching and selection
    Route::get('/select-company', [LoginController::class, 'selectCompany'])->name('company.select');
    Route::post('/set-company', [LoginController::class, 'setCompany'])->name('company.set');
    Route::post('/company/switch', [CompanyController::class, 'switch'])->name('company.switch');

    // Admin Routes (Roles & Permissions)
    Route::prefix('admin')->name('admin.')->group(function () {

        // Roles Management
        Route::get('/roles', [RoleController::class, 'index'])
            ->middleware('permission:roles.view')->name('roles.index');
        Route::get('/roles/create', [RoleController::class, 'create'])
            ->middleware('permission:roles.create')->name('roles.create');
        Route::post('/roles', [RoleController::class, 'store'])
            ->middleware('permission:roles.create')->name('roles.store');
        Route::get('/roles/{role}', [RoleController::class, 'show'])
            ->middleware('permission:roles.view')->name('roles.show');
        Route::get('/roles/{role}/edit', [RoleController::class, 'edit'])
            ->middleware('permission:roles.edit')->name('roles.edit');
        Route::put('/roles/{role}', [RoleController::class, 'update'])
            ->middleware('permission:roles.edit')->name('roles.update');
        Route::delete('/roles/{role}', [RoleController::class, 'destroy'])
            ->middleware('permission:roles.delete')->name('roles.destroy');
        Route::patch('/roles/{role}/toggle-status', [RoleController::class, 'toggleStatus'])
            ->middleware('permission:roles.edit')->name('roles.toggle-status');

        // Permissions Management
        Route::get('/permissions', [PermissionController::class, 'index'])
            ->middleware('permission:permissions.view')->name('permissions.index');
        Route::get('/permissions/create', [PermissionController::class, 'create'])
            ->middleware('permission:permissions.create')->name('permissions.create');
        Route::post('/permissions', [PermissionController::class, 'store'])
            ->middleware('permission:permissions.create')->name('permissions.store');
        Route::get('/permissions/{permission}', [PermissionController::class, 'show'])
            ->middleware('permission:permissions.view')->name('permissions.show');
        Route::get('/permissions/{permission}/edit', [PermissionController::class, 'edit'])
            ->middleware('permission:permissions.edit')->name('permissions.edit');
        Route::put('/permissions/{permission}', [PermissionController::class, 'update'])
            ->middleware('permission:permissions.edit')->name('permissions.update');
        Route::delete('/permissions/{permission}', [PermissionController::class, 'destroy'])
            ->middleware('permission:permissions.delete')->name('permissions.destroy');
        Route::patch('/permissions/{permission}/toggle-status', [PermissionController::class, 'toggleStatus'])
            ->middleware('permission:permissions.edit')->name('permissions.toggle-status');
        Route::post('/permissions/bulk-create', [PermissionController::class, 'bulkCreate'])
            ->middleware('permission:permissions.create')->name('permissions.bulk-create');

        // Permission Checker
        Route::get('/permissions/check', function () {
            return view('admin.permissions.check');
        })->name('permissions.check');
    });

    // API Routes for AJAX
    Route::prefix('api')->name('api.')->group(function () {
        Route::get('/roles', [RoleController::class, 'getRoles'])->name('roles');
        Route::get('/roles/{role}/permissions', [RoleController::class, 'getRolePermissions'])->name('roles.permissions');
    });

    // Master Data Routes
    Route::prefix('masters')->name('masters.')->middleware('permission:menu.masters')->group(function () {

        // Categories Master (with masters prefix)
        Route::get('/categories', [CategoryController::class, 'index'])
            ->middleware('permission:categories.view')->name('categories.index');
        Route::get('/categories/create', [CategoryController::class, 'create'])
            ->middleware('permission:categories.create')->name('categories.create');
        Route::post('/categories', [CategoryController::class, 'store'])
            ->middleware('permission:categories.create')->name('categories.store');
        Route::get('/categories/{category}', [CategoryController::class, 'show'])
            ->middleware('permission:categories.view')->name('categories.show');
        Route::get('/categories/{category}/edit', [CategoryController::class, 'edit'])
            ->middleware('permission:categories.edit')->name('categories.edit');
        Route::put('/categories/{category}', [CategoryController::class, 'update'])
            ->middleware('permission:categories.edit')->name('categories.update');
        Route::delete('/categories/{category}', [CategoryController::class, 'destroy'])
            ->middleware('permission:categories.delete')->name('categories.destroy');

        // Products Master (with masters prefix)
        Route::get('/products', [ProductController::class, 'index'])
            ->middleware('permission:products.view')->name('products.index');
        Route::get('/products/create', [ProductController::class, 'create'])
            ->middleware('permission:products.create')->name('products.create');
        Route::post('/products', [ProductController::class, 'store'])
            ->middleware('permission:products.create')->name('products.store');
        Route::get('/products/{product}', [ProductController::class, 'show'])
            ->middleware('permission:products.view')->name('products.show');
        Route::get('/products/{product}/edit', [ProductController::class, 'edit'])
            ->middleware('permission:products.edit')->name('products.edit');
        Route::put('/products/{product}', [ProductController::class, 'update'])
            ->middleware('permission:products.edit')->name('products.update');
        Route::delete('/products/{product}', [ProductController::class, 'destroy'])
            ->middleware('permission:products.delete')->name('products.destroy');

        // Customers Master (with masters prefix)
        Route::get('/customers', [CustomerController::class, 'index'])
            ->middleware('permission:customers.view')->name('customers.index');
        Route::get('/customers/create', [CustomerController::class, 'create'])
            ->middleware('permission:customers.create')->name('customers.create');
        Route::post('/customers', [CustomerController::class, 'store'])
            ->middleware('permission:customers.create')->name('customers.store');
        Route::get('/customers/{customer}', [CustomerController::class, 'show'])
            ->middleware('permission:customers.view')->name('customers.show');
        Route::get('/customers/{customer}/edit', [CustomerController::class, 'edit'])
            ->middleware('permission:customers.edit')->name('customers.edit');
        Route::put('/customers/{customer}', [CustomerController::class, 'update'])
            ->middleware('permission:customers.edit')->name('customers.update');
        Route::delete('/customers/{customer}', [CustomerController::class, 'destroy'])
            ->middleware('permission:customers.delete')->name('customers.destroy');

        // Users Master (with masters prefix)
        Route::get('/users', [UserMasterController::class, 'index'])
            ->middleware('permission:users.view')->name('users.index');
        Route::get('/users/create', [UserMasterController::class, 'create'])
            ->middleware('permission:users.create')->name('users.create');
        Route::post('/users', [UserMasterController::class, 'store'])
            ->middleware('permission:users.create')->name('users.store');
        Route::get('/users/{user}', [UserMasterController::class, 'show'])
            ->middleware('permission:users.view')->name('users.show');
        Route::get('/users/{user}/edit', [UserMasterController::class, 'edit'])
            ->middleware('permission:users.edit')->name('users.edit');
        Route::put('/users/{user}', [UserMasterController::class, 'update'])
            ->middleware('permission:users.edit')->name('users.update');
        Route::delete('/users/{user}', [UserMasterController::class, 'destroy'])
            ->middleware('permission:users.delete')->name('users.destroy');
        Route::get('/users/export', [UserMasterController::class, 'export'])
            ->middleware('permission:users.view')->name('users.export');
        Route::get('/users/import', [UserMasterController::class, 'importForm'])
            ->middleware('permission:users.create')->name('users.import');
        Route::get('/users/import/template', [UserMasterController::class, 'downloadTemplate'])
            ->middleware('permission:users.create')->name('users.import.template');
        Route::post('/users/import/preview', [UserMasterController::class, 'previewImport'])
            ->middleware('permission:users.create')->name('users.import.preview');
        Route::post('/users/import/process', [UserMasterController::class, 'processImport'])
            ->middleware('permission:users.create')->name('users.import.process');
        Route::post('/users/{user}/toggle-status', [UserMasterController::class, 'toggleStatus'])
            ->middleware('permission:users.edit')->name('users.toggle-status');
        Route::post('/users/bulk-action', [UserMasterController::class, 'bulkAction'])
            ->middleware('permission:users.edit')->name('users.bulk-action');

        // State Master
        Route::get('/state', [MasterController::class, 'state'])
            ->middleware('permission:states.view')->name('state');
        Route::post('/state', [MasterController::class, 'storeState'])
            ->middleware('permission:states.create')->name('state.store');
        Route::put('/state/{id}', [MasterController::class, 'updateState'])
            ->middleware('permission:states.edit')->name('state.update');
        Route::delete('/state/{id}', [MasterController::class, 'deleteState'])
            ->middleware('permission:states.delete')->name('state.delete');

        // City Master
        Route::get('/city', [MasterController::class, 'city'])
            ->middleware('permission:cities.view')->name('city');
        Route::post('/city', [MasterController::class, 'storeCity'])
            ->middleware('permission:cities.create')->name('city.store');
        Route::put('/city/{id}', [MasterController::class, 'updateCity'])
            ->middleware('permission:cities.edit')->name('city.update');
        Route::delete('/city/{id}', [MasterController::class, 'deleteCity'])
            ->middleware('permission:cities.delete')->name('city.delete');




    });
});
