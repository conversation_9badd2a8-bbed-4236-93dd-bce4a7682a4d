<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Customer extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'company_id',
        'name',
        'email',
        'phone',
        'address',
        'state_id',
        'city_id',
        'pincode',
        'gst_number',
        'status'
    ];

    protected $casts = [
        'company_id' => 'integer',
        'state_id' => 'integer',
        'city_id' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the company that owns the customer.
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * Get the state that owns the customer.
     */
    public function state()
    {
        return $this->belongsTo(State::class);
    }

    /**
     * Get the city that owns the customer.
     */
    public function city()
    {
        return $this->belongsTo(City::class);
    }

    /**
     * Get the sales entries for the customer.
     */
    public function salesEntries()
    {
        return $this->hasMany(SalesEntry::class);
    }

    /**
     * Scope a query to only include active customers.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Get the status badge color
     */
    public function getStatusBadgeAttribute()
    {
        return $this->status === 'active' ? 'success' : 'secondary';
    }

    /**
     * Get the full address
     */
    public function getFullAddressAttribute()
    {
        $address = $this->address;
        if ($this->city) {
            $address .= ', ' . $this->city->name;
        }
        if ($this->state) {
            $address .= ', ' . $this->state->name;
        }
        if ($this->pincode) {
            $address .= ' - ' . $this->pincode;
        }
        return $address;
    }

    /**
     * Get total quotations count
     */
    public function getTotalQuotationsAttribute()
    {
        return $this->salesEntries()->count();
    }

    /**
     * Get total approved quotations value
     */
    public function getTotalValueAttribute()
    {
        return $this->salesEntries()->where('status', 'approved')->sum('total_amount');
    }

    /**
     * Get last quotation date
     */
    public function getLastQuotationDateAttribute()
    {
        $lastEntry = $this->salesEntries()->latest('quotation_date')->first();
        return $lastEntry ? $lastEntry->quotation_date : null;
    }
}
