<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'phone',
        'password',
        'status',
        'current_company_id',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the companies that the user belongs to.
     */
    public function companies(): BelongsToMany
    {
        return $this->belongsToMany(Company::class, 'company_users')
                    ->withPivot('role', 'role_id', 'status', 'is_default')
                    ->withTimestamps();
    }

    /**
     * Get the user's current active company.
     */
    public function currentCompany(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'current_company_id');
    }

    /**
     * Get the user's role in the current company.
     * This is now a computed property, not a relationship.
     */
    public function getUserRoleAttribute(): ?Role
    {
        if (!$this->current_company_id) {
            return null;
        }

        // Clear any cached relationships to get fresh data
        $this->unsetRelation('companies');

        return $this->getRoleObjectInCompany($this->current_company_id);
    }

    /**
     * Get the sales entries for the user.
     */
    public function salesEntries()
    {
        return $this->hasMany(SalesEntry::class);
    }

    /**
     * Get the transport entries for the user.
     */
    public function transportEntries()
    {
        return $this->hasMany(TransportEntry::class);
    }



    /**
     * Get the roles assigned to the user in companies.
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'company_users', 'user_id', 'role_id')
                    ->withPivot('company_id', 'status')
                    ->withTimestamps();
    }

    /**
     * Scope a query to only include active users.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include admin users.
     */
    public function scopeAdmins($query)
    {
        return $query->where('role', 'admin');
    }

    /**
     * Scope a query to only include manager users.
     */
    public function scopeManagers($query)
    {
        return $query->where('role', 'manager');
    }

    /**
     * Check if user has access to a specific company.
     */
    public function hasAccessToCompany($companyId): bool
    {
        return $this->companies()->where('companies.id', $companyId)->exists();
    }

    /**
     * Get user's role in a specific company.
     */
    public function getRoleInCompany($companyId): ?string
    {
        $company = $this->companies()->where('companies.id', $companyId)->first();
        return $company ? $company->pivot->role : null;
    }

    /**
     * Switch to a different company.
     */
    public function switchToCompany($companyId): bool
    {
        if ($this->hasAccessToCompany($companyId)) {
            $this->update(['current_company_id' => $companyId]);
            return true;
        }
        return false;
    }

    /**
     * Check if user has a specific role in current company.
     */
    public function hasRole($role, $companyId = null): bool
    {
        $companyId = $companyId ?? $this->current_company_id;

        // Check company-specific roles only
        if ($companyId) {
            $userRole = $this->getRoleInCompany($companyId);
            if ($userRole === $role) {
                return true;
            }

            // Also check by role object
            $roleObject = $this->getRoleObjectInCompany($companyId);
            if ($roleObject && ($roleObject->slug === $role || $roleObject->name === $role)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if user is admin
     */
    public function isAdmin()
    {
        return $this->hasRole('admin') || $this->hasRole('super-admin');
    }

    /**
     * Check if user is super admin
     */
    public function isSuperAdmin()
    {
        return $this->hasRole('super-admin');
    }

    /**
     * Check if user can access all companies (super admin privilege)
     */
    public function canAccessAllCompanies(): bool
    {
        return $this->isSuperAdmin();
    }

    /**
     * Get all companies user can access (either assigned or all if super admin)
     */
    public function getAccessibleCompanies()
    {
        if ($this->canAccessAllCompanies()) {
            return Company::active()->get();
        }

        return $this->getActiveCompanies();
    }

    /**
     * Get company IDs that user can access
     */
    public function getAccessibleCompanyIds(): array
    {
        if ($this->canAccessAllCompanies()) {
            return Company::active()->pluck('id')->toArray();
        }

        return $this->companies()->where('company_users.status', 'active')->pluck('companies.id')->toArray();
    }

    /**
     * Check if user is manager
     */
    public function isManager()
    {
        return $this->hasRole('manager') || $this->isAdmin();
    }

    /**
     * Check if user is regular user
     */
    public function isUser()
    {
        return $this->role === 'user';
    }

    /**
     * Get the status badge color
     */
    public function getStatusBadgeAttribute()
    {
        return $this->status === 'active' ? 'success' : 'secondary';
    }

    /**
     * Get the role badge color
     */
    public function getRoleBadgeAttribute()
    {
        // Use current company role
        $currentRole = $this->userRole;
        if ($currentRole) {
            switch ($currentRole->slug) {
                case 'super-admin':
                    return 'dark';
                case 'admin':
                    return 'danger';
                case 'manager':
                    return 'warning';
                case 'user':
                    return 'primary';
                default:
                    return 'secondary';
            }
        }

        return 'secondary';
    }

    /**
     * Get formatted role name
     */
    public function getFormattedRoleAttribute()
    {
        // Use current company role
        $currentRole = $this->userRole;
        if ($currentRole) {
            return $currentRole->name;
        }

        return 'No Role Assigned';
    }

    /**
     * Get all active companies for the user.
     */
    public function getActiveCompanies()
    {
        return $this->companies()->wherePivot('status', 'active')->get();
    }

    /**
     * Assign user to company with specific role.
     */
    public function assignToCompany($companyId, $roleId, $isDefault = false)
    {
        $role = Role::find($roleId);
        if (!$role) {
            throw new \InvalidArgumentException("Role with ID {$roleId} not found");
        }

        $this->companies()->syncWithoutDetaching([
            $companyId => [
                'role_id' => $roleId,
                'status' => 'active',
                'is_default' => $isDefault,
                'created_at' => now(),
                'updated_at' => now(),
            ]
        ]);

        // Set as current company if it's default or user has no current company
        if ($isDefault || !$this->current_company_id) {
            $this->update(['current_company_id' => $companyId]);
        }

        return $this;
    }

    /**
     * Update user's role in a specific company.
     */
    public function updateCompanyRole($companyId, $roleId)
    {
        $role = Role::find($roleId);
        if (!$role) {
            throw new \InvalidArgumentException("Role with ID {$roleId} not found");
        }

        $this->companies()->updateExistingPivot($companyId, [
            'role_id' => $roleId,
            'updated_at' => now(),
        ]);

        return $this;
    }

    /**
     * Remove user from company.
     */
    public function removeFromCompany($companyId)
    {
        $this->companies()->detach($companyId);

        // If this was the current company, switch to another one
        if ($this->current_company_id == $companyId) {
            $nextCompany = $this->getActiveCompanies()->first();
            $this->update([
                'current_company_id' => $nextCompany ? $nextCompany->id : null
            ]);
        }

        return $this;
    }



    /**
     * Get current company role for display
     */
    public function getCurrentCompanyRole()
    {
        if ($this->current_company_id) {
            return $this->getRoleInCompany($this->current_company_id);
        }
        return null;
    }

    /**
     * Check if user has a specific permission (role-based only).
     */
    public function hasPermission($permission, $companyId = null): bool
    {
        // Super admin always has all permissions
        if ($this->isSuperAdmin()) {
            return true;
        }

        $companyId = $companyId ?? $this->current_company_id;

        // Check company-specific role permissions only
        if ($companyId) {
            $userRole = $this->getRoleObjectInCompany($companyId);
            if ($userRole && $userRole->hasPermission($permission)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if user has any of the given permissions.
     */
    public function hasAnyPermission(array $permissions, $companyId = null): bool
    {
        foreach ($permissions as $permission) {
            if ($this->hasPermission($permission, $companyId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Check if user has all of the given permissions.
     */
    public function hasAllPermissions(array $permissions, $companyId = null): bool
    {
        foreach ($permissions as $permission) {
            if (!$this->hasPermission($permission, $companyId)) {
                return false;
            }
        }
        return true;
    }



    /**
     * Get user's role object in a specific company.
     */
    public function getRoleObjectInCompany($companyId): ?Role
    {
        $company = $this->companies()->where('companies.id', $companyId)->first();
        if (!$company || !$company->pivot->role_id) {
            return null;
        }

        // Always get fresh role data with permissions to avoid caching issues
        return Role::with('permissions')->find($company->pivot->role_id);
    }



    /**
     * Get all permissions for user (role-based only).
     */
    public function getAllPermissions($companyId = null): \Illuminate\Support\Collection
    {
        $companyId = $companyId ?? $this->current_company_id;
        $permissions = collect();

        // Get role-based permissions from company role only
        if ($companyId) {
            $userRole = $this->getRoleObjectInCompany($companyId);
            if ($userRole) {
                $rolePermissions = $userRole->permissions;
                $permissions = $permissions->merge($rolePermissions);
            }
        }

        return $permissions->unique('id');
    }

    /**
     * Check if user can perform action on resource.
     */
    public function can($ability, $arguments = []): bool
    {
        // This integrates with Laravel's Gate system
        return parent::can($ability, $arguments) || $this->hasPermission($ability);
    }

    /**
     * Check if user can access a specific menu.
     */
    public function canAccessMenu($menuSlug): bool
    {
        // Super admin can access all menus
        if ($this->hasRole('super-admin')) {
            return true;
        }

        // Check specific menu permission
        return $this->hasPermission("menu.{$menuSlug}");
    }

    /**
     * Check if user can access any submenu items in a menu.
     */
    public function canAccessAnySubmenuItem($permissions): bool
    {
        if ($this->hasRole('super-admin')) {
            return true;
        }

        foreach ($permissions as $permission) {
            if ($this->hasPermission($permission)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Refresh user's cached permissions and role data.
     */
    public function refreshPermissions(): void
    {
        // Clear all cached relationships
        $this->unsetRelation('companies');
        $this->unsetRelation('currentCompany');

        // Reload the user with fresh relationships
        $this->load(['companies', 'currentCompany']);
    }

    /**
     * Clear permission cache for all users with a specific role.
     */
    public static function refreshPermissionsForRole($roleId): void
    {
        // Get all users who have this role in any company
        $users = self::whereHas('companies', function($query) use ($roleId) {
            $query->where('role_id', $roleId);
        })->get();

        foreach ($users as $user) {
            $user->refreshPermissions();
        }
    }
}
