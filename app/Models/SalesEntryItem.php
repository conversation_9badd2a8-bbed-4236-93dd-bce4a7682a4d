<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SalesEntryItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'sales_entry_id',
        'product_id',
        'hsn_code',
        'gst_percent',
        'packages',
        'unit',
        'rate',
        'loading',
        'gauge_diff',
        'basic_rate',
        'total',
        'taxable_value',
        'description',
        'cgst_percent',
        'cgst_amount',
        'sgst_percent',
        'sgst_amount',
        'igst_percent',
        'igst_amount',
        // Legacy fields for backward compatibility
        'quantity',
        'unit_price',
        'discount',
        'line_total'
    ];

    protected $casts = [
        'sales_entry_id' => 'integer',
        'product_id' => 'integer',
        'gst_percent' => 'decimal:2',
        'packages' => 'integer',
        'rate' => 'decimal:2',
        'loading' => 'decimal:2',
        'gauge_diff' => 'decimal:2',
        'basic_rate' => 'decimal:2',
        'total' => 'decimal:2',
        'taxable_value' => 'decimal:2',
        'cgst_percent' => 'decimal:2',
        'cgst_amount' => 'decimal:2',
        'sgst_percent' => 'decimal:2',
        'sgst_amount' => 'decimal:2',
        'igst_percent' => 'decimal:2',
        'igst_amount' => 'decimal:2',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        // Legacy fields for backward compatibility
        'quantity' => 'integer',
        'unit_price' => 'decimal:2',
        'discount' => 'decimal:2',
        'line_total' => 'decimal:2',
    ];

    /**
     * Get the sales entry that owns the item.
     */
    public function salesEntry()
    {
        return $this->belongsTo(SalesEntry::class);
    }

    /**
     * Get the product that owns the item.
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get formatted unit price
     */
    public function getFormattedUnitPriceAttribute()
    {
        return '₹' . number_format($this->unit_price, 2);
    }

    /**
     * Get formatted line total
     */
    public function getFormattedLineTotalAttribute()
    {
        return '₹' . number_format($this->line_total, 2);
    }

    /**
     * Get discount amount
     */
    public function getDiscountAmountAttribute()
    {
        return ($this->quantity * $this->unit_price) * ($this->discount / 100);
    }

    /**
     * Get formatted discount amount
     */
    public function getFormattedDiscountAmountAttribute()
    {
        return '₹' . number_format($this->discount_amount, 2);
    }
}
