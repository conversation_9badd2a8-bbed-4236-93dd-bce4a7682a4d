<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SalesEntry extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'company_id',
        'customer_id',
        'user_id',
        'invoice_number',
        'invoice_date',
        'payment_terms',
        'party_name',
        'mobile',
        'email',
        'subtotal',
        'total_cgst',
        'total_sgst',
        'total_igst',
        'grand_total',
        'ins_pmt',
        'insurance',
        'frt_advance',
        'tcs_percent',
        'tcs_amount',
        'net_amount',
        'notes',
        'status',
        'share_token',
        'share_expires_at',
        'share_enabled',
        // Legacy fields for backward compatibility
        'quotation_number',
        'quotation_date',
        'valid_until',
        'tax_rate',
        'tax_amount',
        'total_amount'
    ];

    protected $casts = [
        'company_id' => 'integer',
        'customer_id' => 'integer',
        'user_id' => 'integer',
        'invoice_date' => 'date',
        'subtotal' => 'decimal:2',
        'total_cgst' => 'decimal:2',
        'total_sgst' => 'decimal:2',
        'total_igst' => 'decimal:2',
        'grand_total' => 'decimal:2',
        'share_expires_at' => 'datetime',
        'share_enabled' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        // Legacy fields for backward compatibility
        'quotation_date' => 'date',
        'valid_until' => 'date',
        'tax_rate' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
    ];

    /**
     * Get the company that owns the sales entry.
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * Get the customer that owns the sales entry.
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the user that owns the sales entry.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the items for the sales entry.
     */
    public function items()
    {
        return $this->hasMany(SalesEntryItem::class);
    }

    /**
     * Get the transport entries for the sales entry.
     */
    public function transportEntries()
    {
        return $this->hasMany(TransportEntry::class);
    }

    /**
     * Scope a query to only include pending entries.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope a query to only include approved entries.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Get the status badge color
     */
    public function getStatusBadgeAttribute()
    {
        switch ($this->status) {
            case 'approved':
                return 'success';
            case 'pending':
                return 'warning';
            case 'rejected':
                return 'danger';
            case 'draft':
                return 'secondary';
            default:
                return 'secondary';
        }
    }

    /**
     * Get formatted total amount
     */
    public function getFormattedTotalAttribute()
    {
        return '₹' . number_format($this->calculated_net_amount, 2);
    }

    /**
     * Get calculated net amount
     */
    public function getCalculatedNetAmountAttribute()
    {
        // Priority: stored net_amount > calculated from grand_total + charges > total_amount
        if (!empty($this->net_amount) && $this->net_amount > 0) {
            return $this->net_amount;
        } elseif (!empty($this->grand_total) && $this->grand_total > 0) {
            $ins_pmt = $this->ins_pmt ?? 0;
            $insurance = $this->insurance ?? 0; 
            $frt_advance = $this->frt_advance ?? 0;
            $tcs_amount = $this->tcs_amount ?? 0;
            return $this->grand_total + $ins_pmt + $insurance + $frt_advance + $tcs_amount;
        } else {
            return $this->total_amount ?? 0;
        }
    }

    /**
     * Check if quotation is expired
     */
    public function getIsExpiredAttribute()
    {
        return $this->valid_until < now()->toDateString();
    }

    /**
     * Get days until expiry
     */
    public function getDaysUntilExpiryAttribute()
    {
        return now()->diffInDays($this->valid_until, false);
    }

    /**
     * Generate next quotation number for a specific company
     */
    public static function generateQuotationNumber($companyId = null)
    {
        // If no company ID provided, try to get from current user context
        if (!$companyId) {
            $user = auth()->user();
            $companyId = $user ? $user->current_company_id : null;
        }

        if (!$companyId) {
            // Fallback to old system if no company context
            $year = date('Y');
            $lastEntry = self::whereYear('created_at', $year)
                ->orderBy('id', 'desc')
                ->first();

            if ($lastEntry) {
                $lastNumber = (int) substr($lastEntry->quotation_number, -3);
                $newNumber = $lastNumber + 1;
            } else {
                $newNumber = 1;
            }

            return 'QT-' . $year . '-' . str_pad($newNumber, 3, '0', STR_PAD_LEFT);
        }

        // Use company-specific quotation number generation
        $company = \App\Models\Company::find($companyId);
        if ($company) {
            return $company->generateQuotationNumber();
        }

        // Fallback if company not found
        return 'QT-' . date('Y') . '-0001';
    }

    /**
     * Get next quotation number for preview (without incrementing)
     */
    public static function getNextQuotationNumber($companyId = null)
    {
        // If no company ID provided, try to get from current user context
        if (!$companyId) {
            $user = auth()->user();
            $companyId = $user ? $user->current_company_id : null;
        }

        if (!$companyId) {
            // Fallback to old system if no company context
            $year = date('Y');
            $lastEntry = self::whereYear('created_at', $year)
                ->orderBy('id', 'desc')
                ->first();

            if ($lastEntry) {
                $lastNumber = (int) substr($lastEntry->quotation_number, -3);
                $newNumber = $lastNumber + 1;
            } else {
                $newNumber = 1;
            }

            return 'QT-' . $year . '-' . str_pad($newNumber, 3, '0', STR_PAD_LEFT);
        }

        // Use company-specific quotation number generation
        $company = \App\Models\Company::find($companyId);
        if ($company) {
            return $company->getNextQuotationNumber();
        }

        // Fallback if company not found
        return 'QT-' . date('Y') . '-0001';
    }

    /**
     * Generate a secure share token
     */
    public function generateShareToken($expiresInDays = 30)
    {
        $this->share_token = bin2hex(random_bytes(32)); // 64 character random string
        $this->share_expires_at = now()->addDays($expiresInDays);
        $this->share_enabled = true;
        $this->save();

        return $this->share_token;
    }

    /**
     * Get the shareable URL
     */
    public function getShareUrlAttribute()
    {
        if (!$this->share_token) {
            return null;
        }

        return url('/share/' . $this->share_token);
    }

    /**
     * Get the shareable PDF URL
     */
    public function getSharePdfUrlAttribute()
    {
        if (!$this->share_token) {
            return null;
        }

        return url('/share/' . $this->share_token . '.pdf');
    }

    /**
     * Check if share token is valid
     */
    public function isShareTokenValid()
    {
        return $this->share_enabled &&
               $this->share_token &&
               (!$this->share_expires_at || $this->share_expires_at->isFuture());
    }

    /**
     * Find by share token
     */
    public static function findByShareToken($token)
    {
        return self::where('share_token', $token)
                   ->where('share_enabled', true)
                   ->where(function($query) {
                       $query->whereNull('share_expires_at')
                             ->orWhere('share_expires_at', '>', now());
                   })
                   ->first();
    }
}
