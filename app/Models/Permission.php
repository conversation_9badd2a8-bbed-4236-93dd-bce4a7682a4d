<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Permission extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'module',
        'status'
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the roles that have this permission.
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'role_permissions')
                    ->withTimestamps();
    }

    /**
     * Get users who have this permission through their roles.
     * This is a computed relationship, not a direct database relationship.
     */
    public function users()
    {
        $userIds = collect();

        // Get all users who have roles with this permission
        foreach ($this->roles as $role) {
            $roleUserIds = User::where('role_id', $role->id)->pluck('id');
            $userIds = $userIds->merge($roleUserIds);
        }

        // Remove duplicates and return users
        return User::whereIn('id', $userIds->unique());
    }

    /**
     * Get the count of users who have this permission.
     */



    /**
     * Scope a query to only include active permissions.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to filter by module.
     */
    public function scopeByModule($query, $module)
    {
        return $query->where('module', $module);
    }

    /**
     * Get the status badge color
     */
    public function getStatusBadgeAttribute()
    {
        return $this->status === 'active' ? 'success' : 'secondary';
    }

    /**
     * Get formatted permission name
     */
    public function getFormattedNameAttribute()
    {
        return ucwords(str_replace(['_', '-'], ' ', $this->name));
    }
}
