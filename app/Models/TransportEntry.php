<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class TransportEntry extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'sales_entry_id',
        'user_id',
        'customer_id',
        'company_id',
        'transport_number',
        'transport_date',
        'due_days',
        'due_date',
        'freight_cond',
        'way_bill_no',
        'material_direct_delivered_from',
        'lr_no',
        'goods_insured_by',
        'eway_bill_no',
        'doc_through',
        'policy_no',
        'eway_bill_date',
        'driver_name',
        'lc_no',
        'our_offer_no',
        'driver_mob_no',
        'ac_of',
        'cash_address',
        'dl_no',
        'from_city',
        'from_location',
        'export_inv_no',
        'destination',
        'to_city',
        'to_location',
        'export_inv_date',
        'road_permit_no',
        'delivery_1',
        'prepared_by',
        'delivery_2',
        'remarks',
        'gross_wt',
        'tare_wt',
        'net_wt',
        'weight',
        'packages',
        'goods_description',
        'vehicle_number',
        'driver_phone',
        'pickup_address',
        'delivery_address',
        'estimated_delivery',
        'actual_delivery',
        'transport_cost',
        'total',
        'total_amount',
        'ins_pmt',
        'insurance',
        'frt_advance',
        'grand_total',
        'tcs_percent',
        'tcs_amount',
        'net_amount',
        'notes',
        'status'
    ];

    protected $casts = [
        'sales_entry_id' => 'integer',
        'user_id' => 'integer',
        'customer_id' => 'integer',
        'company_id' => 'integer',
        'transport_date' => 'date',
        'due_date' => 'date',
        'eway_bill_date' => 'date',
        'export_inv_date' => 'date',
        'estimated_delivery' => 'date',
        'actual_delivery' => 'date',
        'transport_cost' => 'decimal:2',
        'total' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'ins_pmt' => 'decimal:2',
        'insurance' => 'decimal:2',
        'frt_advance' => 'decimal:2',
        'grand_total' => 'decimal:2',
        'tcs_percent' => 'decimal:2',
        'tcs_amount' => 'decimal:2',
        'net_amount' => 'decimal:2',
        'gross_wt' => 'decimal:2',
        'tare_wt' => 'decimal:2',
        'net_wt' => 'decimal:2',
        'weight' => 'decimal:2',
        'packages' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the sales entry that owns the transport entry.
     */
    public function salesEntry()
    {
        return $this->belongsTo(SalesEntry::class);
    }

    /**
     * Get the sales entry that owns the transport entry (snake_case alias).
     */
    public function sales_entry()
    {
        return $this->salesEntry();
    }

    /**
     * Get the user that owns the transport entry.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the customer that owns the transport entry.
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the company that owns the transport entry.
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * Scope a query to only include scheduled transports.
     */
    public function scopeScheduled($query)
    {
        return $query->where('status', 'scheduled');
    }

    /**
     * Scope a query to only include in transit transports.
     */
    public function scopeInTransit($query)
    {
        return $query->where('status', 'in_transit');
    }

    /**
     * Scope a query to only include delivered transports.
     */
    public function scopeDelivered($query)
    {
        return $query->where('status', 'delivered');
    }

    /**
     * Get the status badge color
     */
    public function getStatusBadgeAttribute()
    {
        switch ($this->status) {
            case 'delivered':
                return 'success';
            case 'in_transit':
                return 'primary';
            case 'scheduled':
                return 'warning';
            case 'cancelled':
                return 'danger';
            default:
                return 'secondary';
        }
    }

    /**
     * Get formatted transport cost
     */
    public function getFormattedCostAttribute()
    {
        return '₹' . number_format($this->transport_cost, 2);
    }

    /**
     * Get calculated net amount
     */
    public function getCalculatedNetAmountAttribute()
    {
        if (!empty($this->net_amount) && $this->net_amount > 0) {
            return $this->net_amount;
        } elseif (!empty($this->grand_total) && $this->grand_total > 0) {
            $ins_pmt = $this->ins_pmt ?? 0;
            $insurance = $this->insurance ?? 0; 
            $frt_advance = $this->frt_advance ?? 0;
            $tcs_amount = $this->tcs_amount ?? 0;
            return $this->grand_total + $ins_pmt + $insurance + $frt_advance + $tcs_amount;
        } else {
            return $this->total ?? 0;
        }
    }

    /**
     * Get formatted total amount
     */
    public function getFormattedTotalAttribute()
    {
        return '₹' . number_format($this->calculated_net_amount, 2);
    }

    /**
     * Check if delivery is delayed
     */
    public function getIsDelayedAttribute()
    {
        if ($this->status === 'delivered' && $this->actual_delivery) {
            // Check if delivered late compared to due_date or estimated_delivery
            $expectedDate = $this->due_date ?? $this->estimated_delivery;
            return $expectedDate && $this->actual_delivery > $expectedDate;
        }
        
        if ($this->status !== 'delivered') {
            // Check if current date is past the due_date or estimated_delivery
            $expectedDate = $this->due_date ?? $this->estimated_delivery;
            return $expectedDate && now()->toDateString() > $expectedDate->format('Y-m-d');
        }
        
        return false;
    }

    /**
     * Get days delayed or remaining
     */
    public function getDeliveryStatusAttribute()
    {
        $expectedDate = $this->due_date ?? $this->estimated_delivery;
        
        if ($this->status === 'delivered' && $this->actual_delivery && $expectedDate) {
            $days = $expectedDate->diffInDays($this->actual_delivery, false);
            if ($days > 0) {
                return $days . ' days late';
            } elseif ($days < 0) {
                return abs($days) . ' days early';
            } else {
                return 'On time';
            }
        }
        
        if ($this->status !== 'delivered' && $expectedDate) {
            $days = now()->diffInDays($expectedDate, false);
            if ($days > 0) {
                return $days . ' days remaining';
            } elseif ($days < 0) {
                return abs($days) . ' days overdue';
            } else {
                return 'Due today';
            }
        }
        
        return 'Unknown';
    }
}
