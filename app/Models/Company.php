<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Company extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'code',
        'quotation_prefix',
        'quotation_start_number',
        'current_quotation_number',
        'email',
        'phone',
        'address',
        'state',
        'city',
        'pincode',
        'gst_number',
        'pan_number',
        'logo',
        'watermark',
        'website',
        'status',
        'settings',
        'pdf_settings',
        'country',
        'cin',
        'mobile',
        'bankname',
        'branch',
        'bankaccount',
        'ifsccode',
        'watermarkName'
    ];

    protected $casts = [
        'settings' => 'array',
        'pdf_settings' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the state that owns the company.
     */
    public function state()
    {
        return $this->belongsTo(State::class);
    }

    /**
     * Get the city that owns the company.
     */
    public function city()
    {
        return $this->belongsTo(City::class);
    }

    /**
     * Get the users for the company.
     */
    public function users()
    {
        return $this->belongsToMany(User::class, 'company_users')
                    ->withPivot('role', 'status', 'is_default')
                    ->withTimestamps();
    }

    /**
     * Get the customers for the company.
     */
    public function customers()
    {
        return $this->hasMany(Customer::class);
    }

    /**
     * Get the sales entries for the company.
     */
    public function salesEntries()
    {
        return $this->hasMany(SalesEntry::class);
    }

    /**
     * Scope a query to only include active companies.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Get the status badge color
     */
    public function getStatusBadgeAttribute()
    {
        return $this->status === 'active' ? 'success' : 'secondary';
    }

    /**
     * Get the full address
     */
    public function getFullAddressAttribute()
    {
        $address = $this->address;
        if ($this->city) {
            $address .= ', ' . $this->city->name;
        }
        if ($this->state) {
            $address .= ', ' . $this->state->name;
        }
        if ($this->pincode) {
            $address .= ' - ' . $this->pincode;
        }
        return $address;
    }

    /**
     * Get the logo URL
     */
    public function getLogoUrlAttribute()
    {
        return $this->logo ? asset('storage/' . $this->logo) : null;
    }

    /**
     * Get the watermark URL
     */
    public function getWatermarkUrlAttribute()
    {
        return $this->watermark ? asset('storage/' . $this->watermark) : null;
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Generate the next quotation number for this company
     */
    public function generateQuotationNumber()
    {
        // Get the prefix (default to company code + /QT if not set)
        $prefix = $this->quotation_prefix;
        if (!$prefix) {
            $prefix = strtoupper($this->code) . '/QT';
        }

        $quotationNumber = $prefix . '/' . str_pad($this->current_quotation_number, 4, '0', STR_PAD_LEFT);

        // Increment the counter for next time
        $this->increment('current_quotation_number');

        return $quotationNumber;
    }

    /**
     * Get the next quotation number without incrementing the counter (for preview)
     */
    public function getNextQuotationNumber()
    {
        // Get the prefix (default to company code + /QT if not set)
        $prefix = $this->quotation_prefix;
        if (!$prefix) {
            $prefix = strtoupper($this->code) . '/QT';
        }

        return $prefix . '/' . str_pad($this->current_quotation_number, 4, '0', STR_PAD_LEFT);
    }
}
