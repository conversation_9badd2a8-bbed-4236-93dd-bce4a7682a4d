<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DatabaseServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Configure MySQL to be more lenient with GROUP BY
        if (config('database.default') === 'mysql') {
            try {
                // Only run this in production or when explicitly enabled
                if (config('database.fix_group_by', false)) {
                    DB::statement("SET sql_mode=(SELECT REPLACE(@@sql_mode,'ONLY_FULL_GROUP_BY',''))");
                }
            } catch (\Exception $e) {
                // Log the error but don't break the application
                Log::warning('Failed to configure MySQL sql_mode: ' . $e->getMessage());
            }
        }
    }
}
