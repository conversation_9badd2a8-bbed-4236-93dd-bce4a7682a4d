<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Blade;
use App\Helpers\PermissionHelper;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register Blade directives for permission checks
        Blade::if('permission', function ($permission, $companyId = null) {
            return PermissionHelper::can($permission, $companyId);
        });

        Blade::if('anypermission', function ($permissions, $companyId = null) {
            return PermissionHelper::canAny($permissions, $companyId);
        });

        Blade::if('allpermissions', function ($permissions, $companyId = null) {
            return PermissionHelper::canAll($permissions, $companyId);
        });

        Blade::if('role', function ($role, $companyId = null) {
            return PermissionHelper::hasRole($role, $companyId);
        });

        Blade::if('hascompany', function () {
            return PermissionHelper::hasCompanyAccess();
        });

        Blade::if('canmenu', function ($menuSlug) {
            return PermissionHelper::canAccessMenu($menuSlug);
        });
    }
}
