<?php

namespace App\Traits;

use App\Models\Company;
use Illuminate\Database\Eloquent\Builder;

trait CompanyDataAccess
{
    /**
     * Apply company filter to query based on user permissions
     */
    protected function applyCompanyFilter(Builder $query, string $companyColumn = 'company_id'): Builder
    {
        $user = auth()->user();
        
        if (!$user) {
            return $query->whereRaw('1 = 0'); // Return no results if not authenticated
        }

        // Super admin can see all company data
        if ($user->canAccessAllCompanies()) {
            return $query; // No filter needed
        }

        // Regular users can only see data from their assigned companies
        $accessibleCompanyIds = $user->getAccessibleCompanyIds();
        
        if (empty($accessibleCompanyIds)) {
            return $query->whereRaw('1 = 0'); // Return no results if no company access
        }

        return $query->whereIn($companyColumn, $accessibleCompanyIds);
    }

    /**
     * Get companies that the current user can access
     */
    protected function getAccessibleCompanies()
    {
        $user = auth()->user();
        
        if (!$user) {
            return collect();
        }

        return $user->getAccessibleCompanies();
    }

    /**
     * Check if user can access specific company data
     */
    protected function canAccessCompanyData($companyId): bool
    {
        $user = auth()->user();
        
        if (!$user) {
            return false;
        }

        // Super admin can access all companies
        if ($user->canAccessAllCompanies()) {
            return true;
        }

        // Check if user has access to this specific company
        return in_array($companyId, $user->getAccessibleCompanyIds());
    }

    /**
     * Ensure user can access the company data or abort
     */
    protected function ensureCompanyAccess($companyId): void
    {
        if (!$this->canAccessCompanyData($companyId)) {
            abort(403, 'You do not have access to this company data.');
        }
    }

    /**
     * Get current company ID for data filtering
     * Returns null for super admin (no filtering needed)
     */
    protected function getCurrentCompanyIdForFilter(): ?int
    {
        $user = auth()->user();
        
        if (!$user) {
            return null;
        }

        // Super admin doesn't need company filtering
        if ($user->canAccessAllCompanies()) {
            return null;
        }

        return $user->current_company_id;
    }

    /**
     * Apply company scope to a model query
     */
    protected function scopeByCompanyAccess($model, string $companyColumn = 'company_id')
    {
        $query = $model::query();
        return $this->applyCompanyFilter($query, $companyColumn);
    }
}
