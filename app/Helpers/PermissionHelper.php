<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Auth;

class PermissionHelper
{
    /**
     * Check if current user has permission
     */
    public static function can($permission, $companyId = null): bool
    {
        if (!Auth::check()) {
            return false;
        }

        return Auth::user()->hasPermission($permission, $companyId);
    }

    /**
     * Check if current user has any of the given permissions
     */
    public static function canAny(array $permissions, $companyId = null): bool
    {
        if (!Auth::check()) {
            return false;
        }

        return Auth::user()->hasAnyPermission($permissions, $companyId);
    }

    /**
     * Check if current user has all of the given permissions
     */
    public static function canAll(array $permissions, $companyId = null): bool
    {
        if (!Auth::check()) {
            return false;
        }

        return Auth::user()->hasAllPermissions($permissions, $companyId);
    }

    /**
     * Check if current user has role
     */
    public static function hasRole($role, $companyId = null): bool
    {
        if (!Auth::check()) {
            return false;
        }

        return Auth::user()->hasRole($role, $companyId);
    }

    /**
     * Check if current user can access menu
     */
    public static function canAccessMenu($menuSlug): bool
    {
        if (!Auth::check()) {
            return false;
        }

        return Auth::user()->canAccessMenu($menuSlug);
    }

    /**
     * Check if current user has access to current company
     */
    public static function hasCompanyAccess(): bool
    {
        if (!Auth::check()) {
            return false;
        }

        $user = Auth::user();
        return $user->current_company_id && $user->hasAccessToCompany($user->current_company_id);
    }

    /**
     * Get current user's permissions for display
     */
    public static function getUserPermissions($companyId = null): array
    {
        if (!Auth::check()) {
            return [];
        }

        return Auth::user()->getAllPermissions($companyId)->pluck('slug')->toArray();
    }

    /**
     * Common permission groups for easy checking
     */
    public static function getPermissionGroups(): array
    {
        return [
            'dashboard' => ['dashboard.view'],
            'users' => ['users.view', 'users.create', 'users.edit', 'users.delete'],
            'customers' => ['customers.view', 'customers.create', 'customers.edit', 'customers.delete'],
            'products' => ['products.view', 'products.create', 'products.edit', 'products.delete'],
            'categories' => ['categories.view', 'categories.create', 'categories.edit', 'categories.delete'],
            'sales' => ['sales.view', 'sales.create', 'sales.edit', 'sales.delete', 'sales.print'],
            'transport' => ['transport.view', 'transport.create', 'transport.edit', 'transport.delete', 'transport.print'],
            'reports' => ['reports.view', 'reports.daily', 'reports.monthly', 'reports.yearly'],
            'administration' => ['roles.view', 'permissions.view', 'companies.view'],
            'settings' => ['settings.view', 'settings.edit'],
        ];
    }

    /**
     * Check if user can access any feature in a group
     */
    public static function canAccessGroup($group): bool
    {
        $groups = self::getPermissionGroups();
        
        if (!isset($groups[$group])) {
            return false;
        }

        return self::canAny($groups[$group]);
    }

    /**
     * Get menu items based on permissions
     */
    public static function getAuthorizedMenuItems(): array
    {
        $menuItems = [];

        // Dashboard
        if (self::can('dashboard.view')) {
            $menuItems['dashboard'] = [
                'title' => 'Dashboard',
                'icon' => 'fas fa-tachometer-alt',
                'route' => 'dashboard',
                'permission' => 'dashboard.view'
            ];
        }

        // Masters Menu
        $mastersSubmenu = [];
        if (self::can('categories.view')) {
            $mastersSubmenu['categories'] = [
                'title' => 'Categories',
                'route' => 'categories.index',
                'permission' => 'categories.view'
            ];
        }
        if (self::can('products.view')) {
            $mastersSubmenu['products'] = [
                'title' => 'Products',
                'route' => 'products.index',
                'permission' => 'products.view'
            ];
        }
        if (self::can('customers.view')) {
            $mastersSubmenu['customers'] = [
                'title' => 'Customers',
                'route' => 'customers.index',
                'permission' => 'customers.view'
            ];
        }
        if (self::can('users.view')) {
            $mastersSubmenu['users'] = [
                'title' => 'Users',
                'route' => 'users.index',
                'permission' => 'users.view'
            ];
        }

        if (!empty($mastersSubmenu)) {
            $menuItems['masters'] = [
                'title' => 'Masters',
                'icon' => 'fas fa-database',
                'submenu' => $mastersSubmenu
            ];
        }

        // Sales Menu
        if (self::canAccessGroup('sales')) {
            $menuItems['sales'] = [
                'title' => 'Sales Entry',
                'icon' => 'fas fa-shopping-cart',
                'route' => 'entries.sales',
                'permission' => 'sales.view'
            ];
        }

        // Transport Menu
        if (self::canAccessGroup('transport')) {
            $menuItems['transport'] = [
                'title' => 'Transport Entry',
                'icon' => 'fas fa-truck',
                'route' => 'entries.transport',
                'permission' => 'transport.view'
            ];
        }

        // Reports Menu
        if (self::canAccessGroup('reports')) {
            $menuItems['reports'] = [
                'title' => 'Reports',
                'icon' => 'fas fa-chart-bar',
                'route' => 'reports.daily',
                'permission' => 'reports.view'
            ];
        }

        // Administration Menu
        $adminSubmenu = [];
        if (self::can('roles.view')) {
            $adminSubmenu['roles'] = [
                'title' => 'Roles Management',
                'route' => 'admin.roles.index',
                'permission' => 'roles.view'
            ];
        }
        if (self::can('permissions.view')) {
            $adminSubmenu['permissions'] = [
                'title' => 'Permissions Management',
                'route' => 'admin.permissions.index',
                'permission' => 'permissions.view'
            ];
        }
        if (self::can('companies.view')) {
            $adminSubmenu['companies'] = [
                'title' => 'Companies Management',
                'route' => 'admin.companies.index',
                'permission' => 'companies.view'
            ];
        }

        if (!empty($adminSubmenu)) {
            $menuItems['administration'] = [
                'title' => 'Administration',
                'icon' => 'fas fa-cogs',
                'submenu' => $adminSubmenu
            ];
        }

        return $menuItems;
    }
}
