<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Role;
use App\Models\Permission;
use App\Models\Company;
use Illuminate\Support\Facades\Hash;

class SetupSuperAdmin extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:setup-super-admin';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Setup <EMAIL> as super admin with all permissions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Setting up super admin user...');

        // Create or update the admin user
        $adminUser = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Admin',
                'password' => Hash::make('admin123'),
                'role' => 'super-admin',
                'status' => 'active',
                'email_verified_at' => now(),
            ]
        );

        // Get or create super-admin role
        $superAdminRole = Role::firstOrCreate(
            ['slug' => 'super-admin'],
            [
                'name' => 'Super Admin',
                'description' => 'Full system access with all permissions',
                'status' => 'active'
            ]
        );

        // Update user with role_id
        $adminUser->update(['role_id' => $superAdminRole->id]);

        // Get all permissions
        $allPermissions = Permission::where('status', 'active')->get();
        
        if ($allPermissions->isEmpty()) {
            $this->warn("No permissions found. Running permission seeder...");
            $this->call('db:seed', ['--class' => 'RolePermissionSeeder']);
            $allPermissions = Permission::where('status', 'active')->get();
        }

        // Assign all permissions to super-admin role
        $superAdminRole->permissions()->sync($allPermissions->pluck('id'));

        // Assign admin to all companies with admin role
        $companies = Company::where('status', 'active')->get();
        
        foreach ($companies as $company) {
            if (!$company->users()->where('user_id', $adminUser->id)->exists()) {
                $company->users()->attach($adminUser->id, [
                    'role' => 'super-admin',
                    'status' => 'active'
                ]);
            } else {
                // Update existing relationship
                $company->users()->updateExistingPivot($adminUser->id, [
                    'role' => 'super-admin',
                    'status' => 'active'
                ]);
            }
        }

        // Set current company if not set
        if (!$adminUser->current_company_id && $companies->isNotEmpty()) {
            $adminUser->update(['current_company_id' => $companies->first()->id]);
        }

        $this->info("✅ Super admin setup completed successfully!");
        $this->line("");
        $this->info("User Details:");
        $this->line("  Email: {$adminUser->email}");
        $this->line("  Name: {$adminUser->name}");
        $this->line("  Role: {$adminUser->role}");
        $this->line("  Status: {$adminUser->status}");
        $this->line("  Total Permissions: " . $allPermissions->count());
        $this->line("  Companies Access: " . $companies->count());
        
        if ($adminUser->current_company_id) {
            $currentCompany = Company::find($adminUser->current_company_id);
            $this->line("  Current Company: {$currentCompany->name}");
        }

        $this->line("");
        $this->info("The admin user now has access to:");
        $this->line("  ✓ All system permissions");
        $this->line("  ✓ All companies");
        $this->line("  ✓ All modules and features");
        $this->line("  ✓ User management");
        $this->line("  ✓ Role and permission management");
        $this->line("  ✓ Company management");
        $this->line("  ✓ All business operations");

        return 0;
    }
}
