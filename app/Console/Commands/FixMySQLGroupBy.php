<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixMySQLGroupBy extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mysql:fix-group-by';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix MySQL ONLY_FULL_GROUP_BY mode by updating sql_mode';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            // Get current SQL mode
            $currentMode = DB::select("SELECT @@sql_mode as mode")[0]->mode;
            $this->info("Current SQL Mode: " . $currentMode);

            // Remove ONLY_FULL_GROUP_BY from sql_mode
            $newMode = str_replace('ONLY_FULL_GROUP_BY,', '', $currentMode);
            $newMode = str_replace(',ONLY_FULL_GROUP_BY', '', $newMode);
            $newMode = str_replace('ONLY_FULL_GROUP_BY', '', $newMode);
            
            // Clean up any double commas
            $newMode = str_replace(',,', ',', $newMode);
            $newMode = trim($newMode, ',');

            // Set the new SQL mode
            DB::statement("SET sql_mode = '$newMode'");
            
            // Verify the change
            $verifyMode = DB::select("SELECT @@sql_mode as mode")[0]->mode;
            $this->info("New SQL Mode: " . $verifyMode);

            if (strpos($verifyMode, 'ONLY_FULL_GROUP_BY') === false) {
                $this->info("✅ Successfully removed ONLY_FULL_GROUP_BY from SQL mode");
                $this->line("");
                $this->warn("Note: This change is temporary and will reset when MySQL restarts.");
                $this->warn("To make it permanent, update your MySQL configuration file (my.cnf):");
                $this->line("sql_mode = \"$newMode\"");
            } else {
                $this->error("❌ Failed to remove ONLY_FULL_GROUP_BY from SQL mode");
            }

        } catch (\Exception $e) {
            $this->error("Error: " . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
