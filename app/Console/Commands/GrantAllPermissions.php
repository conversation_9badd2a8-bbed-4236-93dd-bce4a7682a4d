<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Role;
use App\Models\Permission;

class GrantAllPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:grant-all-permissions {email : The email of the user to grant permissions to}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Grant all permissions to a specific user by making them super-admin (role-based only)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');

        // Find the user
        $user = User::where('email', $email)->first();

        if (!$user) {
            $this->error("User with email '{$email}' not found.");
            return 1;
        }

        // Get or create super-admin role
        $superAdminRole = Role::where('slug', 'super-admin')->first();

        if (!$superAdminRole) {
            $this->error("Super-admin role not found. Please run the database seeders first.");
            return 1;
        }

        // Update user to super-admin
        $user->update([
            'role' => 'super-admin',
            'role_id' => $superAdminRole->id,
            'status' => 'active'
        ]);

        // Get all permissions
        $allPermissions = Permission::where('status', 'active')->get();

        if ($allPermissions->isEmpty()) {
            $this->error("No permissions found. Please run the database seeders first.");
            return 1;
        }

        // Ensure the super-admin role has all permissions
        $permissionIds = $allPermissions->pluck('id')->toArray();
        $superAdminRole->permissions()->sync($permissionIds);

        $this->info("Successfully granted all permissions to user: {$user->name} ({$user->email})");
        $this->info("User role updated to: super-admin");
        $this->info("Total permissions granted via role: " . count($permissionIds));
        $this->info("Note: Permissions are now granted through role-based system only.");

        // Display some key permissions
        $this->line("\nKey permissions granted include:");
        $keyPermissions = $allPermissions->whereIn('module', ['users', 'roles', 'permissions', 'companies'])
                                        ->take(10);

        foreach ($keyPermissions as $permission) {
            $this->line("  - {$permission->name} ({$permission->slug})");
        }

        if ($allPermissions->count() > 10) {
            $this->line("  ... and " . ($allPermissions->count() - 10) . " more permissions");
        }

        return 0;
    }
}
