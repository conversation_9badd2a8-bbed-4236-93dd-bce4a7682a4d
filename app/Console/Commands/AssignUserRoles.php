<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Role;
use App\Models\Company;

class AssignUserRoles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:assign-roles 
                            {--user-id= : Specific user ID to assign role}
                            {--role= : Role slug to assign (super-admin, admin, manager, user)}
                            {--company-id= : Company ID for role assignment}
                            {--all : Assign default roles to all users}
                            {--dry-run : Show what would be done without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Assign roles and permissions to users';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 User Role Assignment Tool');
        $this->info('================================');

        if ($this->option('all')) {
            return $this->assignRolesToAllUsers();
        }

        if ($this->option('user-id')) {
            return $this->assignRoleToSpecificUser();
        }

        // Interactive mode
        return $this->interactiveMode();
    }

    /**
     * Assign roles to all users
     */
    private function assignRolesToAllUsers()
    {
        $users = User::with('companies')->get();
        $defaultRole = Role::where('slug', 'user')->first();
        $adminRole = Role::where('slug', 'admin')->first();

        if (!$defaultRole || !$adminRole) {
            $this->error('❌ Required roles not found. Please run: php artisan db:seed --class=RolePermissionSeeder');
            return 1;
        }

        $this->info("📊 Found {$users->count()} users to process");

        foreach ($users as $user) {
            $this->processUser($user, $defaultRole, $adminRole);
        }

        $this->info('✅ Role assignment completed!');
        return 0;
    }

    /**
     * Assign role to specific user
     */
    private function assignRoleToSpecificUser()
    {
        $userId = $this->option('user-id');
        $roleSlug = $this->option('role');
        $companyId = $this->option('company-id');

        $user = User::find($userId);
        if (!$user) {
            $this->error("❌ User with ID {$userId} not found");
            return 1;
        }

        $role = Role::where('slug', $roleSlug)->first();
        if (!$role) {
            $this->error("❌ Role '{$roleSlug}' not found");
            return 1;
        }

        if ($companyId) {
            $company = Company::find($companyId);
            if (!$company) {
                $this->error("❌ Company with ID {$companyId} not found");
                return 1;
            }

            $this->assignUserToCompanyWithRole($user, $company, $role);
        } else {
            // Assign global role
            $user->update(['role_id' => $role->id]);
            $this->info("✅ Assigned global role '{$role->name}' to user '{$user->name}'");
        }

        return 0;
    }

    /**
     * Interactive mode for role assignment
     */
    private function interactiveMode()
    {
        $this->info('🎯 Interactive Role Assignment Mode');
        
        // Get all users
        $users = User::all();
        $roles = Role::active()->get();
        $companies = Company::all();

        if ($users->isEmpty()) {
            $this->error('❌ No users found');
            return 1;
        }

        if ($roles->isEmpty()) {
            $this->error('❌ No roles found. Please run: php artisan db:seed --class=RolePermissionSeeder');
            return 1;
        }

        // Select user
        $userChoices = $users->mapWithKeys(function ($user) {
            return [$user->id => "{$user->name} ({$user->email})"];
        })->toArray();

        $selectedUserId = $this->choice('Select a user:', $userChoices);
        $selectedUser = $users->find($selectedUserId);

        // Select role
        $roleChoices = $roles->mapWithKeys(function ($role) {
            return [$role->slug => "{$role->name} - {$role->description}"];
        })->toArray();

        $selectedRoleSlug = $this->choice('Select a role:', $roleChoices);
        $selectedRole = $roles->where('slug', $selectedRoleSlug)->first();

        // Ask for company assignment
        if ($companies->isNotEmpty() && $this->confirm('Assign role for a specific company?')) {
            $companyChoices = $companies->mapWithKeys(function ($company) {
                return [$company->id => $company->name];
            })->toArray();

            $selectedCompanyId = $this->choice('Select a company:', $companyChoices);
            $selectedCompany = $companies->find($selectedCompanyId);

            if (!$this->option('dry-run')) {
                $this->assignUserToCompanyWithRole($selectedUser, $selectedCompany, $selectedRole);
            }

            $this->info("✅ Would assign role '{$selectedRole->name}' to user '{$selectedUser->name}' in company '{$selectedCompany->name}'");
        } else {
            // Global role assignment
            if (!$this->option('dry-run')) {
                $selectedUser->update(['role_id' => $selectedRole->id]);
            }

            $this->info("✅ Would assign global role '{$selectedRole->name}' to user '{$selectedUser->name}'");
        }

        return 0;
    }

    /**
     * Process individual user
     */
    private function processUser($user, $defaultRole, $adminRole)
    {
        $this->line("👤 Processing user: {$user->name} ({$user->email})");

        // Assign global role if not set
        if (!$user->role_id) {
            $globalRole = $this->determineGlobalRole($user, $defaultRole, $adminRole);
            
            if (!$this->option('dry-run')) {
                $user->update(['role_id' => $globalRole->id]);
            }
            
            $this->info("   ✅ Assigned global role: {$globalRole->name}");
        } else {
            $this->comment("   ℹ️  Already has global role");
        }

        // Process company assignments
        $companies = Company::all();
        foreach ($companies as $company) {
            $this->processUserCompanyAssignment($user, $company, $defaultRole);
        }
    }

    /**
     * Determine appropriate global role for user
     */
    private function determineGlobalRole($user, $defaultRole, $adminRole)
    {
        // Check if user email suggests admin role
        $adminEmails = ['admin@', 'administrator@', 'superadmin@'];
        
        foreach ($adminEmails as $adminEmail) {
            if (str_contains(strtolower($user->email), $adminEmail)) {
                return $adminRole;
            }
        }

        // Check legacy role field
        if ($user->role === 'admin' || $user->role === 'super-admin') {
            return $adminRole;
        }

        return $defaultRole;
    }

    /**
     * Process user company assignment
     */
    private function processUserCompanyAssignment($user, $company, $defaultRole)
    {
        // Check if user is already assigned to this company
        $existingAssignment = $user->companies()->where('companies.id', $company->id)->first();
        
        if (!$existingAssignment) {
            if (!$this->option('dry-run')) {
                $this->assignUserToCompanyWithRole($user, $company, $defaultRole);
            }
            
            $this->info("   ✅ Assigned to company: {$company->name} with role: {$defaultRole->name}");
        } else {
            $this->comment("   ℹ️  Already assigned to company: {$company->name}");
        }
    }

    /**
     * Assign user to company with specific role
     */
    private function assignUserToCompanyWithRole($user, $company, $role)
    {
        $user->companies()->syncWithoutDetaching([
            $company->id => [
                'role' => $role->slug,
                'role_id' => $role->id,
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ]
        ]);

        // Set as current company if user doesn't have one
        if (!$user->current_company_id) {
            $user->update(['current_company_id' => $company->id]);
        }
    }
}
