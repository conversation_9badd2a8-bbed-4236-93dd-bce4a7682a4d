<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Controllers\ReportController;
use Illuminate\Http\Request;

class TestReports extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:reports';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test all report queries to ensure they work without GROUP BY errors';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing report queries...');
        
        try {
            // Test customer report
            $this->line('Testing customer report...');
            $request = new Request(['sort_by' => 'total_value', 'order' => 'desc']);
            $controller = new ReportController();
            
            // We'll test the query logic directly
            $customerStats = \DB::table('customers')
                ->select([
                    'customers.id',
                    \DB::raw('COUNT(sales_entries.id) as quotation_count'),
                    \DB::raw('SUM(CASE WHEN sales_entries.status = "approved" THEN sales_entries.total_amount ELSE 0 END) as total_value'),
                    \DB::raw('AVG(CASE WHEN sales_entries.status = "approved" THEN sales_entries.total_amount ELSE NULL END) as avg_value'),
                    \DB::raw('MAX(sales_entries.quotation_date) as last_quotation_date')
                ])
                ->leftJoin('sales_entries', 'customers.id', '=', 'sales_entries.customer_id')
                ->whereNull('customers.deleted_at')
                ->groupBy('customers.id')
                ->get();
                
            $this->info('✅ Customer report query successful - ' . $customerStats->count() . ' customers found');

            // Test sales report - top products
            $this->line('Testing sales report - top products...');
            $year = date('Y');
            $productStats = \DB::table('products')
                ->select([
                    'products.id',
                    \DB::raw('SUM(sales_entry_items.quantity) as total_quantity'),
                    \DB::raw('SUM(sales_entry_items.line_total) as total_value')
                ])
                ->leftJoin('sales_entry_items', 'products.id', '=', 'sales_entry_items.product_id')
                ->leftJoin('sales_entries', 'sales_entry_items.sales_entry_id', '=', 'sales_entries.id')
                ->whereYear('sales_entries.quotation_date', $year)
                ->where('sales_entries.status', 'approved')
                ->whereNull('products.deleted_at')
                ->groupBy('products.id')
                ->get();
                
            $this->info('✅ Top products query successful - ' . $productStats->count() . ' products found');

            // Test sales report - sales by customer
            $this->line('Testing sales report - sales by customer...');
            $customerSalesStats = \DB::table('customers')
                ->select([
                    'customers.id',
                    \DB::raw('COUNT(sales_entries.id) as quotation_count'),
                    \DB::raw('SUM(sales_entries.total_amount) as total_value')
                ])
                ->leftJoin('sales_entries', 'customers.id', '=', 'sales_entries.customer_id')
                ->whereYear('sales_entries.quotation_date', $year)
                ->where('sales_entries.status', 'approved')
                ->whereNull('customers.deleted_at')
                ->groupBy('customers.id')
                ->get();
                
            $this->info('✅ Sales by customer query successful - ' . $customerSalesStats->count() . ' customers found');

            // Test monthly breakdown
            $this->line('Testing monthly breakdown...');
            $monthlyBreakdown = \DB::table('sales_entries')
                ->selectRaw('DATE(created_at) as date, COUNT(*) as quotations, SUM(total_amount) as sales_value')
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', date('m'))
                ->groupBy('date')
                ->orderBy('date')
                ->get();
                
            $this->info('✅ Monthly breakdown query successful - ' . $monthlyBreakdown->count() . ' days found');

            $this->line('');
            $this->info('🎉 All report queries executed successfully!');
            $this->info('The GROUP BY issues have been resolved.');

        } catch (\Exception $e) {
            $this->error('❌ Error testing reports: ' . $e->getMessage());
            $this->line('');
            $this->error('Stack trace:');
            $this->line($e->getTraceAsString());
            return 1;
        }

        return 0;
    }
}
