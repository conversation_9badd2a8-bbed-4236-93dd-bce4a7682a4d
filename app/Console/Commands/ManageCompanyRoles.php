<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Role;
use App\Models\Company;

class ManageCompanyRoles extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'company:manage-roles 
                            {--user-id= : User ID}
                            {--company-id= : Company ID}
                            {--role= : Role slug}
                            {--list : List all company role assignments}
                            {--fix : Fix missing role assignments}
                            {--dry-run : Show what would be done without making changes}';

    /**
     * The console command description.
     */
    protected $description = 'Manage user roles across multiple companies';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🏢 Multi-Company Role Management Tool');
        $this->info('====================================');

        if ($this->option('list')) {
            return $this->listCompanyRoles();
        }

        if ($this->option('fix')) {
            return $this->fixMissingRoleAssignments();
        }

        if ($this->option('user-id') && $this->option('company-id') && $this->option('role')) {
            return $this->assignUserToCompanyRole();
        }

        return $this->interactiveMode();
    }

    /**
     * List all company role assignments
     */
    private function listCompanyRoles()
    {
        $this->info('📊 Current Company Role Assignments');
        $this->info('===================================');

        $users = User::with(['companies', 'userRole'])->get();

        foreach ($users as $user) {
            $this->line("👤 {$user->name} ({$user->email})");
            $this->line("   Global Role: " . ($user->userRole ? $user->userRole->name : 'None'));
            $this->line("   Current Company: " . ($user->current_company_id ?: 'None'));

            if ($user->companies->isNotEmpty()) {
                $this->line("   Company Assignments:");
                foreach ($user->companies as $company) {
                    $status = $company->pivot->status === 'active' ? '✅' : '❌';
                    $default = $company->pivot->is_default ? ' (Default)' : '';
                    $roleInfo = $company->pivot->role;
                    if ($company->pivot->role_id) {
                        $role = Role::find($company->pivot->role_id);
                        $roleInfo = $role ? $role->name : $company->pivot->role;
                    }
                    $this->line("     {$status} {$company->name}: {$roleInfo}{$default}");
                }
            } else {
                $this->line("   Company Assignments: None");
            }
            $this->line("");
        }

        return 0;
    }

    /**
     * Fix missing role assignments
     */
    private function fixMissingRoleAssignments()
    {
        $this->info('🔧 Fixing Missing Role Assignments');
        $this->info('=================================');

        $users = User::with(['companies', 'userRole'])->get();
        $companies = Company::all();
        $defaultRole = Role::where('slug', 'user')->first();

        if (!$defaultRole) {
            $this->error('❌ Default user role not found. Please run role seeder first.');
            return 1;
        }

        foreach ($users as $user) {
            $this->line("👤 Processing: {$user->name}");

            // Ensure user has at least one company assignment
            if ($user->companies->isEmpty() && $companies->isNotEmpty()) {
                $firstCompany = $companies->first();
                
                if (!$this->option('dry-run')) {
                    $user->assignToCompany($firstCompany->id, $defaultRole->id, true);
                }
                
                $this->info("   ✅ Assigned to company: {$firstCompany->name}");
            }

            // Fix missing role_id in pivot table
            foreach ($user->companies as $company) {
                if (!$company->pivot->role_id) {
                    $roleSlug = $company->pivot->role;
                    $role = Role::where('slug', $roleSlug)->first() ?? $defaultRole;
                    
                    if (!$this->option('dry-run')) {
                        $user->updateCompanyRole($company->id, $role->id);
                    }
                    
                    $this->info("   ✅ Fixed role_id for {$company->name}: {$role->name}");
                }
            }

            // Set current company if missing
            if (!$user->current_company_id && $user->companies->isNotEmpty()) {
                $firstCompany = $user->companies->first();
                
                if (!$this->option('dry-run')) {
                    $user->update(['current_company_id' => $firstCompany->id]);
                }
                
                $this->info("   ✅ Set current company: {$firstCompany->name}");
            }
        }

        $this->info('✅ Role assignment fixes completed!');
        return 0;
    }

    /**
     * Assign user to company role
     */
    private function assignUserToCompanyRole()
    {
        $userId = $this->option('user-id');
        $companyId = $this->option('company-id');
        $roleSlug = $this->option('role');

        $user = User::find($userId);
        $company = Company::find($companyId);
        $role = Role::where('slug', $roleSlug)->first();

        if (!$user) {
            $this->error("❌ User with ID {$userId} not found");
            return 1;
        }

        if (!$company) {
            $this->error("❌ Company with ID {$companyId} not found");
            return 1;
        }

        if (!$role) {
            $this->error("❌ Role '{$roleSlug}' not found");
            return 1;
        }

        if (!$this->option('dry-run')) {
            $user->assignToCompany($companyId, $role->id);
        }

        $this->info("✅ Assigned {$user->name} to {$company->name} as {$role->name}");
        return 0;
    }

    /**
     * Interactive mode
     */
    private function interactiveMode()
    {
        $this->info('🎯 Interactive Company Role Management');

        $users = User::all();
        $companies = Company::all();
        $roles = Role::all();

        if ($users->isEmpty() || $companies->isEmpty() || $roles->isEmpty()) {
            $this->error('❌ Missing required data. Ensure users, companies, and roles exist.');
            return 1;
        }

        // Select user
        $userChoices = $users->mapWithKeys(function ($user) {
            return [$user->id => "{$user->name} ({$user->email})"];
        })->toArray();

        $userId = $this->choice('Select a user:', $userChoices);
        $user = $users->find($userId);

        // Select company
        $companyChoices = $companies->mapWithKeys(function ($company) {
            return [$company->id => $company->name];
        })->toArray();

        $companyId = $this->choice('Select a company:', $companyChoices);
        $company = $companies->find($companyId);

        // Select role
        $roleChoices = $roles->mapWithKeys(function ($role) {
            return [$role->slug => "{$role->name} - {$role->description}"];
        })->toArray();

        $roleSlug = $this->choice('Select a role:', $roleChoices);
        $role = $roles->where('slug', $roleSlug)->first();

        // Confirm and execute
        if ($this->confirm("Assign {$user->name} to {$company->name} as {$role->name}?")) {
            if (!$this->option('dry-run')) {
                $user->assignToCompany($companyId, $role->id);
            }
            
            $this->info("✅ Assignment completed!");
        }

        return 0;
    }
}
