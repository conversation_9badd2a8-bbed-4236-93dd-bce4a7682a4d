<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\State;
use App\Models\City;
use Illuminate\Support\Facades\DB;

class ImportIndianStatesAndCities extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:indian-states-cities {--force : Force update existing records}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import all Indian states and cities data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🇮🇳 Starting Indian States and Cities Import...');
        $this->newLine();

        $force = $this->option('force');

        if ($force) {
            $this->warn('⚠️  Force mode enabled - existing records will be updated');
        }

        // Start transaction
        DB::beginTransaction();

        try {
            // Import states first
            $this->importStates($force);
            $this->newLine();

            // Import cities
            $this->importCities($force);

            DB::commit();
            
            $this->newLine();
            $this->info('✅ Import completed successfully!');
            $this->displaySummary();

        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('❌ Import failed: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    private function importStates($force)
    {
        $this->info('📍 Importing Indian States...');

        $states = $this->getStatesData();
        $imported = 0;
        $updated = 0;
        $skipped = 0;

        $progressBar = $this->output->createProgressBar(count($states));
        $progressBar->start();

        foreach ($states as $stateData) {
            $existing = State::where('code', $stateData['code'])->first();

            if ($existing) {
                if ($force) {
                    $existing->update($stateData);
                    $updated++;
                } else {
                    $skipped++;
                }
            } else {
                State::create($stateData);
                $imported++;
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();
        $this->info("States: {$imported} imported, {$updated} updated, {$skipped} skipped");
    }

    private function importCities($force)
    {
        $this->info('🏙️  Importing Indian Cities...');

        $cities = $this->getCitiesData();
        $imported = 0;
        $updated = 0;
        $skipped = 0;
        $errors = 0;

        $progressBar = $this->output->createProgressBar(count($cities));
        $progressBar->start();

        foreach ($cities as $cityData) {
            try {
                // Find state by code
                $state = State::where('code', $cityData['state_code'])->first();
                
                if (!$state) {
                    $this->warn("State not found for code: {$cityData['state_code']}");
                    $errors++;
                    $progressBar->advance();
                    continue;
                }

                $cityData['state_id'] = $state->id;
                unset($cityData['state_code']);

                $existing = City::where('name', $cityData['name'])
                                ->where('state_id', $state->id)
                                ->first();

                if ($existing) {
                    if ($force) {
                        $existing->update($cityData);
                        $updated++;
                    } else {
                        $skipped++;
                    }
                } else {
                    City::create($cityData);
                    $imported++;
                }

            } catch (\Exception $e) {
                $this->warn("Error importing city {$cityData['name']}: " . $e->getMessage());
                $errors++;
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();
        $this->info("Cities: {$imported} imported, {$updated} updated, {$skipped} skipped, {$errors} errors");
    }

    private function getStatesData()
    {
        return [
            ['name' => 'Andhra Pradesh', 'code' => 'AP', 'status' => 'active'],
            ['name' => 'Arunachal Pradesh', 'code' => 'AR', 'status' => 'active'],
            ['name' => 'Assam', 'code' => 'AS', 'status' => 'active'],
            ['name' => 'Bihar', 'code' => 'BR', 'status' => 'active'],
            ['name' => 'Chhattisgarh', 'code' => 'CG', 'status' => 'active'],
            ['name' => 'Goa', 'code' => 'GA', 'status' => 'active'],
            ['name' => 'Gujarat', 'code' => 'GJ', 'status' => 'active'],
            ['name' => 'Haryana', 'code' => 'HR', 'status' => 'active'],
            ['name' => 'Himachal Pradesh', 'code' => 'HP', 'status' => 'active'],
            ['name' => 'Jharkhand', 'code' => 'JH', 'status' => 'active'],
            ['name' => 'Karnataka', 'code' => 'KA', 'status' => 'active'],
            ['name' => 'Kerala', 'code' => 'KL', 'status' => 'active'],
            ['name' => 'Madhya Pradesh', 'code' => 'MP', 'status' => 'active'],
            ['name' => 'Maharashtra', 'code' => 'MH', 'status' => 'active'],
            ['name' => 'Manipur', 'code' => 'MN', 'status' => 'active'],
            ['name' => 'Meghalaya', 'code' => 'ML', 'status' => 'active'],
            ['name' => 'Mizoram', 'code' => 'MZ', 'status' => 'active'],
            ['name' => 'Nagaland', 'code' => 'NL', 'status' => 'active'],
            ['name' => 'Odisha', 'code' => 'OR', 'status' => 'active'],
            ['name' => 'Punjab', 'code' => 'PB', 'status' => 'active'],
            ['name' => 'Rajasthan', 'code' => 'RJ', 'status' => 'active'],
            ['name' => 'Sikkim', 'code' => 'SK', 'status' => 'active'],
            ['name' => 'Tamil Nadu', 'code' => 'TN', 'status' => 'active'],
            ['name' => 'Telangana', 'code' => 'TS', 'status' => 'active'],
            ['name' => 'Tripura', 'code' => 'TR', 'status' => 'active'],
            ['name' => 'Uttar Pradesh', 'code' => 'UP', 'status' => 'active'],
            ['name' => 'Uttarakhand', 'code' => 'UK', 'status' => 'active'],
            ['name' => 'West Bengal', 'code' => 'WB', 'status' => 'active'],
            
            // Union Territories
            ['name' => 'Andaman and Nicobar Islands', 'code' => 'AN', 'status' => 'active'],
            ['name' => 'Chandigarh', 'code' => 'CH', 'status' => 'active'],
            ['name' => 'Dadra and Nagar Haveli and Daman and Diu', 'code' => 'DH', 'status' => 'active'],
            ['name' => 'Delhi', 'code' => 'DL', 'status' => 'active'],
            ['name' => 'Jammu and Kashmir', 'code' => 'JK', 'status' => 'active'],
            ['name' => 'Ladakh', 'code' => 'LA', 'status' => 'active'],
            ['name' => 'Lakshadweep', 'code' => 'LD', 'status' => 'active'],
            ['name' => 'Puducherry', 'code' => 'PY', 'status' => 'active'],
        ];
    }

    private function getCitiesData()
    {
        return array_merge(
            $this->getAndhraPradeshCities(),
            $this->getAssamCities(),
            $this->getBiharCities(),
            $this->getChhattisgarh(),
            $this->getGoaCities(),
            $this->getGujaratCities(),
            $this->getHaryanaCities(),
            $this->getHimachalPradeshCities(),
            $this->getJharkhandCities(),
            $this->getKarnatakaCities(),
            $this->getKeralaCities(),
            $this->getMadhyaPradeshCities(),
            $this->getMaharashtraCities(),
            $this->getOdishaCities(),
            $this->getPunjabCities(),
            $this->getRajasthanCities(),
            $this->getTamilNaduCities(),
            $this->getTelanganaCities(),
            $this->getUttarPradeshCities(),
            $this->getUttarakhandCities(),
            $this->getWestBengalCities(),
            $this->getDelhiCities(),
            $this->getOtherStatesCities()
        );
    }

    private function getAndhraPradeshCities()
    {
        return [
            ['name' => 'Visakhapatnam', 'state_code' => 'AP', 'status' => 'active'],
            ['name' => 'Vijayawada', 'state_code' => 'AP', 'status' => 'active'],
            ['name' => 'Guntur', 'state_code' => 'AP', 'status' => 'active'],
            ['name' => 'Nellore', 'state_code' => 'AP', 'status' => 'active'],
            ['name' => 'Kurnool', 'state_code' => 'AP', 'status' => 'active'],
            ['name' => 'Rajahmundry', 'state_code' => 'AP', 'status' => 'active'],
            ['name' => 'Tirupati', 'state_code' => 'AP', 'status' => 'active'],
            ['name' => 'Anantapur', 'state_code' => 'AP', 'status' => 'active'],
            ['name' => 'Chittoor', 'state_code' => 'AP', 'status' => 'active'],
            ['name' => 'Kadapa', 'state_code' => 'AP', 'status' => 'active'],
            ['name' => 'Eluru', 'state_code' => 'AP', 'status' => 'active'],
            ['name' => 'Ongole', 'state_code' => 'AP', 'status' => 'active'],
            ['name' => 'Machilipatnam', 'state_code' => 'AP', 'status' => 'active'],
            ['name' => 'Adoni', 'state_code' => 'AP', 'status' => 'active'],
            ['name' => 'Tenali', 'state_code' => 'AP', 'status' => 'active'],
        ];
    }

    private function getAssamCities()
    {
        return [
            ['name' => 'Guwahati', 'state_code' => 'AS', 'status' => 'active'],
            ['name' => 'Silchar', 'state_code' => 'AS', 'status' => 'active'],
            ['name' => 'Dibrugarh', 'state_code' => 'AS', 'status' => 'active'],
            ['name' => 'Jorhat', 'state_code' => 'AS', 'status' => 'active'],
            ['name' => 'Nagaon', 'state_code' => 'AS', 'status' => 'active'],
            ['name' => 'Tinsukia', 'state_code' => 'AS', 'status' => 'active'],
            ['name' => 'Tezpur', 'state_code' => 'AS', 'status' => 'active'],
            ['name' => 'Diphu', 'state_code' => 'AS', 'status' => 'active'],
            ['name' => 'North Lakhimpur', 'state_code' => 'AS', 'status' => 'active'],
            ['name' => 'Karimganj', 'state_code' => 'AS', 'status' => 'active'],
        ];
    }

    private function getBiharCities()
    {
        return [
            ['name' => 'Patna', 'state_code' => 'BR', 'status' => 'active'],
            ['name' => 'Gaya', 'state_code' => 'BR', 'status' => 'active'],
            ['name' => 'Bhagalpur', 'state_code' => 'BR', 'status' => 'active'],
            ['name' => 'Muzaffarpur', 'state_code' => 'BR', 'status' => 'active'],
            ['name' => 'Purnia', 'state_code' => 'BR', 'status' => 'active'],
            ['name' => 'Darbhanga', 'state_code' => 'BR', 'status' => 'active'],
            ['name' => 'Bihar Sharif', 'state_code' => 'BR', 'status' => 'active'],
            ['name' => 'Arrah', 'state_code' => 'BR', 'status' => 'active'],
            ['name' => 'Begusarai', 'state_code' => 'BR', 'status' => 'active'],
            ['name' => 'Katihar', 'state_code' => 'BR', 'status' => 'active'],
            ['name' => 'Munger', 'state_code' => 'BR', 'status' => 'active'],
            ['name' => 'Chhapra', 'state_code' => 'BR', 'status' => 'active'],
            ['name' => 'Danapur', 'state_code' => 'BR', 'status' => 'active'],
            ['name' => 'Saharsa', 'state_code' => 'BR', 'status' => 'active'],
            ['name' => 'Hajipur', 'state_code' => 'BR', 'status' => 'active'],
        ];
    }

    private function getChhattisgarh()
    {
        return [
            ['name' => 'Raipur', 'state_code' => 'CG', 'status' => 'active'],
            ['name' => 'Bhilai', 'state_code' => 'CG', 'status' => 'active'],
            ['name' => 'Korba', 'state_code' => 'CG', 'status' => 'active'],
            ['name' => 'Bilaspur', 'state_code' => 'CG', 'status' => 'active'],
            ['name' => 'Durg', 'state_code' => 'CG', 'status' => 'active'],
            ['name' => 'Rajnandgaon', 'state_code' => 'CG', 'status' => 'active'],
        ];
    }

    private function getGoaCities()
    {
        return [
            ['name' => 'Panaji', 'state_code' => 'GA', 'status' => 'active'],
            ['name' => 'Margao', 'state_code' => 'GA', 'status' => 'active'],
            ['name' => 'Vasco da Gama', 'state_code' => 'GA', 'status' => 'active'],
            ['name' => 'Mapusa', 'state_code' => 'GA', 'status' => 'active'],
            ['name' => 'Ponda', 'state_code' => 'GA', 'status' => 'active'],
        ];
    }

    private function getGujaratCities()
    {
        return [
            ['name' => 'Ahmedabad', 'state_code' => 'GJ', 'status' => 'active'],
            ['name' => 'Surat', 'state_code' => 'GJ', 'status' => 'active'],
            ['name' => 'Vadodara', 'state_code' => 'GJ', 'status' => 'active'],
            ['name' => 'Rajkot', 'state_code' => 'GJ', 'status' => 'active'],
            ['name' => 'Bhavnagar', 'state_code' => 'GJ', 'status' => 'active'],
            ['name' => 'Jamnagar', 'state_code' => 'GJ', 'status' => 'active'],
            ['name' => 'Junagadh', 'state_code' => 'GJ', 'status' => 'active'],
            ['name' => 'Gandhinagar', 'state_code' => 'GJ', 'status' => 'active'],
            ['name' => 'Anand', 'state_code' => 'GJ', 'status' => 'active'],
            ['name' => 'Navsari', 'state_code' => 'GJ', 'status' => 'active'],
            ['name' => 'Morbi', 'state_code' => 'GJ', 'status' => 'active'],
            ['name' => 'Mehsana', 'state_code' => 'GJ', 'status' => 'active'],
            ['name' => 'Bharuch', 'state_code' => 'GJ', 'status' => 'active'],
            ['name' => 'Vapi', 'state_code' => 'GJ', 'status' => 'active'],
            ['name' => 'Veraval', 'state_code' => 'GJ', 'status' => 'active'],
        ];
    }

    private function getHaryanaCities()
    {
        return [
            ['name' => 'Faridabad', 'state_code' => 'HR', 'status' => 'active'],
            ['name' => 'Gurgaon', 'state_code' => 'HR', 'status' => 'active'],
            ['name' => 'Panipat', 'state_code' => 'HR', 'status' => 'active'],
            ['name' => 'Ambala', 'state_code' => 'HR', 'status' => 'active'],
            ['name' => 'Yamunanagar', 'state_code' => 'HR', 'status' => 'active'],
            ['name' => 'Rohtak', 'state_code' => 'HR', 'status' => 'active'],
            ['name' => 'Hisar', 'state_code' => 'HR', 'status' => 'active'],
            ['name' => 'Karnal', 'state_code' => 'HR', 'status' => 'active'],
            ['name' => 'Sonipat', 'state_code' => 'HR', 'status' => 'active'],
            ['name' => 'Panchkula', 'state_code' => 'HR', 'status' => 'active'],
        ];
    }

    private function getHimachalPradeshCities()
    {
        return [
            ['name' => 'Shimla', 'state_code' => 'HP', 'status' => 'active'],
            ['name' => 'Dharamshala', 'state_code' => 'HP', 'status' => 'active'],
            ['name' => 'Solan', 'state_code' => 'HP', 'status' => 'active'],
            ['name' => 'Mandi', 'state_code' => 'HP', 'status' => 'active'],
            ['name' => 'Palampur', 'state_code' => 'HP', 'status' => 'active'],
            ['name' => 'Baddi', 'state_code' => 'HP', 'status' => 'active'],
            ['name' => 'Nahan', 'state_code' => 'HP', 'status' => 'active'],
            ['name' => 'Paonta Sahib', 'state_code' => 'HP', 'status' => 'active'],
        ];
    }

    private function getJharkhandCities()
    {
        return [
            ['name' => 'Ranchi', 'state_code' => 'JH', 'status' => 'active'],
            ['name' => 'Jamshedpur', 'state_code' => 'JH', 'status' => 'active'],
            ['name' => 'Dhanbad', 'state_code' => 'JH', 'status' => 'active'],
            ['name' => 'Bokaro', 'state_code' => 'JH', 'status' => 'active'],
            ['name' => 'Deoghar', 'state_code' => 'JH', 'status' => 'active'],
            ['name' => 'Phusro', 'state_code' => 'JH', 'status' => 'active'],
            ['name' => 'Hazaribagh', 'state_code' => 'JH', 'status' => 'active'],
            ['name' => 'Giridih', 'state_code' => 'JH', 'status' => 'active'],
            ['name' => 'Ramgarh', 'state_code' => 'JH', 'status' => 'active'],
            ['name' => 'Medininagar', 'state_code' => 'JH', 'status' => 'active'],
        ];
    }

    private function getKarnatakaCities()
    {
        return [
            ['name' => 'Bangalore', 'state_code' => 'KA', 'status' => 'active'],
            ['name' => 'Mysore', 'state_code' => 'KA', 'status' => 'active'],
            ['name' => 'Hubli', 'state_code' => 'KA', 'status' => 'active'],
            ['name' => 'Mangalore', 'state_code' => 'KA', 'status' => 'active'],
            ['name' => 'Belgaum', 'state_code' => 'KA', 'status' => 'active'],
            ['name' => 'Gulbarga', 'state_code' => 'KA', 'status' => 'active'],
            ['name' => 'Davanagere', 'state_code' => 'KA', 'status' => 'active'],
            ['name' => 'Bellary', 'state_code' => 'KA', 'status' => 'active'],
            ['name' => 'Bijapur', 'state_code' => 'KA', 'status' => 'active'],
            ['name' => 'Shimoga', 'state_code' => 'KA', 'status' => 'active'],
            ['name' => 'Tumkur', 'state_code' => 'KA', 'status' => 'active'],
            ['name' => 'Raichur', 'state_code' => 'KA', 'status' => 'active'],
            ['name' => 'Bidar', 'state_code' => 'KA', 'status' => 'active'],
            ['name' => 'Hospet', 'state_code' => 'KA', 'status' => 'active'],
            ['name' => 'Gadag-Betigeri', 'state_code' => 'KA', 'status' => 'active'],
        ];
    }

    private function getKeralaCities()
    {
        return [
            ['name' => 'Thiruvananthapuram', 'state_code' => 'KL', 'status' => 'active'],
            ['name' => 'Kochi', 'state_code' => 'KL', 'status' => 'active'],
            ['name' => 'Kozhikode', 'state_code' => 'KL', 'status' => 'active'],
            ['name' => 'Kollam', 'state_code' => 'KL', 'status' => 'active'],
            ['name' => 'Thrissur', 'state_code' => 'KL', 'status' => 'active'],
            ['name' => 'Alappuzha', 'state_code' => 'KL', 'status' => 'active'],
            ['name' => 'Kottayam', 'state_code' => 'KL', 'status' => 'active'],
            ['name' => 'Palakkad', 'state_code' => 'KL', 'status' => 'active'],
            ['name' => 'Kannur', 'state_code' => 'KL', 'status' => 'active'],
            ['name' => 'Malappuram', 'state_code' => 'KL', 'status' => 'active'],
        ];
    }

    private function getMadhyaPradeshCities()
    {
        return [
            ['name' => 'Indore', 'state_code' => 'MP', 'status' => 'active'],
            ['name' => 'Bhopal', 'state_code' => 'MP', 'status' => 'active'],
            ['name' => 'Jabalpur', 'state_code' => 'MP', 'status' => 'active'],
            ['name' => 'Gwalior', 'state_code' => 'MP', 'status' => 'active'],
            ['name' => 'Ujjain', 'state_code' => 'MP', 'status' => 'active'],
            ['name' => 'Sagar', 'state_code' => 'MP', 'status' => 'active'],
            ['name' => 'Dewas', 'state_code' => 'MP', 'status' => 'active'],
            ['name' => 'Satna', 'state_code' => 'MP', 'status' => 'active'],
            ['name' => 'Ratlam', 'state_code' => 'MP', 'status' => 'active'],
            ['name' => 'Rewa', 'state_code' => 'MP', 'status' => 'active'],
        ];
    }

    private function getMaharashtraCities()
    {
        return [
            ['name' => 'Mumbai', 'state_code' => 'MH', 'status' => 'active'],
            ['name' => 'Pune', 'state_code' => 'MH', 'status' => 'active'],
            ['name' => 'Nagpur', 'state_code' => 'MH', 'status' => 'active'],
            ['name' => 'Thane', 'state_code' => 'MH', 'status' => 'active'],
            ['name' => 'Nashik', 'state_code' => 'MH', 'status' => 'active'],
            ['name' => 'Aurangabad', 'state_code' => 'MH', 'status' => 'active'],
            ['name' => 'Solapur', 'state_code' => 'MH', 'status' => 'active'],
            ['name' => 'Amravati', 'state_code' => 'MH', 'status' => 'active'],
            ['name' => 'Kolhapur', 'state_code' => 'MH', 'status' => 'active'],
            ['name' => 'Sangli', 'state_code' => 'MH', 'status' => 'active'],
            ['name' => 'Malegaon', 'state_code' => 'MH', 'status' => 'active'],
            ['name' => 'Akola', 'state_code' => 'MH', 'status' => 'active'],
            ['name' => 'Latur', 'state_code' => 'MH', 'status' => 'active'],
            ['name' => 'Dhule', 'state_code' => 'MH', 'status' => 'active'],
            ['name' => 'Ahmednagar', 'state_code' => 'MH', 'status' => 'active'],
        ];
    }

    private function getOdishaCities()
    {
        return [
            ['name' => 'Bhubaneswar', 'state_code' => 'OR', 'status' => 'active'],
            ['name' => 'Cuttack', 'state_code' => 'OR', 'status' => 'active'],
            ['name' => 'Rourkela', 'state_code' => 'OR', 'status' => 'active'],
            ['name' => 'Brahmapur', 'state_code' => 'OR', 'status' => 'active'],
            ['name' => 'Sambalpur', 'state_code' => 'OR', 'status' => 'active'],
            ['name' => 'Puri', 'state_code' => 'OR', 'status' => 'active'],
            ['name' => 'Balasore', 'state_code' => 'OR', 'status' => 'active'],
            ['name' => 'Bhadrak', 'state_code' => 'OR', 'status' => 'active'],
            ['name' => 'Baripada', 'state_code' => 'OR', 'status' => 'active'],
        ];
    }

    private function getPunjabCities()
    {
        return [
            ['name' => 'Ludhiana', 'state_code' => 'PB', 'status' => 'active'],
            ['name' => 'Amritsar', 'state_code' => 'PB', 'status' => 'active'],
            ['name' => 'Jalandhar', 'state_code' => 'PB', 'status' => 'active'],
            ['name' => 'Patiala', 'state_code' => 'PB', 'status' => 'active'],
            ['name' => 'Bathinda', 'state_code' => 'PB', 'status' => 'active'],
            ['name' => 'Hoshiarpur', 'state_code' => 'PB', 'status' => 'active'],
            ['name' => 'Batala', 'state_code' => 'PB', 'status' => 'active'],
            ['name' => 'Pathankot', 'state_code' => 'PB', 'status' => 'active'],
            ['name' => 'Moga', 'state_code' => 'PB', 'status' => 'active'],
            ['name' => 'Malerkotla', 'state_code' => 'PB', 'status' => 'active'],
        ];
    }

    private function getRajasthanCities()
    {
        return [
            ['name' => 'Jaipur', 'state_code' => 'RJ', 'status' => 'active'],
            ['name' => 'Jodhpur', 'state_code' => 'RJ', 'status' => 'active'],
            ['name' => 'Kota', 'state_code' => 'RJ', 'status' => 'active'],
            ['name' => 'Bikaner', 'state_code' => 'RJ', 'status' => 'active'],
            ['name' => 'Udaipur', 'state_code' => 'RJ', 'status' => 'active'],
            ['name' => 'Ajmer', 'state_code' => 'RJ', 'status' => 'active'],
            ['name' => 'Bhilwara', 'state_code' => 'RJ', 'status' => 'active'],
            ['name' => 'Alwar', 'state_code' => 'RJ', 'status' => 'active'],
            ['name' => 'Bharatpur', 'state_code' => 'RJ', 'status' => 'active'],
            ['name' => 'Pali', 'state_code' => 'RJ', 'status' => 'active'],
            ['name' => 'Barmer', 'state_code' => 'RJ', 'status' => 'active'],
            ['name' => 'Sikar', 'state_code' => 'RJ', 'status' => 'active'],
            ['name' => 'Tonk', 'state_code' => 'RJ', 'status' => 'active'],
            ['name' => 'Kishangarh', 'state_code' => 'RJ', 'status' => 'active'],
        ];
    }

    private function getTamilNaduCities()
    {
        return [
            ['name' => 'Chennai', 'state_code' => 'TN', 'status' => 'active'],
            ['name' => 'Coimbatore', 'state_code' => 'TN', 'status' => 'active'],
            ['name' => 'Madurai', 'state_code' => 'TN', 'status' => 'active'],
            ['name' => 'Tiruchirappalli', 'state_code' => 'TN', 'status' => 'active'],
            ['name' => 'Salem', 'state_code' => 'TN', 'status' => 'active'],
            ['name' => 'Tirunelveli', 'state_code' => 'TN', 'status' => 'active'],
            ['name' => 'Tiruppur', 'state_code' => 'TN', 'status' => 'active'],
            ['name' => 'Vellore', 'state_code' => 'TN', 'status' => 'active'],
            ['name' => 'Erode', 'state_code' => 'TN', 'status' => 'active'],
            ['name' => 'Thoothukkudi', 'state_code' => 'TN', 'status' => 'active'],
            ['name' => 'Dindigul', 'state_code' => 'TN', 'status' => 'active'],
            ['name' => 'Thanjavur', 'state_code' => 'TN', 'status' => 'active'],
            ['name' => 'Ranipet', 'state_code' => 'TN', 'status' => 'active'],
            ['name' => 'Sivakasi', 'state_code' => 'TN', 'status' => 'active'],
            ['name' => 'Karur', 'state_code' => 'TN', 'status' => 'active'],
        ];
    }

    private function getTelanganaCities()
    {
        return [
            ['name' => 'Hyderabad', 'state_code' => 'TS', 'status' => 'active'],
            ['name' => 'Warangal', 'state_code' => 'TS', 'status' => 'active'],
            ['name' => 'Nizamabad', 'state_code' => 'TS', 'status' => 'active'],
            ['name' => 'Khammam', 'state_code' => 'TS', 'status' => 'active'],
            ['name' => 'Karimnagar', 'state_code' => 'TS', 'status' => 'active'],
            ['name' => 'Ramagundam', 'state_code' => 'TS', 'status' => 'active'],
            ['name' => 'Mahbubnagar', 'state_code' => 'TS', 'status' => 'active'],
            ['name' => 'Nalgonda', 'state_code' => 'TS', 'status' => 'active'],
            ['name' => 'Adilabad', 'state_code' => 'TS', 'status' => 'active'],
            ['name' => 'Suryapet', 'state_code' => 'TS', 'status' => 'active'],
        ];
    }

    private function getUttarPradeshCities()
    {
        return [
            ['name' => 'Lucknow', 'state_code' => 'UP', 'status' => 'active'],
            ['name' => 'Kanpur', 'state_code' => 'UP', 'status' => 'active'],
            ['name' => 'Ghaziabad', 'state_code' => 'UP', 'status' => 'active'],
            ['name' => 'Agra', 'state_code' => 'UP', 'status' => 'active'],
            ['name' => 'Meerut', 'state_code' => 'UP', 'status' => 'active'],
            ['name' => 'Varanasi', 'state_code' => 'UP', 'status' => 'active'],
            ['name' => 'Allahabad', 'state_code' => 'UP', 'status' => 'active'],
            ['name' => 'Bareilly', 'state_code' => 'UP', 'status' => 'active'],
            ['name' => 'Aligarh', 'state_code' => 'UP', 'status' => 'active'],
            ['name' => 'Moradabad', 'state_code' => 'UP', 'status' => 'active'],
            ['name' => 'Saharanpur', 'state_code' => 'UP', 'status' => 'active'],
            ['name' => 'Gorakhpur', 'state_code' => 'UP', 'status' => 'active'],
            ['name' => 'Noida', 'state_code' => 'UP', 'status' => 'active'],
            ['name' => 'Firozabad', 'state_code' => 'UP', 'status' => 'active'],
            ['name' => 'Jhansi', 'state_code' => 'UP', 'status' => 'active'],
        ];
    }

    private function getUttarakhandCities()
    {
        return [
            ['name' => 'Dehradun', 'state_code' => 'UK', 'status' => 'active'],
            ['name' => 'Haridwar', 'state_code' => 'UK', 'status' => 'active'],
            ['name' => 'Roorkee', 'state_code' => 'UK', 'status' => 'active'],
            ['name' => 'Haldwani-cum-Kathgodam', 'state_code' => 'UK', 'status' => 'active'],
            ['name' => 'Rudrapur', 'state_code' => 'UK', 'status' => 'active'],
            ['name' => 'Kashipur', 'state_code' => 'UK', 'status' => 'active'],
            ['name' => 'Rishikesh', 'state_code' => 'UK', 'status' => 'active'],
        ];
    }

    private function getWestBengalCities()
    {
        return [
            ['name' => 'Kolkata', 'state_code' => 'WB', 'status' => 'active'],
            ['name' => 'Howrah', 'state_code' => 'WB', 'status' => 'active'],
            ['name' => 'Durgapur', 'state_code' => 'WB', 'status' => 'active'],
            ['name' => 'Asansol', 'state_code' => 'WB', 'status' => 'active'],
            ['name' => 'Siliguri', 'state_code' => 'WB', 'status' => 'active'],
            ['name' => 'Malda', 'state_code' => 'WB', 'status' => 'active'],
            ['name' => 'Baharampur', 'state_code' => 'WB', 'status' => 'active'],
            ['name' => 'Habra', 'state_code' => 'WB', 'status' => 'active'],
            ['name' => 'Kharagpur', 'state_code' => 'WB', 'status' => 'active'],
            ['name' => 'Shantipur', 'state_code' => 'WB', 'status' => 'active'],
        ];
    }

    private function getDelhiCities()
    {
        return [
            ['name' => 'New Delhi', 'state_code' => 'DL', 'status' => 'active'],
            ['name' => 'Delhi', 'state_code' => 'DL', 'status' => 'active'],
            ['name' => 'Delhi Cantonment', 'state_code' => 'DL', 'status' => 'active'],
        ];
    }

    private function getOtherStatesCities()
    {
        return [
            // Manipur
            ['name' => 'Imphal', 'state_code' => 'MN', 'status' => 'active'],
            ['name' => 'Thoubal', 'state_code' => 'MN', 'status' => 'active'],

            // Meghalaya
            ['name' => 'Shillong', 'state_code' => 'ML', 'status' => 'active'],
            ['name' => 'Tura', 'state_code' => 'ML', 'status' => 'active'],

            // Mizoram
            ['name' => 'Aizawl', 'state_code' => 'MZ', 'status' => 'active'],
            ['name' => 'Lunglei', 'state_code' => 'MZ', 'status' => 'active'],

            // Nagaland
            ['name' => 'Kohima', 'state_code' => 'NL', 'status' => 'active'],
            ['name' => 'Dimapur', 'state_code' => 'NL', 'status' => 'active'],

            // Sikkim
            ['name' => 'Gangtok', 'state_code' => 'SK', 'status' => 'active'],
            ['name' => 'Namchi', 'state_code' => 'SK', 'status' => 'active'],

            // Tripura
            ['name' => 'Agartala', 'state_code' => 'TR', 'status' => 'active'],
            ['name' => 'Dharmanagar', 'state_code' => 'TR', 'status' => 'active'],

            // Union Territories
            ['name' => 'Port Blair', 'state_code' => 'AN', 'status' => 'active'],
            ['name' => 'Chandigarh', 'state_code' => 'CH', 'status' => 'active'],
            ['name' => 'Silvassa', 'state_code' => 'DH', 'status' => 'active'],
            ['name' => 'Daman', 'state_code' => 'DH', 'status' => 'active'],
            ['name' => 'Srinagar', 'state_code' => 'JK', 'status' => 'active'],
            ['name' => 'Jammu', 'state_code' => 'JK', 'status' => 'active'],
            ['name' => 'Leh', 'state_code' => 'LA', 'status' => 'active'],
            ['name' => 'Kavaratti', 'state_code' => 'LD', 'status' => 'active'],
            ['name' => 'Puducherry', 'state_code' => 'PY', 'status' => 'active'],
            ['name' => 'Karaikal', 'state_code' => 'PY', 'status' => 'active'],
        ];
    }

    private function displaySummary()
    {
        $totalStates = State::count();
        $totalCities = City::count();

        $this->info("📊 Final Summary:");
        $this->info("• Total States: {$totalStates}");
        $this->info("• Total Cities: {$totalCities}");
        $this->newLine();
        $this->info("🎉 Indian States and Cities import completed successfully!");
    }
}
