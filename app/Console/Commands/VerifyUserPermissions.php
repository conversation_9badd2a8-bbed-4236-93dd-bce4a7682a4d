<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Permission;

class VerifyUserPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:verify-permissions {email : The email of the user to verify}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Verify and display all permissions for a specific user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        
        // Find the user
        $user = User::where('email', $email)->first();
        
        if (!$user) {
            $this->error("User with email '{$email}' not found.");
            return 1;
        }

        $this->info("User Permission Verification");
        $this->line("================================");
        $this->line("User: {$user->name} ({$user->email})");
        $this->line("Role: {$user->role}");
        $this->line("Status: {$user->status}");
        $this->line("");

        // Get all permissions (role-based only)
        $allPermissions = $user->getAllPermissions();

        // Get role permissions
        $rolePermissions = collect();
        if ($user->role_id) {
            $role = $user->userRole;
            if ($role) {
                $rolePermissions = $role->permissions;
            }
        }

        $this->info("Permission Summary:");
        $this->line("  Total Permissions: " . $allPermissions->count());
        $this->line("  Role-based Permissions: " . $rolePermissions->count());
        $this->line("  Note: Direct user permissions have been removed - only role-based permissions are used.");
        $this->line("");

        // Group permissions by module
        $groupedPermissions = $allPermissions->groupBy('module');
        
        $this->info("Permissions by Module:");
        $this->line("======================");
        
        foreach ($groupedPermissions as $module => $permissions) {
            $this->line("");
            $this->line("📁 " . ucfirst($module ?: 'General') . " ({$permissions->count()} permissions):");
            
            foreach ($permissions as $permission) {
                $source = '';
                if ($rolePermissions->contains('id', $permission->id)) {
                    $source .= '[Role] ';
                }

                $this->line("   ✓ {$permission->name} ({$permission->slug}) {$source}");
            }
        }

        // Check some key permissions
        $this->line("");
        $this->info("Key Permission Checks:");
        $this->line("======================");
        
        $keyChecks = [
            'users.view' => 'View Users',
            'users.create' => 'Create Users',
            'users.edit' => 'Edit Users',
            'users.delete' => 'Delete Users',
            'roles.view' => 'View Roles',
            'roles.create' => 'Create Roles',
            'permissions.view' => 'View Permissions',
            'companies.view' => 'View Companies',
            'dashboard.view' => 'View Dashboard',
            'menu.administration' => 'Access Administration Menu',
        ];

        foreach ($keyChecks as $permission => $description) {
            $hasPermission = $user->hasPermission($permission);
            $status = $hasPermission ? '✅' : '❌';
            $this->line("  {$status} {$description} ({$permission})");
        }

        // Check admin status
        $this->line("");
        $this->info("Admin Status Checks:");
        $this->line("====================");
        $this->line("  Is Admin: " . ($user->isAdmin() ? '✅ Yes' : '❌ No'));
        $this->line("  Has Role 'admin': " . ($user->hasRole('admin') ? '✅ Yes' : '❌ No'));
        $this->line("  Has Role 'super-admin': " . ($user->hasRole('super-admin') ? '✅ Yes' : '❌ No'));

        return 0;
    }
}
