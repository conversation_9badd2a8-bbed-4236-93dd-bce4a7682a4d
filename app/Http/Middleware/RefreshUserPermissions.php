<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;

class RefreshUserPermissions
{
    /**
     * Handle an incoming request.
     * This middleware refreshes user permissions on each request to ensure
     * role/permission changes are applied immediately.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only refresh for authenticated users
        if (Auth::check()) {
            $user = Auth::user();

            // Check if we should refresh permissions
            // You can add conditions here to only refresh when needed
            $shouldRefresh = $request->has('refresh_permissions') ||
                           session()->has('role_updated') ||
                           $this->shouldAutoRefresh();

            if ($shouldRefresh) {
                $user->refreshPermissions();

                // Clear the session flag if it exists
                session()->forget('role_updated');
            }
        }

        return $next($request);
    }

    /**
     * Determine if permissions should be auto-refreshed.
     * You can customize this logic based on your needs.
     */
    private function shouldAutoRefresh(): bool
    {
        // Auto-refresh every 5 minutes for development
        // In production, you might want to disable this or increase the interval
        $lastRefresh = session('last_permission_refresh', 0);
        $refreshInterval = config('app.debug') ? 300 : 3600; // 5 min in debug, 1 hour in production

        return (time() - $lastRefresh) > $refreshInterval;
    }
}
