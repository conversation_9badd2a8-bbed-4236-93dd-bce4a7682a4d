<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;

class RoleOrPermissionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  string  $roleOrPermission
     * @param  string|null  $guard
     */
    public function handle(Request $request, Closure $next, string $roleOrPermission, string $guard = null): Response
    {
        if (!Auth::guard($guard)->check()) {
            return redirect()->route('login');
        }

        $user = Auth::guard($guard)->user();

        // Parse multiple roles/permissions separated by |
        $items = explode('|', $roleOrPermission);
        $hasAccess = false;

        foreach ($items as $item) {
            $item = trim($item);

            // Check if it's a role (starts with role:) or permission
            if (str_starts_with($item, 'role:')) {
                $role = substr($item, 5);
                if ($user->hasRole($role)) {
                    $hasAccess = true;
                    break;
                }
            } else {
                // Treat as permission
                if ($user->hasPermission($item)) {
                    $hasAccess = true;
                    break;
                }
            }
        }

        if (!$hasAccess) {
            abort(403, 'Access denied. You do not have the required role or permission.');
        }

        return $next($request);
    }
}
