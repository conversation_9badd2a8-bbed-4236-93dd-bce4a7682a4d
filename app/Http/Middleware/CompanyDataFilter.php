<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CompanyDataFilter
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (auth()->check()) {
            $user = auth()->user();
            
            // Add company access information to the request
            $request->merge([
                'user_can_access_all_companies' => $user->canAccessAllCompanies(),
                'user_accessible_company_ids' => $user->getAccessibleCompanyIds(),
                'user_current_company_id' => $user->current_company_id,
            ]);
            
            // If user is super admin, they can see all data
            // If regular user, they can only see data from their assigned companies
            if (!$user->canAccessAllCompanies() && empty($user->getAccessibleCompanyIds())) {
                // User has no company access, redirect to access denied
                abort(403, 'You do not have access to any company data. Please contact your administrator.');
            }
        }

        return $next($request);
    }
}
