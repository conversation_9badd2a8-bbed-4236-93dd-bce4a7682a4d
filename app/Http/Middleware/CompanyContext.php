<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\View;

class CompanyContext
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (Auth::check()) {
            $user = Auth::user();

            // If user doesn't have a current company but has companies, set the first one
            if (!$user->current_company_id && $user->companies()->exists()) {
                $firstCompany = $user->companies()->first();
                $user->update(['current_company_id' => $firstCompany->id]);
                $user->refresh();
            }

            // Share current company with all views
            if ($user->currentCompany) {
                View::share('currentCompany', $user->currentCompany);
                View::share('userCompanies', $user->getActiveCompanies());
            }

            // Add company context to request
            $request->merge(['current_company_id' => $user->current_company_id]);
        }

        return $next($request);
    }
}
