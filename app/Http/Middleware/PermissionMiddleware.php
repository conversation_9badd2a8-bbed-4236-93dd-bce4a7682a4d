<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;

class PermissionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  string  $permission
     * @param  string|null  $guard
     */
    public function handle(Request $request, Closure $next, string $permission, string $guard = null): Response
    {
        if (!Auth::guard($guard)->check()) {
            return redirect()->route('login');
        }

        $user = Auth::guard($guard)->user();

        // Ensure user has a current company set
        if (!$user->current_company_id && $user->companies()->exists()) {
            $firstCompany = $user->getActiveCompanies()->first();
            if ($firstCompany) {
                $user->update(['current_company_id' => $firstCompany->id]);
            }
        }

        // Check if user has the required permission in current company context
        if (!$user->hasPermission($permission, $user->current_company_id)) {
            abort(403, 'Access denied. You do not have the required permission: ' . $permission .
                  ' in your current company context.');
        }

        return $next($request);
    }
}
