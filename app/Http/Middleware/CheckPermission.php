<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  string  $permission
     */
    public function handle(Request $request, Closure $next, string $permission): Response
    {
        // Check if user is authenticated
        if (!auth()->check()) {
            return redirect()->route('login')->with('error', 'Please login to access this page.');
        }

        $user = auth()->user();

        // Check if user has company access
        if (!$user->current_company_id) {
            abort(403, 'No company access. Please contact your administrator.');
        }

        // Check if user has the required permission
        if (!$user->hasPermission($permission)) {
            abort(403, "You don't have permission to access this resource. Required permission: {$permission}");
        }

        return $next($request);
    }
}
