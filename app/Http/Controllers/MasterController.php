<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Category;
use App\Models\Product;
use App\Models\Customer;
use App\Models\User;
use App\Models\State;
use App\Models\City;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class MasterController extends Controller
{
    // Group Master Methods
    public function category()
    {
        $categories = Category::paginate(15);
        return view('masters.category', compact('categories'));
    }

    public function storeCategory(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:categories',
            'description' => 'nullable|string',
            'status' => 'required|in:active,inactive'
        ]);

        Category::create($request->all());

        return response()->json([
            'success' => true,
            'message' => 'Category created successfully'
        ]);
    }

    public function updateCategory(Request $request, $id)
    {
        $category = Category::findOrFail($id);
        
        $request->validate([
            'name' => ['required', 'string', 'max:255', Rule::unique('categories')->ignore($id)],
            'description' => 'nullable|string',
            'status' => 'required|in:active,inactive'
        ]);

        $category->update($request->all());

        return response()->json([
            'success' => true,
            'message' => 'Category updated successfully'
        ]);
    }

    public function deleteCategory($id)
    {
        $category = Category::findOrFail($id);
        $category->delete();

        return response()->json([
            'success' => true,
            'message' => 'Category deleted successfully'
        ]);
    }

    // Product Master Methods
    public function product(Request $request)
    {
        $query = Product::with('category');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('hsn_code', 'like', "%{$search}%")
                  ->orWhereHas('category', function($categoryQuery) use ($search) {
                      $categoryQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Category filter
        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Handle per_page parameter
        $perPage = $request->get('per_page', 25); // Default to 25
        $perPage = in_array($perPage, [10, 25, 50, 100]) ? $perPage : 25;

        $products = $query->latest()->paginate($perPage);
        $products->appends($request->query()); // Preserve query parameters

        $categories = Category::where('status', 'active')->get();
        return view('masters.products.index', compact('products', 'categories'));
    }

    public function storeProduct(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
            'price' => 'required|numeric|min:0',
            'stock_quantity' => 'required|integer|min:0',
            'description' => 'nullable|string',
            'status' => 'required|in:active,inactive'
        ]);

        Product::create($request->all());

        return response()->json([
            'success' => true,
            'message' => 'Product created successfully'
        ]);
    }

    public function updateProduct(Request $request, $id)
    {
        $product = Product::findOrFail($id);
        
        $request->validate([
            'name' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
            'sku' => ['required', 'string', Rule::unique('products')->ignore($id)],
            'price' => 'required|numeric|min:0',
            'stock_quantity' => 'required|integer|min:0',
            'description' => 'nullable|string',
            'status' => 'required|in:active,inactive'
        ]);

        $product->update($request->all());

        return response()->json([
            'success' => true,
            'message' => 'Product updated successfully'
        ]);
    }

    public function deleteProduct($id)
    {
        $product = Product::findOrFail($id);
        $product->delete();

        return response()->json([
            'success' => true,
            'message' => 'Product deleted successfully'
        ]);
    }

    // Customer Master Methods
    public function customer()
    {
        $customers = Customer::with(['state', 'city'])->paginate(15);
        $states = State::where('status', 'active')->get();
        return view('masters.customer', compact('customers', 'states'));
    }

    public function storeCustomer(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:customers',
            'phone' => 'required|string|max:15',
            'address' => 'required|string',
            'state_id' => 'required|exists:states,id',
            'city_id' => 'required|exists:cities,id',
            'pincode' => 'required|string|max:10',
            'gst_number' => 'nullable|string|max:15',
            'status' => 'required|in:active,inactive'
        ]);

        Customer::create($request->all());

        return response()->json([
            'success' => true,
            'message' => 'Customer created successfully'
        ]);
    }

    public function updateCustomer(Request $request, $id)
    {
        $customer = Customer::findOrFail($id);
        
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'email', Rule::unique('customers')->ignore($id)],
            'phone' => 'required|string|max:15',
            'address' => 'required|string',
            'state_id' => 'required|exists:states,id',
            'city_id' => 'required|exists:cities,id',
            'pincode' => 'required|string|max:10',
            'gst_number' => 'nullable|string|max:15',
            'status' => 'required|in:active,inactive'
        ]);

        $customer->update($request->all());

        return response()->json([
            'success' => true,
            'message' => 'Customer updated successfully'
        ]);
    }

    public function deleteCustomer($id)
    {
        $customer = Customer::findOrFail($id);
        $customer->delete();

        return response()->json([
            'success' => true,
            'message' => 'Customer deleted successfully'
        ]);
    }

    // User Master Methods
    public function user()
    {
        $users = User::paginate(15);
        return view('masters.user', compact('users'));
    }

    public function storeUser(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'required|in:admin,manager,user',
            'status' => 'required|in:active,inactive'
        ]);

        $data = $request->all();
        $data['password'] = Hash::make($request->password);

        User::create($data);

        return response()->json([
            'success' => true,
            'message' => 'User created successfully'
        ]);
    }

    public function updateUser(Request $request, $id)
    {
        $user = User::findOrFail($id);
        
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'email', Rule::unique('users')->ignore($id)],
            'password' => 'nullable|string|min:8|confirmed',
            'role' => 'required|in:admin,manager,user',
            'status' => 'required|in:active,inactive'
        ]);

        $data = $request->except('password');
        if ($request->filled('password')) {
            $data['password'] = Hash::make($request->password);
        }

        $user->update($data);

        return response()->json([
            'success' => true,
            'message' => 'User updated successfully'
        ]);
    }

    public function deleteUser($id)
    {
        $user = User::findOrFail($id);
        
        if ($user->id === auth()->id()) {
            return response()->json([
                'success' => false,
                'message' => 'You cannot delete your own account'
            ], 400);
        }

        $user->delete();

        return response()->json([
            'success' => true,
            'message' => 'User deleted successfully'
        ]);
    }

    // State Master Methods
    public function state(Request $request)
    {
        $query = State::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%");
            });
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Handle per_page parameter
        $perPage = $request->get('per_page', 25); // Default to 25
        $perPage = in_array($perPage, [10, 25, 50, 100]) ? $perPage : 25;

        $states = $query->latest()->paginate($perPage);
        $states->appends($request->query()); // Preserve query parameters

        return view('masters.state', compact('states'));
    }

    public function storeState(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:states',
            'code' => 'required|string|max:10|unique:states',
            'status' => 'required|in:active,inactive'
        ]);

        State::create($request->all());

        return response()->json([
            'success' => true,
            'message' => 'State created successfully'
        ]);
    }

    public function updateState(Request $request, $id)
    {
        $state = State::findOrFail($id);
        
        $request->validate([
            'name' => ['required', 'string', 'max:255', Rule::unique('states')->ignore($id)],
            'code' => ['required', 'string', 'max:10', Rule::unique('states')->ignore($id)],
            'status' => 'required|in:active,inactive'
        ]);

        $state->update($request->all());

        return response()->json([
            'success' => true,
            'message' => 'State updated successfully'
        ]);
    }

    public function deleteState($id)
    {
        $state = State::findOrFail($id);
        $state->delete();

        return response()->json([
            'success' => true,
            'message' => 'State deleted successfully'
        ]);
    }

    // City Master Methods
    public function city(Request $request)
    {
        $query = City::with('state');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhereHas('state', function($stateQuery) use ($search) {
                      $stateQuery->where('name', 'like', "%{$search}%")
                                 ->orWhere('code', 'like', "%{$search}%");
                  });
            });
        }

        // State filter
        if ($request->filled('state_id')) {
            $query->where('state_id', $request->state_id);
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Handle per_page parameter
        $perPage = $request->get('per_page', 25); // Default to 25
        $perPage = in_array($perPage, [10, 25, 50, 100]) ? $perPage : 25;

        $cities = $query->latest()->paginate($perPage);
        $cities->appends($request->query()); // Preserve query parameters

        $states = State::where('status', 'active')->get();
        return view('masters.city', compact('cities', 'states'));
    }

    public function storeCity(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'state_id' => 'required|exists:states,id',
            'status' => 'required|in:active,inactive'
        ]);

        City::create($request->all());

        return response()->json([
            'success' => true,
            'message' => 'City created successfully'
        ]);
    }

    public function updateCity(Request $request, $id)
    {
        $city = City::findOrFail($id);
        
        $request->validate([
            'name' => 'required|string|max:255',
            'state_id' => 'required|exists:states,id',
            'status' => 'required|in:active,inactive'
        ]);

        $city->update($request->all());

        return response()->json([
            'success' => true,
            'message' => 'City updated successfully'
        ]);
    }

    public function deleteCity($id)
    {
        $city = City::findOrFail($id);
        $city->delete();

        return response()->json([
            'success' => true,
            'message' => 'City deleted successfully'
        ]);
    }
}
