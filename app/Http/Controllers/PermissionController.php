<?php

namespace App\Http\Controllers;

use App\Models\Permission;
use App\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class PermissionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $permissions = Permission::with('roles')
            ->orderBy('module')
            ->orderBy('name')
            ->paginate(15);

        $groupedPermissions = $permissions->getCollection()->groupBy('module');

        return view('admin.permissions.index', compact('permissions', 'groupedPermissions'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $modules = Permission::distinct()->pluck('module')->filter()->sort();
        return view('admin.permissions.create', compact('modules'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'module' => 'nullable|string|max:100',
            'status' => 'required|in:active,inactive'
        ]);

        Permission::create([
            'name' => $request->name,
            'slug' => Str::slug($request->name),
            'description' => $request->description,
            'module' => $request->module,
            'status' => $request->status
        ]);

        return redirect()->route('admin.permissions.index')
            ->with('success', 'Permission created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Permission $permission)
    {
        $permission->load('roles');
        return view('admin.permissions.show', compact('permission'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Permission $permission)
    {
        $modules = Permission::distinct()->pluck('module')->filter()->sort();
        return view('admin.permissions.edit', compact('permission', 'modules'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Permission $permission)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'description' => 'nullable|string',
            'module' => 'nullable|string|max:100',
            'status' => 'required|in:active,inactive'
        ]);

        $permission->update([
            'name' => $request->name,
            'slug' => Str::slug($request->name),
            'description' => $request->description,
            'module' => $request->module,
            'status' => $request->status
        ]);

        return redirect()->route('admin.permissions.index')
            ->with('success', 'Permission updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Permission $permission)
    {
        // Check if permission is being used
        $rolesCount = $permission->roles()->count();
        $usersCount = $permission->users()->count();

        if ($rolesCount > 0 || $usersCount > 0) {
            return redirect()->route('admin.permissions.index')
                ->with('error', 'Cannot delete permission. It is assigned to ' . $rolesCount . ' role(s) and ' . $usersCount . ' user(s).');
        }

        $permission->delete();

        return redirect()->route('admin.permissions.index')
            ->with('success', 'Permission deleted successfully!');
    }

    /**
     * Toggle permission status.
     */
    public function toggleStatus(Permission $permission)
    {
        $permission->update([
            'status' => $permission->status === 'active' ? 'inactive' : 'active'
        ]);

        return redirect()->route('admin.permissions.index')
            ->with('success', 'Permission status updated successfully!');
    }

    /**
     * Bulk create permissions for a module.
     */
    public function bulkCreate(Request $request)
    {
        $request->validate([
            'module' => 'required|string|max:100',
            'actions' => 'required|array|min:1',
            'actions.*' => 'required|string|max:50'
        ]);

        $created = 0;
        foreach ($request->actions as $action) {
            $name = $request->module . ' ' . $action;
            $slug = Str::slug($name);

            // Check if permission already exists
            if (!Permission::where('slug', $slug)->exists()) {
                Permission::create([
                    'name' => $name,
                    'slug' => $slug,
                    'description' => 'Permission to ' . $action . ' ' . $request->module,
                    'module' => $request->module,
                    'status' => 'active'
                ]);
                $created++;
            }
        }

        return redirect()->route('admin.permissions.index')
            ->with('success', $created . ' permissions created successfully!');
    }
}
