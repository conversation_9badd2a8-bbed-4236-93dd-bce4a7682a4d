<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Category;
use App\Models\Product;
use App\Models\Customer;
use App\Models\User;
use App\Models\SalesEntry;
use App\Models\TransportEntry;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * Display the admin dashboard
     */
    public function index()
    {
        $currentMonth = Carbon::now();
        $lastMonth = Carbon::now()->subMonth();

        // Enhanced dashboard statistics
        $stats = [
            'total_quotations' => SalesEntry::count(),
            'pending_quotations' => SalesEntry::where('status', 'pending')->count(),
            'approved_quotations' => SalesEntry::where('status', 'approved')->count(),
            'total_customers' => Customer::count(),
            'total_products' => Product::count(),
            'total_transport' => TransportEntry::count(),
            'monthly_sales' => SalesEntry::whereMonth('created_at', $currentMonth->month)->count(),
            'monthly_revenue' => SalesEntry::whereMonth('created_at', $currentMonth->month)
                ->where('status', 'approved')
                ->selectRaw('SUM(COALESCE(NULLIF(net_amount, 0), NULLIF(grand_total, 0), total_amount)) as total')
                ->value('total') ?? 0,
            'active_users' => User::count(),
            'in_transit_shipments' => TransportEntry::where('status', 'in_transit')->count(),
            'delivered_today' => TransportEntry::where('status', 'delivered')
                ->whereDate('actual_delivery', Carbon::today())->count(),
        ];

        // Calculate growth percentages
        $lastMonthSales = SalesEntry::whereMonth('created_at', $lastMonth->month)->count();
        $lastMonthRevenue = SalesEntry::whereMonth('created_at', $lastMonth->month)
            ->where('status', 'approved')
            ->selectRaw('SUM(COALESCE(NULLIF(net_amount, 0), NULLIF(grand_total, 0), total_amount)) as total')
            ->value('total') ?? 0;

        $stats['sales_growth'] = $lastMonthSales > 0 ?
            (($stats['monthly_sales'] - $lastMonthSales) / $lastMonthSales) * 100 : 0;
        $stats['revenue_growth'] = $lastMonthRevenue > 0 ?
            (($stats['monthly_revenue'] - $lastMonthRevenue) / $lastMonthRevenue) * 100 : 0;

        // Get recent quotations
        $recent_quotations = SalesEntry::with(['customer', 'user', 'company'])
            ->latest()
            ->take(8)
            ->get();

        // Get recent transport entries
        $recent_transports = TransportEntry::with(['customer', 'user', 'company'])
            ->latest()
            ->take(5)
            ->get();

        // Get chart data for sales trend (last 12 months)
        $salesTrendData = [];
        for ($i = 11; $i >= 0; $i--) {
            $month = Carbon::now()->subMonths($i);
            $salesTrendData[] = [
                'month' => $month->format('M Y'),
                'sales' => SalesEntry::whereYear('created_at', $month->year)
                    ->whereMonth('created_at', $month->month)
                    ->count(),
                'revenue' => SalesEntry::whereYear('created_at', $month->year)
                    ->whereMonth('created_at', $month->month)
                    ->where('status', 'approved')
                    ->selectRaw('SUM(COALESCE(NULLIF(net_amount, 0), NULLIF(grand_total, 0), total_amount)) as total')
                    ->value('total') ?? 0
            ];
        }

        // Status distribution for pie chart
        $statusDistribution = [
            'pending' => SalesEntry::where('status', 'pending')->count(),
            'approved' => SalesEntry::where('status', 'approved')->count(),
            'rejected' => SalesEntry::where('status', 'rejected')->count(),
            'draft' => SalesEntry::where('status', 'draft')->count(),
        ];

        // Top customers by revenue
        $topCustomers = Customer::select('customers.*')
            ->selectRaw('SUM(COALESCE(NULLIF(sales_entries.net_amount, 0), NULLIF(sales_entries.grand_total, 0), sales_entries.total_amount)) as total_revenue')
            ->leftJoin('sales_entries', function($join) {
                $join->on('customers.id', '=', 'sales_entries.customer_id')
                     ->where('sales_entries.status', '=', 'approved')
                     ->whereNull('sales_entries.deleted_at');
            })
            ->groupBy('customers.id')
            ->orderBy('total_revenue', 'desc')
            ->take(5)
            ->get();

        return view('dashboard.index', compact(
            'stats',
            'recent_quotations',
            'recent_transports',
            'salesTrendData',
            'statusDistribution',
            'topCustomers'
        ));
    }

    /**
     * Display user management page
     */
    public function userManagement()
    {
        $users = User::paginate(15);
        
        return view('dashboard.user-management', compact('users'));
    }

    /**
     * Display settings page
     */
    public function settings()
    {
        $settings = [
            'company_name' => config('app.name'),
            'company_email' => config('mail.from.address'),
            'timezone' => config('app.timezone'),
            'currency' => 'INR',
            'date_format' => 'd/m/Y',
        ];

        return view('dashboard.settings', compact('settings'));
    }

    /**
     * Get dashboard data for AJAX requests
     */
    public function getDashboardData(Request $request)
    {
        $type = $request->get('type');

        switch ($type) {
            case 'stats':
                return response()->json([
                    'total_quotations' => SalesEntry::count(),
                    'pending_quotations' => SalesEntry::where('status', 'pending')->count(),
                    'total_customers' => Customer::count(),
                    'total_products' => Product::count(),
                ]);

            case 'recent_quotations':
                $quotations = SalesEntry::with(['customer', 'user'])
                    ->latest()
                    ->take(5)
                    ->get();
                return response()->json($quotations);

            case 'notifications':
                // Return notifications count
                $notifications = [
                    'pending_quotations' => SalesEntry::where('status', 'pending')->count(),
                    'low_stock_products' => Product::where('stock_quantity', '<', 10)->count(),
                    'new_customers' => Customer::whereDate('created_at', Carbon::today())->count(),
                ];
                return response()->json($notifications);

            default:
                return response()->json(['error' => 'Invalid request type'], 400);
        }
    }

    /**
     * Update dashboard settings
     */
    public function updateSettings(Request $request)
    {
        $request->validate([
            'company_name' => 'required|string|max:255',
            'company_email' => 'required|email',
            'timezone' => 'required|string',
            'currency' => 'required|string|max:10',
            'date_format' => 'required|string',
        ]);

        // Here you would typically save settings to database
        // For now, we'll just return success
        
        return response()->json([
            'success' => true,
            'message' => 'Settings updated successfully'
        ]);
    }
}
