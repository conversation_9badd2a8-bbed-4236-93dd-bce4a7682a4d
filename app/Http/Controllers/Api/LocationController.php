<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\State;
use App\Models\City;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class LocationController extends Controller
{
    /**
     * Get all active states
     */
    public function getStates(): JsonResponse
    {
        $states = State::active()
            ->orderBy('name')
            ->select('id', 'name', 'code')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $states,
            'message' => 'States retrieved successfully'
        ]);
    }

    /**
     * Get cities by state
     */
    public function getCitiesByState(Request $request, $stateId): JsonResponse
    {
        $request->validate([
            'search' => 'nullable|string|max:255'
        ]);

        $query = City::where('state_id', $stateId)
            ->active()
            ->with('state:id,name,code');

        // Add search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where('name', 'like', "%{$search}%");
        }

        $cities = $query->orderBy('name')
            ->select('id', 'name', 'state_id')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $cities,
            'message' => 'Cities retrieved successfully'
        ]);
    }

    /**
     * Get all cities with state information
     */
    public function getAllCities(Request $request): JsonResponse
    {
        $request->validate([
            'state_id' => 'nullable|exists:states,id',
            'search' => 'nullable|string|max:255',
            'per_page' => 'nullable|integer|min:1|max:100'
        ]);

        $query = City::with('state:id,name,code')->active();

        // Filter by state
        if ($request->filled('state_id')) {
            $query->where('state_id', $request->state_id);
        }

        // Add search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhereHas('state', function($stateQuery) use ($search) {
                      $stateQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        $perPage = $request->get('per_page', 50);
        $cities = $query->orderBy('name')
            ->select('id', 'name', 'state_id')
            ->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $cities->items(),
            'pagination' => [
                'current_page' => $cities->currentPage(),
                'last_page' => $cities->lastPage(),
                'per_page' => $cities->perPage(),
                'total' => $cities->total(),
                'from' => $cities->firstItem(),
                'to' => $cities->lastItem(),
            ],
            'message' => 'Cities retrieved successfully'
        ]);
    }

    /**
     * Get state and city counts
     */
    public function getLocationStats(): JsonResponse
    {
        $stats = [
            'total_states' => State::active()->count(),
            'total_cities' => City::active()->count(),
            'states_with_cities' => State::active()->has('cities')->count(),
            'top_states_by_cities' => State::active()
                ->withCount(['cities' => function($query) {
                    $query->active();
                }])
                ->orderBy('cities_count', 'desc')
                ->limit(10)
                ->get(['id', 'name', 'code', 'cities_count'])
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
            'message' => 'Location statistics retrieved successfully'
        ]);
    }

    /**
     * Search locations (states and cities)
     */
    public function searchLocations(Request $request): JsonResponse
    {
        $request->validate([
            'query' => 'required|string|min:2|max:255'
        ]);

        $searchTerm = $request->query;

        // Search states
        $states = State::active()
            ->where(function($q) use ($searchTerm) {
                $q->where('name', 'like', "%{$searchTerm}%")
                  ->orWhere('code', 'like', "%{$searchTerm}%");
            })
            ->select('id', 'name', 'code')
            ->limit(10)
            ->get()
            ->map(function($state) {
                return [
                    'id' => $state->id,
                    'name' => $state->name,
                    'code' => $state->code,
                    'type' => 'state',
                    'display_name' => $state->name . ' (' . $state->code . ')'
                ];
            });

        // Search cities
        $cities = City::active()
            ->with('state:id,name,code')
            ->where('name', 'like', "%{$searchTerm}%")
            ->select('id', 'name', 'state_id')
            ->limit(20)
            ->get()
            ->map(function($city) {
                return [
                    'id' => $city->id,
                    'name' => $city->name,
                    'state_id' => $city->state_id,
                    'state_name' => $city->state->name,
                    'state_code' => $city->state->code,
                    'type' => 'city',
                    'display_name' => $city->name . ', ' . $city->state->name
                ];
            });

        return response()->json([
            'success' => true,
            'data' => [
                'states' => $states,
                'cities' => $cities,
                'total_results' => $states->count() + $cities->count()
            ],
            'message' => 'Search completed successfully'
        ]);
    }
}
