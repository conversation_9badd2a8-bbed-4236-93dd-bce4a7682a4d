<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Company;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class CompanyController extends Controller
{
    /**
     * Display a listing of companies.
     */
    public function index()
    {
        $companies = Company::with('users')->paginate(10);
        return view('companies.index', compact('companies'));
    }

    /**
     * Show the form for creating a new company.
     */
    public function create()
    {
        return view('companies.create');
    }

    /**
     * Store a newly created company.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:10|unique:companies,code',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:15',
            'address' => 'nullable|string',
            'website' => 'nullable|url',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'watermark' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            // Quotation settings validation
            'quotation_prefix' => 'nullable|string|max:20',
            'quotation_start_number' => 'nullable|integer|min:1',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Map form fields to database fields
        $data = [
            'name' => $request->name,
            'code' => strtoupper($request->code), // Convert to uppercase for consistency
            'email' => $request->email,
            'phone' => $request->phone,
            'address' => $request->address,
            'website' => $request->website,
            'status' => $request->is_active ? 'active' : 'inactive',
            // Quotation settings
            'quotation_prefix' => $request->quotation_prefix,
            'quotation_start_number' => $request->quotation_start_number ?? 2007,
            'current_quotation_number' => $request->quotation_start_number ?? 2007,
        ];

        // Handle logo upload
        if ($request->hasFile('logo')) {
            $logoPath = $request->file('logo')->store('company-logos', 'public');
            $data['logo'] = $logoPath;
        }

        // Handle watermark upload
        if ($request->hasFile('watermark')) {
            $watermarkPath = $request->file('watermark')->store('company-watermarks', 'public');
            $data['watermark'] = $watermarkPath;
        }

        $company = Company::create($data);

        // Add current user as admin of the new company
        $company->users()->attach(Auth::id(), [
            'role' => 'admin',
            'status' => 'active'
        ]);

        return redirect()->route('companies.index')
            ->with('success', 'Company created successfully.');
    }

    /**
     * Display the specified company.
     */
    public function show(Company $company)
    {
        $company->load('users');
        return view('companies.show', compact('company'));
    }

    /**
     * Show the form for editing the specified company.
     */
    public function edit(Company $company)
    {
        return view('companies.edit', compact('company'));
    }

    /**
     * Update the specified company.
     */
    public function update(Request $request, Company $company)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:10|unique:companies,code,' . $company->id,
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:15',
            'address' => 'nullable|string',
            'website' => 'nullable|url',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'watermark' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            // Quotation settings validation
            'quotation_prefix' => 'nullable|string|max:20',
            'quotation_start_number' => 'nullable|integer|min:1',
            'current_quotation_number' => 'nullable|integer|min:1',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Map form fields to database fields
        $data = [
            'name' => $request->name,
            'code' => strtoupper($request->code), // Convert to uppercase for consistency
            'email' => $request->email,
            'phone' => $request->phone,
            'mobile' => $request->mobile,
            'country' => $request->country,
            'state' => $request->state,
            'city' => $request->city,
            'pincode' => $request->pincode,
            'cin' => $request->cin,
            'gst_number' => $request->gst_number,
            'pan_number' => $request->pan_number,
            'bankname' => $request->bankname,
            'branch' => $request->branch,
            'bankaccount' => $request->bankaccount,
            'ifsccode' => $request->ifsccode,
            'address' => $request->address,
            'website' => $request->website,
            'watermarkName'=>$request->watermarkName,
            'status' => $request->is_active ? 'active' : 'inactive',
            // Quotation settings
            'quotation_prefix' => $request->quotation_prefix,
            'quotation_start_number' => $request->quotation_start_number ?? 2007,
            'current_quotation_number' => $request->current_quotation_number ?? $request->quotation_start_number ?? 2007,
        ];

        // Handle logo upload
        if ($request->hasFile('logo')) {
            $logoPath = $request->file('logo')->store('company-logos', 'public');
            $data['logo'] = $logoPath;
        }

        // Handle watermark upload
        if ($request->hasFile('watermark')) {
            $watermarkPath = $request->file('watermark')->store('company-watermarks', 'public');
            $data['watermark'] = $watermarkPath;
        }

        $company->update($data);

        return redirect()->route('companies.index')
            ->with('success', 'Company updated successfully.');
    }

    /**
     * Remove the specified company.
     */
    public function destroy(Company $company)
    {
        $company->delete();
        return redirect()->route('companies.index')
            ->with('success', 'Company deleted successfully.');
    }

    /**
     * Switch to a different company.
     */
    public function switch(Request $request)
    {
        $companyId = $request->input('company_id');
        $user = Auth::user();

        if ($user->switchToCompany($companyId)) {
            return redirect()->back()->with('success', 'Company switched successfully.');
        }

        return redirect()->back()->with('error', 'You do not have access to this company.');
    }
}
