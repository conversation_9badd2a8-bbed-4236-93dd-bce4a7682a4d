<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class ProductController extends Controller
{
    /**
     * Display a listing of the products.
     */
    public function index(Request $request)
    {
        $query = Product::with('category');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('hsn_code', 'like', "%{$search}%")
                  ->orWhereHas('category', function($categoryQuery) use ($search) {
                      $categoryQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Category filter
        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Handle per_page parameter
        $perPage = $request->get('per_page', 25); // Default to 25
        $perPage = in_array($perPage, [10, 25, 50, 100]) ? $perPage : 25;

        $products = $query->orderBy('name','ASC')->paginate($perPage);
        $products->appends($request->query()); // Preserve query parameters

        $categories = Category::all();

        return view('masters.products.index', compact('products', 'categories'));
    }

    /**
     * Show the form for creating a new product.
     */
    public function create()
    {
        $categories = Category::where('status', 'active')->orderBy('name')->get();
        return view('masters.products.create', compact('categories'));
    }

    /**
     * Store a newly created product in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
            'hsn_code' => 'nullable|string|max:20',
            'gst_percentage' => 'required|numeric|min:0|max:100',
            'unit' => 'required|string|max:20',
            'rate' => 'required|numeric|min:0',
            'price' => 'required|numeric|min:0',
            'gauge_diff' => 'nullable|numeric|min:0',
            'stock_quantity' => 'required|integer|min:0',
            'description' => 'nullable|string|max:1000',
            'status' => 'required|in:active,inactive'
        ]);

        $product = Product::create($request->all());

        // Check if this is an AJAX request
        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Product created successfully!',
                'product' => [
                    'id' => $product->id,
                    'name' => $product->name,
                    'hsn_code' => $product->hsn_code,
                    'gst_percentage' => $product->gst_percentage,
                    'unit' => $product->unit,
                    'rate' => $product->rate
                ]
            ]);
        }

        return redirect()->route('products.index')
            ->with('success', 'Product created successfully!');
    }

    /**
     * Display the specified product.
     */
    public function show(Product $product)
    {
        $product->load('category');
        return view('masters.products.show', compact('product'));
    }

    /**
     * Show the form for editing the specified product.
     */
    public function edit(Product $product)
    {
        $categories = Category::where('status', 'active')->orderBy('name')->get();
        return view('masters.products.edit', compact('product', 'categories'));
    }

    /**
     * Update the specified product in storage.
     */
    public function update(Request $request, Product $product)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
            'hsn_code' => 'nullable|string|max:20',
            'gst_percentage' => 'required|numeric|min:0|max:100',
            'unit' => 'required|string|max:20',
            'rate' => 'required|numeric|min:0',
            'price' => 'required|numeric|min:0',
            'gauge_diff' => 'nullable|numeric|min:0',
            'stock_quantity' => 'required|integer|min:0',
            'description' => 'nullable|string|max:1000',
            'status' => 'required|in:active,inactive'
        ]);

        $product->update($request->all());

        return redirect()->route('products.index')
            ->with('success', 'Product updated successfully!');
    }

    /**
     * Remove the specified product from storage.
     */
    public function destroy(Product $product)
    {
        // Check if product is used in any sales entries
        if ($product->salesEntryItems()->count() > 0) {
            return redirect()->route('products.index')
                ->with('error', 'Cannot delete product as it is used in sales entries.');
        }

        $product->delete();

        return redirect()->route('products.index')
            ->with('success', 'Product deleted successfully!');
    }

    /**
     * Toggle product status.
     */
    public function toggleStatus(Product $product)
    {
        $product->update([
            'status' => $product->status === 'active' ? 'inactive' : 'active'
        ]);

        return redirect()->route('products.index')
            ->with('success', 'Product status updated successfully!');
    }
}
