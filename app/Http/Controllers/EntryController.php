<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\SalesEntry;
use App\Models\TransportEntry;
use App\Models\Customer;
use App\Models\Product;
use App\Models\User;
use App\Models\Company;
use App\Traits\CompanyDataAccess;
use App\Exports\SalesExport;
use App\Exports\SimpleSalesExport;
use App\Exports\TransportExport;
use App\Exports\SimpleTransportExport;
use Maatwebsite\Excel\Facades\Excel;
use Dompdf\Dompdf;
use Dompdf\Options;
use Barryvdh\DomPDF\Facade\Pdf;


class EntryController extends Controller
{
    use CompanyDataAccess;
    // Sales Entry Methods
    public function sales(Request $request)
    {
        // Apply company-based filtering for sales entries
        $query = SalesEntry::with(['user', 'items.product', 'company', 'customer']);
        $this->applyCompanyFilter($query);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('quotation_number', 'like', "%{$search}%")
                  ->orWhere('invoice_number', 'like', "%{$search}%")
                  ->orWhere('party_name', 'like', "%{$search}%")
                  ->orWhereHas('customer', function($customerQuery) use ($search) {
                      $customerQuery->where('name', 'like', "%{$search}%")
                                   ->orWhere('email', 'like', "%{$search}%")
                                   ->orWhere('phone', 'like', "%{$search}%");
                  });
            });
        }

        // Customer filter
        if ($request->filled('customer_id')) {
            $query->where('customer_id', $request->customer_id);
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Date range filter
        if ($request->filled('date_from')) {
            $query->where('quotation_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->where('quotation_date', '<=', $request->date_to);
        }

        // Company filter (for super admin)
        if ($request->filled('company_id') && auth()->user()->hasRole('super_admin')) {
            $query->where('company_id', $request->company_id);
        }

        // Handle per_page parameter
        $perPage = $request->get('per_page', 25); // Default to 25
        $perPage = in_array($perPage, [10, 25, 50, 100]) ? $perPage : 25;

        $salesEntries = $query->orderBy('created_at', 'desc')->paginate($perPage);
        $salesEntries->appends($request->query()); // Preserve query parameters

        // Get customers accessible to current user
        $customerQuery = Customer::query();
        $customers = $this->applyCompanyFilter($customerQuery)->get();

        // Get accessible companies for filter dropdown (if super admin)
        $accessibleCompanies = $this->getAccessibleCompanies();

        // Calculate statistics with company filtering
        $statsQuery = SalesEntry::query();
        $this->applyCompanyFilter($statsQuery);

        $stats = [
            'total' => (clone $statsQuery)->count(),
            'pending' => (clone $statsQuery)->where('status', 'pending')->count(),
            'approved' => (clone $statsQuery)->where('status', 'approved')->count(),
            'total_value' => (clone $statsQuery)->where('status', 'approved')->sum('grand_total') ?: (clone $statsQuery)->where('status', 'approved')->sum('total_amount')
        ];

        return view('entries.sales.index', compact('salesEntries', 'customers', 'stats', 'accessibleCompanies'));
    }

    public function createSales()
    {
        // Get customers and products accessible to current user
        $customerQuery = Customer::query();
        $customers = $this->applyCompanyFilter($customerQuery)->get();

        $productQuery = Product::query();
        $products = $productQuery->get();
        $states = \App\Models\State::where('status', 'active')->orderBy('name')->get();
        $categories = \App\Models\Category::where('status', 'active')->orderBy('name')->get();

        // Get next quotation number for preview
        $nextQuotationNumber = SalesEntry::getNextQuotationNumber(auth()->user()->current_company_id);

        return view('entries.sales.create_new', compact('customers', 'products', 'states', 'categories', 'nextQuotationNumber'));
    }

    /**
     * Get next quotation number via AJAX
     */
    public function getNextQuotationNumber()
    {
        $companyId = auth()->user()->current_company_id;
        $nextNumber = SalesEntry::getNextQuotationNumber($companyId);

        return response()->json([
            'quotation_number' => $nextNumber
        ]);
    }

    public function storeSales(Request $request)
    {
        // Debug: Log incoming request data
        \Log::info('Sales Entry Store Request:', [
            'all_data' => $request->all(),
            'products' => $request->input('products'),
            'user_id' => auth()->id(),
            'user_check' => auth()->check()
        ]);

        $request->validate([
            'invoice_no' => 'required|string|unique:sales_entries,invoice_number|unique:sales_entries,quotation_number',
            'invoice_date' => 'required|date',
            'payment_terms' => 'required|string',
            'billed_to' => 'nullable|exists:customers,id',
            'party_name' => 'required|string|max:255',
            'mobile' => 'required|string|max:15',
            'email' => 'nullable|email|max:255',
            'products' => 'required|array|min:1',
            'products.*.product_id' => 'required|exists:products,id',
            'products.*.hsn_code' => 'nullable|string',
            'products.*.gst_percent' => 'required|numeric|min:0|max:100',
            'products.*.packages' => 'required|integer|min:1',
            'products.*.unit' => 'required|string',
            'products.*.rate' => 'required|numeric|min:0',
            'products.*.loading' => 'nullable|numeric|min:0',
            'products.*.basic_rate' => 'required|numeric|min:0',
            'products.*.total' => 'required|numeric|min:0',
            'products.*.taxable_value' => 'required|numeric|min:0',
            'products.*.description' => 'nullable|string',
            'products.*.cgst_percent' => 'required|numeric|min:0',
            'products.*.cgst_amount' => 'required|numeric|min:0',
            'products.*.sgst_percent' => 'required|numeric|min:0',
            'products.*.sgst_amount' => 'required|numeric|min:0',
            'products.*.igst_percent' => 'required|numeric|min:0',
            'products.*.igst_amount' => 'required|numeric|min:0',
            'ins_pmt' => 'nullable|numeric|min:0',
            'insurance' => 'nullable|numeric|min:0',
            'frt_advance' => 'nullable|numeric|min:0',
            'tcs_percent' => 'nullable|numeric|min:0|max:100',
            'tcs_amount' => 'nullable|numeric|min:0',
            'net_amount' => 'nullable|numeric|min:0',
            'status' => 'nullable|in:draft,pending,approved,rejected'
        ]);

        if(empty($request->billed_to)){
           $data['name'] = $request->party_name;
           $data['phone'] = $request->mobile;
           $data['email'] = $request->email;
           $data['state_id'] = 1;
           $data['city_id'] = 1;
           $data['gst_number'] = 0000;
           $data['status'] = 'active';
           $customer = Customer::create($data);
        }
        try {
            // Always generate a new quotation number for each sales entry
            $companyId = auth()->user()->current_company_id;
            $quotationNumber = SalesEntry::generateQuotationNumber($companyId);

            $sales_entry = SalesEntry::create([
                'company_id' => $companyId,
                'customer_id' => $request->billed_to ?? $customer->id,
                'user_id' => auth()->id(),
                'invoice_number' => $quotationNumber,
                'invoice_date' => $request->invoice_date,
                'payment_terms' => $request->payment_terms,
                'party_name' => $request->party_name,
                'mobile' => $request->mobile,
                'email' => $request->email,
                'ins_pmt' => $request->ins_pmt ?? 0,
                'insurance' => $request->insurance ?? 0,
                'frt_advance' => $request->frt_advance ?? 0,
                'tcs_percent' => $request->tcs_percent ?? 0,
                'tcs_amount' => $request->tcs_amount ?? 0,
                'net_amount' => $request->net_amount ?? 0,
                'status' => $request->status ?? 'pending',
                // Legacy fields for backward compatibility
                'quotation_number' => $quotationNumber,
                'quotation_date' => $request->invoice_date,
                'valid_until' => now()->addDays(30), // Default 30 days validity
            ]);

            \Log::info('Sales Entry Created:', ['id' => $sales_entry->id]);

        $subtotal = 0;
        $total_cgst = 0;
        $total_sgst = 0;
        $total_igst = 0;

        foreach ($request->products as $product) {
            $subtotal += $product['taxable_value'];
            $total_cgst += $product['cgst_amount'];
            $total_sgst += $product['sgst_amount'];
            $total_igst += $product['igst_amount'];

            $sales_entry->items()->create([
                'product_id' => $product['product_id'],
                'hsn_code' => $product['hsn_code'],
                'gst_percent' => $product['gst_percent'],
                'packages' => $product['packages'],
                'unit' => $product['unit'],
                'rate' => $product['rate'],
                'loading' => $product['loading'] ?? 0,
                'gauge_diff' => $product['gauge_diff'] ?? 0,
                'basic_rate' => $product['basic_rate'],
                'total' => $product['total'],
                'taxable_value' => $product['taxable_value'],
                'description' => $product['description'],
                'cgst_percent' => $product['cgst_percent'],
                'cgst_amount' => $product['cgst_amount'],
                'sgst_percent' => $product['sgst_percent'],
                'sgst_amount' => $product['sgst_amount'],
                'igst_percent' => $product['igst_percent'],
                'igst_amount' => $product['igst_amount'],
                // Legacy fields for backward compatibility
                'quantity' => $product['packages'], // Use packages as quantity
                'unit_price' => $product['rate'],   // Use rate as unit price
                'discount' => 0,                    // Default discount
                'line_total' => $product['total'],  // Use total as line total
            ]);
        }

        $grand_total = $subtotal + $total_cgst + $total_sgst + $total_igst;

            $sales_entry->update([
                'subtotal' => $subtotal,
                'total_cgst' => $total_cgst,
                'total_sgst' => $total_sgst,
                'total_igst' => $total_igst,
                'grand_total' => $grand_total,
            ]);

            \Log::info('Sales Entry Updated with totals');

            return redirect()->route('entries.sales')->with('success', 'Sales invoice created successfully!');

        } catch (\Exception $e) {
            \Log::error('Sales Entry Store Error:', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return back()->withErrors(['error' => 'Failed to create sales entry: ' . $e->getMessage()])->withInput();
        }
    }

    public function showSales($id)
    {
        $salesEntry = SalesEntry::with(['customer', 'items.product', 'user', 'company'])
            ->findOrFail($id);

        // Ensure user can access this sales entry's company data
        $this->ensureCompanyAccess($salesEntry->company_id);

        return view('entries.sales.show', compact('salesEntry'));
    }

    public function printSales($id)
    {
        $salesEntry = SalesEntry::with(['customer.state', 'customer.city', 'items.product', 'user', 'company.state', 'company.city'])->findOrFail($id);

        // Get company for dynamic template with company-specific header
        $company = $salesEntry->company;
        return $pdfSettings = $this->getCompanyPdfSettings($company);



        // Generate PDF with company-specific header - use enhanced template for better design
        try {
            $html = view('entries.sales.print-simple', compact('salesEntry', 'company', 'pdfSettings'))->render();
        } catch (\Exception $e) {
            // Fallback to simple template if enhanced template fails
            \Log::warning('Print template error, using fallback: ' . $e->getMessage());
            $html = view('entries.sales.print-simple', compact('salesEntry', 'company', 'pdfSettings'))->render();
        }

        // Configure Dompdf with company-specific settings
        $options = new \Dompdf\Options();
        $options->set('defaultFont', $pdfSettings['font_family'] ?? 'Arial');
        $options->set('isRemoteEnabled', true);
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isPhpEnabled', false); // Disable PHP for security
        $options->set('debugKeepTemp', false);
        $options->set('debugCss', false);
        $options->set('debugLayout', false);
        $options->set('debugLayoutLines', false);
        $options->set('debugLayoutBlocks', false);
        $options->set('debugLayoutInline', false);
        $options->set('debugLayoutPaddingBox', false);

        $dompdf = new \Dompdf\Dompdf($options);

        try {
            $dompdf->loadHtml($html);
            $dompdf->setPaper($pdfSettings['paper_size'] ?? 'A4', $pdfSettings['orientation'] ?? 'portrait');
            $dompdf->render();
        } catch (\Exception $e) {
            \Log::error('Dompdf render error: ' . $e->getMessage());
            throw new \Exception('PDF generation failed: ' . $e->getMessage());
        }

        $filename = $pdfSettings['filename_prefix'] . '_' . $salesEntry->quotation_number . '_' . date('Y-m-d') . '.pdf';

        return response($dompdf->output(), 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="' . $filename . '"',
            'Content-Length' => strlen($dompdf->output()),
            'Cache-Control' => 'private, max-age=0, must-revalidate',
            'Pragma' => 'public',
        ]);
    }

    /**
     * View sales entry using dynamic print template (public access)
     */
    public function printSalesDynamic($id)
    {
        $salesEntry = SalesEntry::with(['customer.state', 'customer.city', 'items.product', 'company.state', 'company.city'])->findOrFail($id);

        // Get company for dynamic template
        $company = $salesEntry->company;

        // Default PDF settings with all required keys
        $pdfSettings = [
            'filename_prefix' => 'Quotation',
            'font_family' => 'Arial',
            'font_size' => '12px',
            'header_color' => '#2563eb',
            'accent_color' => '#1d4ed8',
            'show_logo' => true,
            'logo_position' => 'left',
            'logo_size' => 'medium',

            'paper_size' => 'A4',
            'orientation' => 'portrait',
            'currency_symbol' => '₹',
            'date_format' => 'd M Y',
            'show_company_details' => true,
            'show_gst_details' => true,
            'show_watermark'=>true,
            'terms_and_conditions' => 'Thank you for your business. Please review the quotation and contact us for any clarifications.',
            'footer_text' => 'This is a system-generated quotation from ' . ($company->name ?? 'JMD Traders')
        ];
     //return view('entries.sales.print', compact('salesEntry', 'company', 'pdfSettings'));


         $pdf = Pdf::loadView('entries.sales.pdf', compact('salesEntry', 'company', 'pdfSettings'));

        // $pdf = Pdf::loadView('pdf.report', $data);

        // Download karne ke liye
        return $pdf->download($salesEntry->invoice_number.'.pdf');
    }

    public function downloadSalesPdf($id)
    {
        $salesEntry = SalesEntry::with(['customer.state', 'customer.city', 'items.product', 'user', 'company.state', 'company.city'])->findOrFail($id);

        // Get company-specific PDF settings
        $company = $salesEntry->company;
        $pdfSettings = $this->getCompanyPdfSettings($company);

        // Generate HTML content with company-specific template - use enhanced template for better design
        try {
            $html = view('entries.sales.print-dynamic', compact('salesEntry', 'company', 'pdfSettings'))->render();
        } catch (\Exception $e) {
            // Fallback to simple template if enhanced template fails
            \Log::warning('Print template error, using fallback: ' . $e->getMessage());
            $html = view('entries.sales.print-dynamic', compact('salesEntry', 'company', 'pdfSettings'))->render();
        }

        // Configure Dompdf with company-specific settings
        $options = new Options();
        $options->set('defaultFont', $pdfSettings['font_family'] ?? 'Arial');
        $options->set('isRemoteEnabled', true);
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isPhpEnabled', false);

        $dompdf = new Dompdf($options);

        try {
            $dompdf->loadHtml($html);
            $dompdf->setPaper($pdfSettings['paper_size'] ?? 'A4', $pdfSettings['orientation'] ?? 'portrait');
            $dompdf->render();
        } catch (\Exception $e) {
            \Log::error('Dompdf render error: ' . $e->getMessage());
            throw new \Exception('PDF generation failed: ' . $e->getMessage());
        }

        $filename = $pdfSettings['filename_prefix'] . '_' . str_replace(['/', '\\'], '-', $salesEntry->quotation_number) . '_' . date('Y-m-d') . '.pdf';

        return response($dompdf->output(), 200)
            ->header('Content-Type', 'application/pdf')
            ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
    }

    public function generateSalesPdfUrl($id)
    {
        $salesEntry = SalesEntry::with(['customer.state', 'customer.city', 'items.product', 'user', 'company.state', 'company.city'])->findOrFail($id);

        // Get company-specific PDF settings
        $company = $salesEntry->company;
        $pdfSettings = $this->getCompanyPdfSettings($company);

        // Generate HTML content with company-specific template - use enhanced template for better design
        try {
            $html = view('entries.sales.print-enhanced', compact('salesEntry', 'company', 'pdfSettings'))->render();
        } catch (\Exception $e) {
            // Fallback to simple template if enhanced template fails
            \Log::warning('Print template error, using fallback: ' . $e->getMessage());
            $html = view('entries.sales.print-dynamic', compact('salesEntry', 'company', 'pdfSettings'))->render();
        }

        // Configure Dompdf with company-specific settings
        $options = new Options();
        $options->set('defaultFont', $pdfSettings['font_family'] ?? 'Arial');
        $options->set('isRemoteEnabled', true);
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isPhpEnabled', false);

        $dompdf = new Dompdf($options);

        try {
            $dompdf->loadHtml($html);
            $dompdf->setPaper($pdfSettings['paper_size'] ?? 'A4', $pdfSettings['orientation'] ?? 'portrait');
            $dompdf->render();
        } catch (\Exception $e) {
            \Log::error('Dompdf render error: ' . $e->getMessage());
            throw new \Exception('PDF generation failed: ' . $e->getMessage());
        }

        $filename = $pdfSettings['filename_prefix'] . '_' . str_replace(['/', '\\'], '-', $salesEntry->quotation_number) . '_' . date('Y-m-d') . '.pdf';
        $filepath = 'pdfs/' . $filename;

        // Create directory if it doesn't exist
        $directory = storage_path('app/public/pdfs');
        if (!file_exists($directory)) {
            mkdir($directory, 0755, true);
        }

        // Save PDF to storage
        file_put_contents(storage_path('app/public/' . $filepath), $dompdf->output());

        // Return public URL
        return response()->json([
            'url' => asset('storage/' . $filepath),
            'filename' => $filename,
            'filepath' => $filepath
        ]);
    }

    /**
     * Generate share token for sales entry
     */
    public function generateShareToken($id)
    {
        try {
            $salesEntry = SalesEntry::findOrFail($id);

            $token = $salesEntry->generateShareToken();

            return response()->json([
                'success' => true,
                'token' => $token,
                'share_url' => $salesEntry->share_url,
                'share_pdf_url' => $salesEntry->share_pdf_url,
                'expires_at' => $salesEntry->share_expires_at->format('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            \Log::error('Error generating share token:', ['error' => $e->getMessage(), 'id' => $id]);
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * View shared sales entry
     */
    public function viewSharedSales($token)
    {
        try {
            $salesEntry = SalesEntry::findByShareToken($token);

            if (!$salesEntry) {
                // Try to find by token without expiry check for debugging
                $salesEntryDebug = SalesEntry::where('share_token', $token)->first();
                if ($salesEntryDebug) {
                    \Log::error('Share token found but expired or disabled', [
                        'token' => $token,
                        'entry_id' => $salesEntryDebug->id,
                        'enabled' => $salesEntryDebug->share_enabled,
                        'expires_at' => $salesEntryDebug->share_expires_at
                    ]);
                    abort(404, 'Shared link has expired or is disabled');
                } else {
                    \Log::error('Share token not found', ['token' => $token]);
                    abort(404, 'Shared link not found');
                }
            }
        } catch (\Exception $e) {
            \Log::error('Error finding shared sales entry', [
                'token' => $token,
                'error' => $e->getMessage()
            ]);
            abort(404, 'Error accessing shared link: ' . $e->getMessage());
        }

        // Load relationships
        $salesEntry->load(['customer', 'items.product', 'company']);

        return view('entries.sales.shared', compact('salesEntry'));
    }

    /**
     * Download shared sales PDF
     */
    public function downloadSharedSalesPdf($token)
    {
        try {
            $salesEntry = SalesEntry::findByShareToken($token);

            if (!$salesEntry) {
                // Try to find by token without expiry check for debugging
                $salesEntryDebug = SalesEntry::where('share_token', $token)->first();
                if ($salesEntryDebug) {
                    \Log::error('Share token found but expired or disabled', [
                        'token' => $token,
                        'entry_id' => $salesEntryDebug->id,
                        'enabled' => $salesEntryDebug->share_enabled,
                        'expires_at' => $salesEntryDebug->share_expires_at
                    ]);
                    abort(404, 'Shared link has expired or is disabled');
                } else {
                    \Log::error('Share token not found', ['token' => $token]);
                    abort(404, 'Shared link not found');
                }
            }
        } catch (\Exception $e) {
            \Log::error('Error finding shared sales entry', [
                'token' => $token,
                'error' => $e->getMessage()
            ]);
            abort(404, 'Error accessing shared link: ' . $e->getMessage());
        }

        // Load relationships
        $salesEntry->load(['customer.state', 'customer.city', 'items.product', 'company.state', 'company.city']);

        // Get company-specific PDF settings
        $company = $salesEntry->company;
        $pdfSettings = $this->getCompanyPdfSettings($company);

        // Generate HTML content with company-specific template - use enhanced template for better design
        try {
            $html = view('entries.sales.print-enhanced', compact('salesEntry', 'company', 'pdfSettings'))->render();
        } catch (\Exception $e) {
            // Fallback to simple template if enhanced template fails
            \Log::warning('Print template error, using fallback: ' . $e->getMessage());
            $html = view('entries.sales.print-simple', compact('salesEntry', 'company', 'pdfSettings'))->render();
        }

        // Configure Dompdf with company-specific settings
        $options = new \Dompdf\Options();
        $options->set('defaultFont', $pdfSettings['font_family'] ?? 'Arial');
        $options->set('isRemoteEnabled', true);
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isPhpEnabled', false); // Disable PHP for security
        $options->set('debugKeepTemp', false);

        $dompdf = new \Dompdf\Dompdf($options);

        try {
            $dompdf->loadHtml($html);
            $dompdf->setPaper($pdfSettings['paper_size'] ?? 'A4', $pdfSettings['orientation'] ?? 'portrait');
            $dompdf->render();
        } catch (\Exception $e) {
            \Log::error('Dompdf render error: ' . $e->getMessage());
            throw new \Exception('PDF generation failed: ' . $e->getMessage());
        }

        $filename = $pdfSettings['filename_prefix'] . '_' . $salesEntry->quotation_number . '_' . date('Y-m-d') . '.pdf';

        return response($dompdf->output(), 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="' . $filename . '"',
            'Content-Length' => strlen($dompdf->output()),
            'Cache-Control' => 'private, max-age=0, must-revalidate',
            'Pragma' => 'public',
        ]);
    }

    /**
     * Get company-specific PDF settings
     */
    private function getCompanyPdfSettings($company)
    {
        // Default PDF settings
        $defaultSettings = [
            'template' => 'default',
            'font_family' => 'Arial',
            'font_size' => '12px',
            'paper_size' => 'A4',
            'orientation' => 'portrait',
            'header_color' => '#4e73df',
            'accent_color' => '#1cc88a',
            'show_logo' => true,
            'logo_position' => 'left',
            'logo_size' => 'medium',
            'show_company_details' => true,
            'show_gst_details' => true,
            'show_watermark' => true,
            'watermark_opacity' => 0.1,
            'watermark_position' => 'center',

            'filename_prefix' => 'Quotation',
            'currency_symbol' => '₹',
            'date_format' => 'd/m/Y',
            'terms_and_conditions' => 'Thank you for your business!',
            'footer_text' => 'This is a computer generated document.',
        ];

        // If company is null, return default settings
        if (!$company) {
            return $defaultSettings;
        }

        // Get company-specific settings from company settings or pdf_settings
        $companySettings = $company->settings ?? [];
        $pdfSettings = $company->pdf_settings ?? [];

        // Merge with company-specific overrides
        $settings = array_merge($defaultSettings, $companySettings, $pdfSettings);

        // Company-specific customizations based on company name/code
        $companyCode = $company->code ?? ($company->name ? strtoupper(substr($company->name, 0, 3)) : 'DOC');
        switch (strtoupper($companyCode)) {
            case 'JMD':
                $settings['header_color'] = '#2c3e50';
                $settings['accent_color'] = '#e74c3c';
                $settings['filename_prefix'] = 'JMD_Quotation';
                break;
            case 'ABC':
                $settings['header_color'] = '#8e44ad';
                $settings['accent_color'] = '#f39c12';
                $settings['filename_prefix'] = 'ABC_Quote';
                break;
            default:
                $settings['filename_prefix'] = $companyCode . '_Quotation';
                break;
        }

        return $settings;
    }





    /**
     * Download sales PDF for printing (public access)
     */
    public function downloadSalesForPrint($id)
    {
        $salesEntry = SalesEntry::with(['customer.state', 'customer.city', 'items.product', 'user', 'company.state', 'company.city'])->findOrFail($id);

        // Get company-specific PDF settings
        $company = $salesEntry->company;
        $pdfSettings = $this->getCompanyPdfSettings($company);



        // Generate HTML content with enhanced template for better design
        try {
            $html = view('entries.sales.print-enhanced', compact('salesEntry', 'company', 'pdfSettings'))->render();
        } catch (\Exception $e) {
            // Fallback to simple template if enhanced template fails
            \Log::warning('Print template error, using fallback: ' . $e->getMessage());
            $html = view('entries.sales.print-simple', compact('salesEntry', 'company', 'pdfSettings'))->render();
        }

        // Configure Dompdf with basic settings
        $options = new Options();
        $options->set('defaultFont', 'Arial');
        $options->set('isRemoteEnabled', true);
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isPhpEnabled', false);

        $dompdf = new Dompdf($options);

        try {
            $dompdf->loadHtml($html);
            $dompdf->setPaper('A4', 'portrait');
            $dompdf->render();
        } catch (\Exception $e) {
            \Log::error('Dompdf render error: ' . $e->getMessage());
            throw new \Exception('PDF generation failed: ' . $e->getMessage());
        }

        $filename = 'Quotation_' . $salesEntry->quotation_number . '_' . date('Y-m-d') . '.pdf';

        return response($dompdf->output(), 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="' . $filename . '"',
            'Content-Length' => strlen($dompdf->output()),
            'Cache-Control' => 'private, max-age=0, must-revalidate',
            'Pragma' => 'public',
        ]);
    }

    /**
     * View transport entry for printing (public access)
     */
    public function viewTransportForPrint($id)
    {
        $transportEntry = TransportEntry::with(['customer.state', 'customer.city', 'company.state', 'company.city'])->findOrFail($id);

        return view('entries.transport.public-view', compact('transportEntry'));
    }

    /**
     * Download transport PDF for printing (public access)
     */
    public function downloadTransportForPrint($id)
    {
        $transportEntry = TransportEntry::with(['customer.state', 'customer.city', 'company.state', 'company.city'])->findOrFail($id);

        // Get company-specific PDF settings
        $company = $transportEntry->company;
        $pdfSettings = $this->getCompanyPdfSettings($company);

        // Generate HTML content with company-specific template - use simple approach to avoid Dompdf issues
        try {
            // Try to use a simple transport template if it exists, otherwise fall back to basic
            if (view()->exists('entries.transport.print-simple')) {
                $html = view('entries.transport.print-simple', compact('transportEntry', 'company', 'pdfSettings'))->render();
            } else {
                $html = view('entries.transport.print-dynamic', compact('transportEntry', 'company', 'pdfSettings'))->render();
            }
        } catch (\Exception $e) {
            // Fallback to basic template if dynamic template fails
            \Log::warning('Transport print template error, using fallback: ' . $e->getMessage());
            $html = view('entries.transport.print', compact('transportEntry'))->render();
        }

        // Configure Dompdf with company-specific settings
        $options = new Options();
        $options->set('defaultFont', $pdfSettings['font_family'] ?? 'Arial');
        $options->set('isRemoteEnabled', true);
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isPhpEnabled', false);

        $dompdf = new Dompdf($options);

        try {
            $dompdf->loadHtml($html);
            $dompdf->setPaper($pdfSettings['paper_size'] ?? 'A4', $pdfSettings['orientation'] ?? 'portrait');
            $dompdf->render();
        } catch (\Exception $e) {
            \Log::error('Dompdf render error: ' . $e->getMessage());
            throw new \Exception('PDF generation failed: ' . $e->getMessage());
        }

        $filename = 'Transport_' . $transportEntry->transport_number . '_' . date('Y-m-d') . '.pdf';

        return response($dompdf->output(), 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="' . $filename . '"',
            'Content-Length' => strlen($dompdf->output()),
            'Cache-Control' => 'private, max-age=0, must-revalidate',
            'Pragma' => 'public',
        ]);
    }

    public function editSales($id)
    {
        $salesEntry = SalesEntry::with(['customer', 'items.product', 'company'])
            ->findOrFail($id);

        // Ensure user can access this sales entry's company data
        $this->ensureCompanyAccess($salesEntry->company_id);

        // Get customers and products accessible to current user
        $customerQuery = Customer::query();
        $customers = $this->applyCompanyFilter($customerQuery)->get();

        $productQuery = Product::query();
        $products = $this->applyCompanyFilter($productQuery)->get();

        $states = \App\Models\State::where('status', 'active')->orderBy('name')->get();
        $categories = \App\Models\Category::where('status', 'active')->orderBy('name')->get();

        return view('entries.sales.edit', compact('salesEntry', 'customers', 'products', 'states', 'categories'));
    }

    public function updateSales(Request $request, $id)
    {
        $sales_entry = SalesEntry::findOrFail($id);

        // Ensure user can access this sales entry's company data
        $this->ensureCompanyAccess($sales_entry->company_id);

        $request->validate([
            'invoice_no' => 'required|string',
            'invoice_date' => 'required|date',
            'payment_terms' => 'required|string',
            'billed_to' => 'required|exists:customers,id',
            'party_name' => 'required|string|max:255',
            'mobile' => 'required|string|max:15',
            'email' => 'nullable|email|max:255',
            'products' => 'nullable|array',
            'new_products' => 'nullable|array',
            'products.*.product_id' => 'required|exists:products,id',
            'products.*.hsn_code' => 'nullable|string',
            'products.*.gst_percent' => 'required|numeric|min:0|max:100',
            'products.*.packages' => 'required|integer|min:1',
            'products.*.unit' => 'required|string',
            'products.*.rate' => 'required|numeric|min:0',
            'products.*.loading' => 'nullable|numeric|min:0',
            'products.*.basic_rate' => 'required|numeric|min:0',
            'products.*.total' => 'required|numeric|min:0',
            'products.*.taxable_value' => 'required|numeric|min:0',
            'products.*.description' => 'nullable|string',
            'products.*.cgst_percent' => 'required|numeric|min:0',
            'products.*.cgst_amount' => 'required|numeric|min:0',
            'products.*.sgst_percent' => 'required|numeric|min:0',
            'products.*.sgst_amount' => 'required|numeric|min:0',
            'products.*.igst_percent' => 'required|numeric|min:0',
            'products.*.igst_amount' => 'required|numeric|min:0',
            'new_products.*.product_id' => 'required|exists:products,id',
            'new_products.*.hsn_code' => 'nullable|string',
            'new_products.*.gst_percent' => 'required|numeric|min:0|max:100',
            'new_products.*.packages' => 'required|integer|min:1',
            'new_products.*.unit' => 'required|string',
            'new_products.*.rate' => 'required|numeric|min:0',
            'new_products.*.loading' => 'nullable|numeric|min:0',
            'new_products.*.basic_rate' => 'required|numeric|min:0',
            'new_products.*.total' => 'required|numeric|min:0',
            'new_products.*.taxable_value' => 'required|numeric|min:0',
            'new_products.*.description' => 'nullable|string',
            'new_products.*.cgst_percent' => 'required|numeric|min:0',
            'new_products.*.cgst_amount' => 'required|numeric|min:0',
            'new_products.*.sgst_percent' => 'required|numeric|min:0',
            'new_products.*.sgst_amount' => 'required|numeric|min:0',
            'new_products.*.igst_percent' => 'required|numeric|min:0',
            'new_products.*.igst_amount' => 'required|numeric|min:0',
            'ins_pmt' => 'nullable|numeric|min:0',
            'insurance' => 'nullable|numeric|min:0',
            'frt_advance' => 'nullable|numeric|min:0',
            'tcs_percent' => 'nullable|numeric|min:0|max:100',
            'tcs_amount' => 'nullable|numeric|min:0',
            'net_amount' => 'nullable|numeric|min:0',
            'status' => 'nullable|in:draft,pending,approved,rejected'
        ]);

        // Ensure at least one product exists (either existing or new)
        if ((!$request->has('products') || empty($request->products)) &&
            (!$request->has('new_products') || empty($request->new_products))) {
            return back()->withErrors(['products' => 'At least one product is required.'])->withInput();
        }

        $sales_entry->update([
            'customer_id' => $request->billed_to,
            'invoice_number' => $request->invoice_no,
            'invoice_date' => $request->invoice_date,
            'payment_terms' => $request->payment_terms,
            'party_name' => $request->party_name,
            'mobile' => $request->mobile,
            'email' => $request->email,
            'ins_pmt' => $request->ins_pmt ?? 0,
            'insurance' => $request->insurance ?? 0,
            'frt_advance' => $request->frt_advance ?? 0,
            'tcs_percent' => $request->tcs_percent ?? 0,
            'tcs_amount' => $request->tcs_amount ?? 0,
            'net_amount' => $request->net_amount ?? 0,
            'status' => $request->status ?? 'pending',
            // Update legacy fields for backward compatibility
            'quotation_number' => $request->invoice_no,
            'quotation_date' => $request->invoice_date,
        ]);

        // Update existing items and add new items
        $subtotal = 0;
        $total_cgst = 0;
        $total_sgst = 0;
        $total_igst = 0;

        // Process existing products (update them)
        if ($request->has('products')) {
            foreach ($request->products as $index => $product) {
                // Find existing item by index or create new if not found
                $existingItems = $sales_entry->items()->get();
                if (isset($existingItems[$index])) {
                    // Update existing item
                    $existingItems[$index]->update([
                        'product_id' => $product['product_id'],
                        'hsn_code' => $product['hsn_code'],
                        'gst_percent' => $product['gst_percent'],
                        'packages' => $product['packages'],
                        'unit' => $product['unit'],
                        'rate' => $product['rate'],
                        'loading' => $product['loading'] ?? 0,
                        'gauge_diff' => $product['gauge_diff'] ?? 0,
                        'basic_rate' => $product['basic_rate'],
                        'total' => $product['total'],
                        'taxable_value' => $product['taxable_value'],
                        'description' => $product['description'],
                        'cgst_percent' => $product['cgst_percent'],
                        'cgst_amount' => $product['cgst_amount'],
                        'sgst_percent' => $product['sgst_percent'],
                        'sgst_amount' => $product['sgst_amount'],
                        'igst_percent' => $product['igst_percent'],
                        'igst_amount' => $product['igst_amount'],
                        // Legacy fields for backward compatibility
                        'quantity' => $product['packages'],
                        'unit_price' => $product['rate'],
                        'discount' => 0,
                        'line_total' => $product['total'],
                    ]);
                }

                $subtotal += $product['total'];
                $total_cgst += $product['cgst_amount'];
                $total_sgst += $product['sgst_amount'];
                $total_igst += $product['igst_amount'];
            }
        }

        // Process new products (add them)
        if ($request->has('new_products')) {
            foreach ($request->new_products as $product) {
                $sales_entry->items()->create([
                    'product_id' => $product['product_id'],
                    'hsn_code' => $product['hsn_code'],
                    'gst_percent' => $product['gst_percent'],
                    'packages' => $product['packages'],
                    'unit' => $product['unit'],
                    'rate' => $product['rate'],
                    'loading' => $product['loading'] ?? 0,
                    'gauge_diff' => $product['gauge_diff'] ?? 0,
                    'basic_rate' => $product['basic_rate'],
                    'total' => $product['total'],
                    'taxable_value' => $product['taxable_value'],
                    'description' => $product['description'],
                    'cgst_percent' => $product['cgst_percent'],
                    'cgst_amount' => $product['cgst_amount'],
                    'sgst_percent' => $product['sgst_percent'],
                    'sgst_amount' => $product['sgst_amount'],
                    'igst_percent' => $product['igst_percent'],
                    'igst_amount' => $product['igst_amount'],
                    // Legacy fields for backward compatibility
                    'quantity' => $product['packages'],
                    'unit_price' => $product['rate'],
                    'discount' => 0,
                    'line_total' => $product['total'],
                ]);

                $subtotal += $product['total'];
                $total_cgst += $product['cgst_amount'];
                $total_sgst += $product['sgst_amount'];
                $total_igst += $product['igst_amount'];
            }
        }

        $grand_total = $subtotal + $total_cgst + $total_sgst + $total_igst;

        $sales_entry->update([
            'subtotal' => $subtotal,
            'total_cgst' => $total_cgst,
            'total_sgst' => $total_sgst,
            'total_igst' => $total_igst,
            'grand_total' => $grand_total,
        ]);

        return redirect()->route('entries.sales')->with('success', 'Sales entry updated successfully!');
    }

    public function deleteSales($id)
    {
        $sales_entry = SalesEntry::findOrFail($id);

        // Ensure user can access this sales entry's company data
        $this->ensureCompanyAccess($sales_entry->company_id);
        $sales_entry->items()->delete();
        $sales_entry->delete();

        return response()->json([
            'success' => true,
            'message' => 'Sales entry deleted successfully'
        ]);
    }

    /**
     * Export sales entries to Excel
     */
    public function exportSales(Request $request)
    {
        try {
            $user = auth()->user();

            if (!$user) {
                return response()->json(['error' => 'User not authenticated'], 401);
            }

            // Get accessible company IDs
            $companyIds = $user->canAccessAllCompanies()
                ? null
                : $user->getAccessibleCompanyIds();

            // Get filters from request
            $filters = $request->only(['status', 'customer_id', 'date_from', 'date_to']);

            // Debug: Log the export attempt
            \Log::info('Sales export started', [
                'user_id' => $user->id,
                'company_ids' => $companyIds,
                'filters' => $filters
            ]);

            // Generate filename
            $filename = 'sales_entries_' . date('Y-m-d_H-i-s') . '.xlsx';

            // Create export instance (using simple export to avoid styling issues)
            $export = new SimpleSalesExport($companyIds, $filters);

            // Test if we have data
            $collection = $export->collection();
            if ($collection->isEmpty()) {
                return response()->json(['error' => 'No data found to export'], 404);
            }

            // Try Excel first, fallback to CSV if it fails
            try {
                return Excel::download($export, $filename, \Maatwebsite\Excel\Excel::XLSX, [
                    'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                ]);
            } catch (\Exception $excelError) {
                \Log::warning('Excel export failed, falling back to CSV: ' . $excelError->getMessage());

                // Fallback to CSV
                $csvFilename = str_replace('.xlsx', '.csv', $filename);
                return Excel::download($export, $csvFilename, \Maatwebsite\Excel\Excel::CSV);
            }

        } catch (\Exception $e) {
            \Log::error('Sales export error: ' . $e->getMessage(), [
                'filters' => $request->all(),
                'user_id' => auth()->id(),
                'trace' => $e->getTraceAsString()
            ]);

            // Return JSON error for AJAX requests
            if ($request->expectsJson()) {
                return response()->json(['error' => 'Export failed: ' . $e->getMessage()], 500);
            }

            return back()->with('error', 'Export failed: ' . $e->getMessage());
        }
    }

    /**
     * Export sales entries as CSV (fallback method)
     */
    public function exportSalesCSV(Request $request)
    {
        try {
            $user = auth()->user();

            if (!$user) {
                return response()->json(['error' => 'User not authenticated'], 401);
            }

            // Get accessible company IDs
            $companyIds = $user->canAccessAllCompanies()
                ? null
                : $user->getAccessibleCompanyIds();

            // Get filters from request
            $filters = $request->only(['status', 'customer_id', 'date_from', 'date_to']);

            // Build query
            $query = \App\Models\SalesEntry::with(['customer', 'company', 'items.product', 'user']);

            // Apply company filter
            if ($companyIds) {
                $query->whereIn('company_id', $companyIds);
            }

            // Apply additional filters
            if (!empty($filters['status'])) {
                $query->where('status', $filters['status']);
            }

            if (!empty($filters['customer_id'])) {
                $query->where('customer_id', $filters['customer_id']);
            }

            if (!empty($filters['date_from'])) {
                $query->whereDate('quotation_date', '>=', $filters['date_from']);
            }

            if (!empty($filters['date_to'])) {
                $query->whereDate('quotation_date', '<=', $filters['date_to']);
            }

            $salesEntries = $query->orderBy('quotation_date', 'desc')->get();

            // Create CSV content
            $csv = "Quotation No,Date,Customer Name,Customer Phone,Company,Status,Items Count,Subtotal,Grand Total,Created At\n";

            foreach ($salesEntries as $entry) {
                $csv .= sprintf(
                    '"%s","%s","%s","%s","%s","%s","%s","%s","%s","%s"' . "\n",
                    $entry->quotation_number ?? 'N/A',
                    $entry->quotation_date ? $entry->quotation_date->format('d-m-Y') : 'N/A',
                    optional($entry->customer)->name ?? 'N/A',
                    optional($entry->customer)->phone ?? 'N/A',
                    optional($entry->company)->name ?? 'N/A',
                    ucfirst($entry->status ?? 'pending'),
                    $entry->items ? $entry->items->count() : 0,
                    number_format($entry->subtotal ?? 0, 2),
                    number_format($entry->calculated_net_amount ?? $entry->grand_total ?? $entry->total_amount ?? 0, 2),
                    $entry->created_at ? $entry->created_at->format('d-m-Y H:i') : 'N/A'
                );
            }

            $filename = 'sales_entries_' . date('Y-m-d_H-i-s') . '.csv';

            return response($csv)
                ->header('Content-Type', 'text/csv')
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');

        } catch (\Exception $e) {
            \Log::error('CSV export error: ' . $e->getMessage());
            return response()->json(['error' => 'CSV export failed: ' . $e->getMessage()], 500);
        }
    }

    // Transport Entry Methods
    public function transport(Request $request)
    {
        // Apply company-based filtering for transport entries
        $query = TransportEntry::with(['salesEntry.customer', 'user', 'customer']);
        $this->applyCompanyFilter($query);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('transport_number', 'like', "%{$search}%")
                  ->orWhere('vehicle_number', 'like', "%{$search}%")
                  ->orWhere('driver_name', 'like', "%{$search}%")
                  ->orWhere('driver_mob_no', 'like', "%{$search}%")
                  ->orWhere('lr_no', 'like', "%{$search}%")
                  ->orWhere('way_bill_no', 'like', "%{$search}%")
                  ->orWhere('eway_bill_no', 'like', "%{$search}%")
                  ->orWhereHas('salesEntry', function($salesQuery) use ($search) {
                      $salesQuery->where('quotation_number', 'like', "%{$search}%")
                                 ->orWhere('invoice_number', 'like', "%{$search}%");
                  })
                  ->orWhereHas('salesEntry.customer', function($customerQuery) use ($search) {
                      $customerQuery->where('name', 'like', "%{$search}%");
                  })
                  ->orWhereHas('customer', function($customerQuery) use ($search) {
                      $customerQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Sales Entry filter
        if ($request->filled('sales_entry_id')) {
            $query->where('sales_entry_id', $request->sales_entry_id);
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Date range filter
        if ($request->filled('date_from')) {
            $query->where('transport_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->where('transport_date', '<=', $request->date_to);
        }

        // Company filter (for super admin)
        if ($request->filled('company_id') && auth()->user()->hasRole('super_admin')) {
            $query->where('company_id', $request->company_id);
        }

        // Handle per_page parameter
        $perPage = $request->get('per_page', 25); // Default to 25
        $perPage = in_array($perPage, [10, 25, 50, 100]) ? $perPage : 25;

        $transportEntries = $query->orderBy('created_at', 'desc')->paginate($perPage);
        $transportEntries->appends($request->query()); // Preserve query parameters

        // Get approved sales entries accessible to current user
        $salesQuery = SalesEntry::where('status', 'approved');
        $salesEntries = $this->applyCompanyFilter($salesQuery)->get();

        // Get accessible companies for filter dropdown (if super admin)
        $accessibleCompanies = $this->getAccessibleCompanies();

        // Calculate statistics with company filtering
        $statsQuery = TransportEntry::query();
        $this->applyCompanyFilter($statsQuery);

        $stats = [
            'total' => (clone $statsQuery)->count(),
            'scheduled' => (clone $statsQuery)->where('status', 'scheduled')->count(),
            'in_transit' => (clone $statsQuery)->where('status', 'in_transit')->count(),
            'delivered' => (clone $statsQuery)->where('status', 'delivered')->count(),
            'delayed' => (clone $statsQuery)->where('status', '!=', 'delivered')
                ->where(function($query) {
                    $query->where('due_date', '<', now()->toDateString())
                          ->orWhere(function($q) {
                              $q->whereNull('due_date')
                                ->where('estimated_delivery', '<', now()->toDateString());
                          });
                })->count(),
            'total_value' => (clone $statsQuery)->sum('transport_cost')
        ];

        return view('entries.transport.index', compact('transportEntries', 'salesEntries', 'stats', 'accessibleCompanies'));
    }

    public function createTransport()
    {
        // Get all sales entries for selection (not just approved ones)
        $salesEntries = SalesEntry::with('customer')
            ->whereIn('status', ['pending', 'approved'])
            ->orderBy('created_at', 'desc')
            ->get();
        
        return view('entries.transport.create', compact('salesEntries'));
    }

    public function storeTransport(Request $request)
    {
        $request->validate([
            'sales_entry_id' => 'nullable|exists:sales_entries,id',
            'transport_date' => 'required|date',
            'due_days' => 'nullable|integer|min:0',
            'due_date' => 'nullable|date',
            'freight_cond' => 'nullable|string|max:255',
            'way_bill_no' => 'nullable|string|max:255',
            'material_direct_delivered_from' => 'nullable|string|max:255',
            'lr_no' => 'nullable|string|max:255',
            'goods_insured_by' => 'nullable|string|max:255',
            'eway_bill_no' => 'nullable|string|max:255',
            'doc_through' => 'nullable|string|max:255',
            'policy_no' => 'nullable|string|max:255',
            'eway_bill_date' => 'nullable|date',
            'driver_name' => 'nullable|string|max:255',
            'lc_no' => 'nullable|string|max:255',
            'our_offer_no' => 'nullable|string|max:255',
            'driver_mob_no' => 'nullable|string|max:15',
            'ac_of' => 'nullable|string|max:255',
            'cash_address' => 'nullable|string',
            'dl_no' => 'nullable|string|max:255',
            'from_city' => 'nullable|string|max:255',
            'export_inv_no' => 'nullable|string|max:255',
            'destination' => 'nullable|string|max:255',
            'to_city' => 'nullable|string|max:255',
            'export_inv_date' => 'nullable|date',
            'road_permit_no' => 'nullable|string|max:255',
            'delivery_1' => 'nullable|string|max:255',
            'prepared_by' => 'nullable|string|max:255',
            'delivery_2' => 'nullable|string|max:255',
            'remarks' => 'nullable|string',
            'gross_wt' => 'nullable|numeric|min:0',
            'tare_wt' => 'nullable|numeric|min:0',
            'net_wt' => 'nullable|numeric|min:0',
            'vehicle_number' => 'nullable|string|max:20',
            'total' => 'nullable|numeric|min:0',
            'ins_pmt' => 'nullable|numeric|min:0',
            'insurance' => 'nullable|numeric|min:0',
            'frt_advance' => 'nullable|numeric|min:0',
            'grand_total' => 'nullable|numeric|min:0',
            'tcs_percent' => 'nullable|numeric|min:0|max:100',
            'tcs_amount' => 'nullable|numeric|min:0',
            'net_amount' => 'nullable|numeric|min:0',
            'status' => 'nullable|in:scheduled,in_transit,delivered,cancelled'
        ]);

        try {
            $transportEntry = TransportEntry::create([
                'company_id' => auth()->user()->current_company_id,
                'customer_id' => auth()->user()->id,
                'sales_entry_id' => $request->sales_entry_id, // Add this field
                'user_id' => auth()->id(),
                'transport_date' => $request->transport_date,
                'due_days' => $request->due_days,
                'due_date' => $request->due_date,
                'freight_cond' => $request->freight_cond,
                'way_bill_no' => $request->way_bill_no,
                'material_direct_delivered_from' => $request->material_direct_delivered_from,
                'lr_no' => $request->lr_no,
                'goods_insured_by' => $request->goods_insured_by,
                'eway_bill_no' => $request->eway_bill_no,
                'doc_through' => $request->doc_through,
                'policy_no' => $request->policy_no,
                'eway_bill_date' => $request->eway_bill_date,
                'driver_name' => $request->driver_name,
                'lc_no' => $request->lc_no,
                'our_offer_no' => $request->our_offer_no,
                'driver_mob_no' => $request->driver_mob_no,
                'ac_of' => $request->ac_of,
                'cash_address' => $request->cash_address,
                'dl_no' => $request->dl_no,
                'from_city' => $request->from_city,
                'export_inv_no' => $request->export_inv_no,
                'destination' => $request->destination,
                'to_city' => $request->to_city,
                'export_inv_date' => $request->export_inv_date,
                'road_permit_no' => $request->road_permit_no,
                'delivery_1' => $request->delivery_1,
                'prepared_by' => $request->prepared_by,
                'delivery_2' => $request->delivery_2,
                'remarks' => $request->remarks,
                'gross_wt' => $request->gross_wt ?? 0,
                'tare_wt' => $request->tare_wt ?? 0,
                'net_wt' => $request->net_wt ?? 0,
                'vehicle_number' => $request->vehicle_number,
                'total' => $request->total ?? 0,
                'ins_pmt' => $request->ins_pmt ?? 0,
                'insurance' => $request->insurance ?? 0,
                'frt_advance' => $request->frt_advance ?? 0,
                'grand_total' => $request->grand_total ?? 0,
                'tcs_percent' => $request->tcs_percent ?? 0,
                'tcs_amount' => $request->tcs_amount ?? 0,
                'net_amount' => $request->net_amount ?? 0,
                'status' => $request->status ?? 'scheduled',
            ]);

            return redirect()->route('entries.transport')->with('success', 'Transport entry created successfully!');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to create transport entry: ' . $e->getMessage()])->withInput();
        }
    }

    public function showTransport($id)
    {
        $transportEntry = TransportEntry::with(['salesEntry.customer', 'user'])->findOrFail($id);
        return view('entries.transport.show', compact('transportEntry'));
    }

    public function editTransport($id)
    {
        $transportEntry = TransportEntry::findOrFail($id);
        $salesEntries = SalesEntry::where('status', 'approved')->get();
        
        return view('entries.transport.edit', compact('transportEntry', 'salesEntries'));
    }

    public function updateTransport(Request $request, $id)
    {
        $transportEntry = TransportEntry::findOrFail($id);
        
        $request->validate([
            'transport_date' => 'required|date',
            'due_days' => 'nullable|integer|min:0',
            'due_date' => 'nullable|date',
            'freight_cond' => 'nullable|string|max:255',
            'way_bill_no' => 'nullable|string|max:255',
            'material_direct_delivered_from' => 'nullable|string|max:255',
            'lr_no' => 'nullable|string|max:255',
            'goods_insured_by' => 'nullable|string|max:255',
            'eway_bill_no' => 'nullable|string|max:255',
            'doc_through' => 'nullable|string|max:255',
            'policy_no' => 'nullable|string|max:255',
            'eway_bill_date' => 'nullable|date',
            'driver_name' => 'nullable|string|max:255',
            'lc_no' => 'nullable|string|max:255',
            'our_offer_no' => 'nullable|string|max:255',
            'driver_mob_no' => 'nullable|string|max:15',
            'ac_of' => 'nullable|string|max:255',
            'cash_address' => 'nullable|string',
            'dl_no' => 'nullable|string|max:255',
            'from_city' => 'nullable|string|max:255',
            'export_inv_no' => 'nullable|string|max:255',
            'destination' => 'nullable|string|max:255',
            'to_city' => 'nullable|string|max:255',
            'export_inv_date' => 'nullable|date',
            'road_permit_no' => 'nullable|string|max:255',
            'delivery_1' => 'nullable|string|max:255',
            'prepared_by' => 'nullable|string|max:255',
            'delivery_2' => 'nullable|string|max:255',
            'remarks' => 'nullable|string',
            'gross_wt' => 'nullable|numeric|min:0',
            'tare_wt' => 'nullable|numeric|min:0',
            'net_wt' => 'nullable|numeric|min:0',
            'vehicle_number' => 'nullable|string|max:20',
            'total' => 'nullable|numeric|min:0',
            'ins_pmt' => 'nullable|numeric|min:0',
            'insurance' => 'nullable|numeric|min:0',
            'frt_advance' => 'nullable|numeric|min:0',
            'grand_total' => 'nullable|numeric|min:0',
            'tcs_percent' => 'nullable|numeric|min:0|max:100',
            'tcs_amount' => 'nullable|numeric|min:0',
            'net_amount' => 'nullable|numeric|min:0',
            'status' => 'nullable|in:scheduled,in_transit,delivered,cancelled'
        ]);

        try {
            $transportEntry->update($request->only([
                'transport_date', 'due_days', 'due_date', 'freight_cond', 'way_bill_no',
                'material_direct_delivered_from', 'lr_no', 'goods_insured_by', 'eway_bill_no',
                'doc_through', 'policy_no', 'eway_bill_date', 'driver_name', 'lc_no',
                'our_offer_no', 'driver_mob_no', 'ac_of', 'cash_address', 'dl_no',
                'from_city', 'export_inv_no', 'destination', 'to_city', 'export_inv_date',
                'road_permit_no', 'delivery_1', 'prepared_by', 'delivery_2', 'remarks',
                'gross_wt', 'tare_wt', 'net_wt', 'vehicle_number', 'total', 'ins_pmt',
                'insurance', 'frt_advance', 'grand_total', 'tcs_percent', 'tcs_amount',
                'net_amount', 'status'
            ]));

            return redirect()->route('entries.transport')->with('success', 'Transport entry updated successfully!');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to update transport entry: ' . $e->getMessage()])->withInput();
        }
    }

    public function deleteTransport($id)
    {
        try {
            $transportEntry = TransportEntry::findOrFail($id);
            $transportEntry->delete();

            return redirect()->route('entries.transport')->with('success', 'Transport entry deleted successfully!');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to delete transport entry: ' . $e->getMessage()]);
        }
    }

    public function updateTransportStatus(Request $request, $id)
    {
        try {
            $request->validate([
                'status' => 'required|in:scheduled,in_transit,delivered,cancelled'
            ]);

            $transportEntry = TransportEntry::findOrFail($id);
            $transportEntry->update([
                'status' => $request->status
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Status updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating status: ' . $e->getMessage()
            ], 500);
        }
    }

    public function printTransport($id)
    {
        $transportEntry = TransportEntry::with(['salesEntry.customer', 'user', 'customer.state', 'customer.city', 'company.state', 'company.city'])->findOrFail($id);
        // Get company for dynamic template
        $company = $transportEntry->company;
        // Use the regular print template with company data

          // Default PDF settings with all required keys
        $pdfSettings = [
            'filename_prefix' => 'Quotation',
            'font_family' => 'Arial',
            'font_size' => '12px',
            'header_color' => '#2563eb',
            'accent_color' => '#1d4ed8',
            'show_logo' => true,
            'logo_position' => 'left',
            'logo_size' => 'medium',

            'paper_size' => 'A4',
            'orientation' => 'portrait',
            'currency_symbol' => '₹',
            'date_format' => 'd M Y',
            'show_company_details' => true,
            'show_gst_details' => true,
            'show_watermark'=>true,
            'terms_and_conditions' => 'Thank you for your business. Please review the quotation and contact us for any clarifications.',
            'footer_text' => 'This is a system-generated quotation from ' . ($company->name ?? 'JMD Traders')
        ];

       // return view('entries.transport.print', compact('transportEntry', 'company', 'pdfSettings'));

        $pdf = Pdf::loadView('entries.transport.print', compact('transportEntry', 'company', 'pdfSettings'));

        // $pdf = Pdf::loadView('pdf.report', $data);

        // Download karne ke liye
        return $pdf->download('transport'.$id.'.pdf');
    }



    public function downloadTransportPdf($id)
    {
        try {
            $transportEntry = TransportEntry::with(['salesEntry.customer', 'user', 'customer.state', 'customer.city', 'company.state', 'company.city'])->findOrFail($id);
            
            // Configure DomPDF options
            $options = new Options();
            $options->set('isRemoteEnabled', true);
            $options->set('isHtml5ParserEnabled', true);
            $options->set('isPhpEnabled', false); // Disable PHP for security
            $options->set('isFontSubsettingEnabled', true);
            $options->set('defaultFont', 'Arial');
            $options->set('debugKeepTemp', false);
            $options->set('debugCss', false);
            $options->set('debugLayout', false);
            
            // Create PDF instance
            $dompdf = new Dompdf($options);
            
            // Generate HTML from view with error handling
            try {
                $html = view('entries.transport.print', compact('transportEntry'))->render();
            } catch (\Exception $e) {
                \Log::warning('Transport print template error, using fallback: ' . $e->getMessage());
                // Create a simple fallback HTML if template fails
                $html = '<html><body><h1>Transport Entry #' . $transportEntry->id . '</h1><p>PDF generation error. Please contact support.</p></body></html>';
            }

            // Load HTML into DomPDF
            $dompdf->loadHtml($html);

            // Set paper size and orientation
            $dompdf->setPaper('A4', 'portrait');

            // Render PDF with error handling
            try {
                $dompdf->render();
            } catch (\Exception $e) {
                \Log::error('Dompdf render error: ' . $e->getMessage());
                throw new \Exception('PDF generation failed: ' . $e->getMessage());
            }
            
            // Generate filename
            $filename = 'Transport_Entry_' . $transportEntry->id . '_' . date('Y-m-d') . '.pdf';
            
            // Return PDF download response
            return response($dompdf->output(), 200)
                ->header('Content-Type', 'application/pdf')
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
                ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                ->header('Pragma', 'no-cache')
                ->header('Expires', '0');
                
        } catch (\Exception $e) {
            \Log::error('Transport PDF Download Error: ' . $e->getMessage());
            return back()->withErrors(['error' => 'Failed to generate PDF: ' . $e->getMessage()]);
        }
    }

    /**
     * Export transport entries to Excel
     */
    public function exportTransport(Request $request)
    {
        try {
            $user = auth()->user();

            if (!$user) {
                return response()->json(['error' => 'User not authenticated'], 401);
            }

            // Get accessible company IDs
            $companyIds = $user->canAccessAllCompanies()
                ? null
                : $user->getAccessibleCompanyIds();

            // Get filters from request
            $filters = $request->only(['status', 'date_from', 'date_to']);

            // Debug: Log the export attempt
            \Log::info('Transport export started', [
                'user_id' => $user->id,
                'company_ids' => $companyIds,
                'filters' => $filters
            ]);

            // Generate filename
            $filename = 'transport_entries_' . date('Y-m-d_H-i-s') . '.xlsx';

            // Create export instance (using simple export to avoid styling issues)
            $export = new SimpleTransportExport($companyIds, $filters);

            // Test if we have data
            $collection = $export->collection();
            if ($collection->isEmpty()) {
                return response()->json(['error' => 'No data found to export'], 404);
            }

            // Try Excel first, fallback to CSV if it fails
            try {
                return Excel::download($export, $filename, \Maatwebsite\Excel\Excel::XLSX, [
                    'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                ]);
            } catch (\Exception $excelError) {
                \Log::warning('Excel export failed, falling back to CSV: ' . $excelError->getMessage());

                // Fallback to CSV
                $csvFilename = str_replace('.xlsx', '.csv', $filename);
                return Excel::download($export, $csvFilename, \Maatwebsite\Excel\Excel::CSV);
            }

        } catch (\Exception $e) {
            \Log::error('Transport export error: ' . $e->getMessage(), [
                'filters' => $request->all(),
                'user_id' => auth()->id(),
                'trace' => $e->getTraceAsString()
            ]);

            // Return JSON error for AJAX requests
            if ($request->expectsJson()) {
                return response()->json(['error' => 'Export failed: ' . $e->getMessage()], 500);
            }

            return back()->with('error', 'Export failed: ' . $e->getMessage());
        }
    }

    /**
     * Export transport entries as CSV (fallback method)
     */
    public function exportTransportCSV(Request $request)
    {
        try {
            $user = auth()->user();

            if (!$user) {
                return response()->json(['error' => 'User not authenticated'], 401);
            }

            // Get accessible company IDs
            $companyIds = $user->canAccessAllCompanies()
                ? null
                : $user->getAccessibleCompanyIds();

            // Get filters from request
            $filters = $request->only(['status', 'date_from', 'date_to']);

            // Build query
            $query = \App\Models\TransportEntry::with(['customer', 'company', 'user']);

            // Apply company filter
            if ($companyIds) {
                $query->whereIn('company_id', $companyIds);
            }

            // Apply additional filters
            if (!empty($filters['status'])) {
                $query->where('status', $filters['status']);
            }

            if (!empty($filters['date_from'])) {
                $query->whereDate('transport_date', '>=', $filters['date_from']);
            }

            if (!empty($filters['date_to'])) {
                $query->whereDate('transport_date', '<=', $filters['date_to']);
            }

            $transportEntries = $query->orderBy('transport_date', 'desc')->get();

            // Create CSV content
            $csv = "Sales Entry,Transport Date,Customer Name,Customer Phone,Company,Vehicle,Driver Details,Delivery Date,Status,Created At\n";

            foreach ($transportEntries as $entry) {
                $csv .= sprintf(
                    '"%s","%s","%s","%s","%s","%s","%s","%s","%s","%s"' . "\n",
                    $entry->sales_entry ?? 'N/A',
                    $entry->transport_date ? $entry->transport_date->format('d-m-Y') : 'N/A',
                    optional($entry->customer)->name ?? 'N/A',
                    optional($entry->customer)->phone ?? 'N/A',
                    optional($entry->company)->name ?? 'N/A',
                    $entry->vehicle ?? 'N/A',
                    $entry->driver_details ?? 'N/A',
                    $entry->delivery_date ? $entry->delivery_date->format('d-m-Y') : 'N/A',
                    ucfirst($entry->status ?? 'pending'),
                    $entry->created_at ? $entry->created_at->format('d-m-Y H:i') : 'N/A'
                );
            }

            $filename = 'transport_entries_' . date('Y-m-d_H-i-s') . '.csv';

            return response($csv)
                ->header('Content-Type', 'text/csv')
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');

        } catch (\Exception $e) {
            \Log::error('Transport CSV export error: ' . $e->getMessage());
            return response()->json(['error' => 'CSV export failed: ' . $e->getMessage()], 500);
        }
    }
}
