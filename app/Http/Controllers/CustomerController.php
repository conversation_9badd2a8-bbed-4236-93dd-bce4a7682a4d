<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\State;
use App\Models\City;
use App\Traits\CompanyDataAccess;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class CustomerController extends Controller
{
    use CompanyDataAccess;
    /**
     * Display a listing of the customers.
     */
    public function index()
    {
        // Apply company-based filtering
        $query = Customer::with(['state', 'city', 'company']);
        $customers = $this->applyCompanyFilter($query)
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        // Get accessible companies for filter dropdown (if super admin)
        $accessibleCompanies = $this->getAccessibleCompanies();

        return view('masters.customers.index', compact('customers', 'accessibleCompanies'));
    }

    /**
     * Show the form for creating a new customer.
     */
    public function create()
    {
        $states = State::where('status', 'active')->orderBy('name')->get();
        return view('masters.customers.create', compact('states'));
    }

    /**
     * Store a newly created customer in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'nullable|email|max:255|unique:customers',
            'phone' => 'required|string|max:20',
            'address' => 'required|string|max:500',
            'state_id' => 'required|exists:states,id',
            'city_id' => 'required|exists:cities,id',
            'pincode' => 'required|string|max:10',
            'gst_number' => 'nullable|string|max:15|unique:customers',
            'status' => 'required|in:active,inactive'
        ]);

        $data = $request->all();
        $data['company_id'] = auth()->user()->current_company_id;

        $customer = Customer::create($data);

        // Check if this is an AJAX request
        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Customer created successfully!',
                'customer' => [
                    'id' => $customer->id,
                    'name' => $customer->name,
                    'phone' => $customer->phone,
                    'email' => $customer->email
                ]
            ]);
        }

        return redirect()->route('customers.index')
            ->with('success', 'Customer created successfully!');
    }

    /**
     * Display the specified customer.
     */
    public function show(Customer $customer)
    {
        // Ensure user can access this customer's company data
        $this->ensureCompanyAccess($customer->company_id);

        $customer->load(['state', 'city', 'salesEntries', 'company']);
        return view('masters.customers.show', compact('customer'));
    }

    /**
     * Show the form for editing the specified customer.
     */
    public function edit(Customer $customer)
    {
        // Ensure user can access this customer's company data
        $this->ensureCompanyAccess($customer->company_id);

        $states = State::where('status', 'active')->orderBy('name')->get();
        $cities = City::where('state_id', $customer->state_id)->orderBy('name')->get();
        return view('masters.customers.edit', compact('customer', 'states', 'cities'));
    }

    /**
     * Update the specified customer in storage.
     */
    public function update(Request $request, Customer $customer)
    {
        // Ensure customer belongs to current company
        if ($customer->company_id !== auth()->user()->current_company_id) {
            abort(404);
        }
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['nullable', 'email', 'max:255', Rule::unique('customers')->ignore($customer->id)],
            'phone' => 'required|string|max:20',
            'address' => 'required|string|max:500',
            'state_id' => 'required|exists:states,id',
            'city_id' => 'required|exists:cities,id',
            'pincode' => 'required|string|max:10',
            'gst_number' => ['nullable', 'string', 'max:15', Rule::unique('customers')->ignore($customer->id)],
            'status' => 'required|in:active,inactive'
        ]);

        $customer->update($request->all());

        return redirect()->route('customers.index')
            ->with('success', 'Customer updated successfully!');
    }

    /**
     * Remove the specified customer from storage.
     */
    public function destroy(Customer $customer)
    {
        // Ensure customer belongs to current company
        if ($customer->company_id !== auth()->user()->current_company_id) {
            abort(404);
        }

        // Check if customer has any sales entries
        if ($customer->salesEntries()->count() > 0) {
            return redirect()->route('customers.index')
                ->with('error', 'Cannot delete customer as they have sales entries.');
        }

        $customer->delete();

        return redirect()->route('customers.index')
            ->with('success', 'Customer deleted successfully!');
    }

    /**
     * Toggle customer status.
     */
    public function toggleStatus(Customer $customer)
    {
        // Ensure customer belongs to current company
        if ($customer->company_id !== auth()->user()->current_company_id) {
            abort(404);
        }

        $customer->update([
            'status' => $customer->status === 'active' ? 'inactive' : 'active'
        ]);

        return redirect()->route('customers.index')
            ->with('success', 'Customer status updated successfully!');
    }

    /**
     * Get cities by state (AJAX)
     */
    public function getCitiesByState($stateId)
    {
        \Log::info('getCitiesByState called with state ID: ' . $stateId);

        $cities = City::where('state_id', $stateId)->where('status', 'active')->orderBy('name')->get();

        \Log::info('Found ' . $cities->count() . ' cities for state ID: ' . $stateId);

        return response()->json($cities);
    }
}
