<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Role;
use App\Models\Permission;
use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class UserMasterController extends Controller
{
    /**
     * Display a listing of the users.
     */
    public function index(Request $request)
    {
        $query = User::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        // Company filter (if needed)
        if ($request->filled('company') && $request->company !== 'all') {
            $query->whereHas('companies', function($q) use ($request) {
                $q->where('companies.id', $request->company);
            });
        }

        // Status filter
        if ($request->filled('status') && $request->status !== 'all') {
            $query->where('status', $request->status);
        }

        // Sort functionality
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        
        if (in_array($sortBy, ['name', 'email', 'status', 'created_at'])) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $users = $query->paginate(10)->withQueryString();

        // Statistics
        $stats = [
            'total' => User::count(),
            'active' => User::where('status', 'active')->count(),
            'inactive' => User::where('status', 'inactive')->count(),
            'with_companies' => User::whereHas('companies')->count(),
            'without_companies' => User::whereDoesntHave('companies')->count(),
        ];
            
        return view('masters.users.index', compact('users', 'stats'));
    }

    /**
     * Show the form for creating a new user.
     */
    public function create()
    {
        $roles = \App\Models\Role::where('status', 'active')->orderBy('name')->get();
        $companies = \App\Models\Company::where('status', 'active')->get();
        $permissions = \App\Models\Permission::where('status', 'active')
                                           ->orderBy('module')
                                           ->orderBy('name')
                                           ->get()
                                           ->groupBy('module');

        return view('masters.users.create', compact('roles', 'companies', 'permissions'));
    }

    /**
     * Store a newly created user in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users',
            'phone' => 'nullable|string|max:20',
            'password' => 'required|string|min:8|confirmed',
            'status' => 'required|in:active,inactive',
            'companies' => 'required|array|min:1',
            'companies.*' => 'exists:companies,id',
            'company_roles' => 'required|array',
            'company_roles.*' => 'exists:roles,id'
        ]);

        // Create the user
        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'password' => Hash::make($request->password),
            'status' => $request->status,
            'current_company_id' => $request->companies[0] ?? null
        ]);

        // Assign user to companies with roles
        foreach ($request->companies as $index => $companyId) {
            $roleId = $request->company_roles[$index];
            $role = \App\Models\Role::find($roleId);

            $user->companies()->attach($companyId, [
                'role' => $role->slug,
                'role_id' => $roleId,
                'status' => 'active',
                'is_default' => $index === 0,
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }

        return redirect()->route('masters.users.index')
            ->with('success', 'User created successfully with company assignments!');
    }

    /**
     * Display the specified user.
     */
    public function show(User $user)
    {
        return view('masters.users.show', compact('user'));
    }

    /**
     * Show the form for editing the specified user.
     */
    public function edit(User $user)
    {
        $roles = \App\Models\Role::where('status', 'active')->orderBy('name')->get();
        $companies = \App\Models\Company::where('status', 'active')->get();
        $userCompanies = $user->companies()->withPivot('role_id', 'is_default')->get();

        return view('masters.users.edit', compact('user', 'roles', 'companies', 'userCompanies'));
    }

    /**
     * Update the specified user in storage.
     */
    public function update(Request $request, User $user)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'phone' => 'nullable|string|max:20',
            'password' => 'nullable|string|min:8|confirmed',
            'status' => 'required|in:active,inactive',
            'companies' => 'array',
            'companies.*' => 'exists:companies,id',
            'company_roles' => 'array',
            'company_roles.*' => 'exists:roles,id'
        ]);

        $data = $request->except(['password', 'password_confirmation', 'companies', 'company_roles']);

        if ($request->filled('password')) {
            $data['password'] = Hash::make($request->password);
        }

        $user->update($data);

        // Update company assignments
        if ($request->has('companies') && $request->has('company_roles')) {
            // Detach all existing companies
            $user->companies()->detach();

            // Attach new companies with roles
            foreach ($request->companies as $index => $companyId) {
                $roleId = $request->company_roles[$index];
                $role = \App\Models\Role::find($roleId);

                $user->companies()->attach($companyId, [
                    'role' => $role->slug,
                    'role_id' => $roleId,
                    'status' => 'active',
                    'is_default' => $index === 0,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }

            // Update current company if needed
            if (!in_array($user->current_company_id, $request->companies)) {
                $user->update(['current_company_id' => $request->companies[0] ?? null]);
            }
        }

        return redirect()->route('masters.users.index')
            ->with('success', 'User updated successfully!');
    }

    /**
     * Remove the specified user from storage.
     */
    public function destroy(User $user)
    {
        // Prevent deleting the current user
        if ($user->id === auth()->id()) {
            return redirect()->route('masters.users.index')
                ->with('error', 'You cannot delete your own account.');
        }

        // Check if user has associated records
        $salesCount = $user->salesEntries()->count();
        $transportCount = $user->transportEntries()->count();

        if ($salesCount > 0 || $transportCount > 0) {
            return redirect()->route('masters.users.index')
                ->with('error', 'Cannot delete user with associated sales or transport entries. Total entries: ' . ($salesCount + $transportCount));
        }

        $user->delete();

        return redirect()->route('masters.users.index')
            ->with('success', 'User deleted successfully!');
    }

    /**
     * Toggle user status.
     */
    public function toggleStatus(User $user)
    {
        // Prevent deactivating the current user
        if ($user->id === auth()->id() && $user->status === 'active') {
            return redirect()->route('masters.users.index')
                ->with('error', 'You cannot deactivate your own account.');
        }

        $user->update([
            'status' => $user->status === 'active' ? 'inactive' : 'active'
        ]);

        return redirect()->route('masters.users.index')
            ->with('success', 'User status updated successfully!');
    }

    /**
     * Bulk actions for users
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:activate,deactivate,delete',
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id'
        ]);

        $userIds = $request->user_ids;
        $currentUserId = auth()->id();

        // Remove current user from bulk actions to prevent self-modification
        $userIds = array_filter($userIds, function($id) use ($currentUserId) {
            return $id != $currentUserId;
        });

        if (empty($userIds)) {
            return redirect()->route('masters.users.index')
                ->with('error', 'No valid users selected for bulk action.');
        }

        switch ($request->action) {
            case 'activate':
                User::whereIn('id', $userIds)->update(['status' => 'active']);
                $message = 'Selected users have been activated successfully!';
                break;
            
            case 'deactivate':
                User::whereIn('id', $userIds)->update(['status' => 'inactive']);
                $message = 'Selected users have been deactivated successfully!';
                break;
            
            case 'delete':
                // Check for associated records before deletion
                $usersWithRecords = User::whereIn('id', $userIds)
                    ->whereHas('salesEntries')
                    ->orWhereHas('transportEntries')
                    ->pluck('name');

                if ($usersWithRecords->isNotEmpty()) {
                    return redirect()->route('masters.users.index')
                        ->with('error', 'Cannot delete users with associated records: ' . $usersWithRecords->implode(', '));
                }

                User::whereIn('id', $userIds)->delete();
                $message = 'Selected users have been deleted successfully!';
                break;
        }

        return redirect()->route('masters.users.index')->with('success', $message);
    }

    /**
     * Export users data
     */
    public function export(Request $request)
    {
        $users = User::with(['salesEntries', 'transportEntries'])
            ->when($request->role, function($query, $role) {
                return $query->where('role', $role);
            })
            ->when($request->status, function($query, $status) {
                return $query->where('status', $status);
            })
            ->get();

        $csvData = "ID,Name,Email,Phone,Role,Status,Sales Entries,Transport Entries,Created At\n";

        foreach ($users as $user) {
            $csvData .= sprintf(
                "%d,\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",%d,%d,\"%s\"\n",
                $user->id,
                $user->name,
                $user->email ?? '',
                $user->phone ?? '',
                $user->role,
                $user->status,
                $user->salesEntries->count(),
                $user->transportEntries->count(),
                $user->created_at->format('Y-m-d H:i:s')
            );
        }

        $filename = 'users_export_' . date('Y-m-d_H-i-s') . '.csv';

        return response($csvData)
            ->header('Content-Type', 'text/csv')
            ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
    }

    /**
     * Show import form
     */
    public function importForm()
    {
        $roles = Role::active()->orderBy('name')->get();
        $permissions = Permission::active()->orderBy('module')->orderBy('name')->get();
        $groupedPermissions = $permissions->groupBy('module');

        return view('masters.users.import', compact('roles', 'permissions', 'groupedPermissions'));
    }

    /**
     * Download import template
     */
    public function downloadTemplate()
    {
        $csvData = "Name,Email,Phone,Role Name,Permissions (comma-separated),Company ID,Status,Password\n";
        $csvData .= "John Doe,<EMAIL>,1234567890,Manager,\"users.view,users.create\",1,active,password123\n";
        $csvData .= "Jane Smith,<EMAIL>,0987654321,User,\"customers.view\",1,active,password456\n";
        $csvData .= "Admin User,<EMAIL>,1122334455,Administrator,\"users.view,users.create,users.edit,users.delete\",1,active,adminpass\n";

        $filename = 'users_import_template.csv';

        return response($csvData)
            ->header('Content-Type', 'text/csv')
            ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
    }

    /**
     * Preview import data
     */
    public function previewImport(Request $request)
    {
        $request->validate([
            'import_file' => 'required|file|mimes:csv,txt,xlsx|max:2048'
        ]);

        try {
            $file = $request->file('import_file');
            $data = $this->parseImportFile($file);

            $validationResults = $this->validateImportData($data);

            return response()->json([
                'success' => true,
                'data' => $data,
                'validation' => $validationResults,
                'total_rows' => count($data)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error processing file: ' . $e->getMessage()
            ], 400);
        }
    }

    /**
     * Process import
     */
    public function processImport(Request $request)
    {
        $request->validate([
            'import_file' => 'required|file|mimes:csv,txt,xlsx|max:2048',
            'skip_duplicates' => 'boolean',
            'update_existing' => 'boolean',
            'default_company_id' => 'nullable|exists:companies,id'
        ]);

        try {
            $file = $request->file('import_file');
            $data = $this->parseImportFile($file);

            $validationResults = $this->validateImportData($data);

            if (!empty($validationResults['errors'])) {
                return redirect()->back()
                    ->with('error', 'Import failed due to validation errors.')
                    ->with('validation_errors', $validationResults['errors']);
            }

            $results = $this->importUsers($data, [
                'skip_duplicates' => $request->boolean('skip_duplicates'),
                'update_existing' => $request->boolean('update_existing'),
                'default_company_id' => $request->input('default_company_id', 1)
            ]);

            return redirect()->route('masters.users.index')
                ->with('success', "Import completed successfully! Created: {$results['created']}, Updated: {$results['updated']}, Skipped: {$results['skipped']}");

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Import failed: ' . $e->getMessage());
        }
    }




    /**
     * Get user profile
     */
    public function profile()
    {
        $user = auth()->user();
        return view('masters.users.profile', compact('user'));
    }

    /**
     * Update user profile
     */
    public function updateProfile(Request $request)
    {
        $user = auth()->user();

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'phone' => 'nullable|string|max:20',
            'current_password' => 'required_with:password',
            'password' => 'nullable|string|min:8|confirmed',
        ]);

        // Verify current password if new password is provided
        if ($request->filled('password')) {
            if (!Hash::check($request->current_password, $user->password)) {
                return back()->withErrors(['current_password' => 'Current password is incorrect.']);
            }
        }

        $data = $request->only(['name', 'email', 'phone']);
        
        if ($request->filled('password')) {
            $data['password'] = Hash::make($request->password);
        }

        $user->update($data);

        return redirect()->route('users.profile')
            ->with('success', 'Profile updated successfully!');
    }

    /**
     * Parse import file (CSV or Excel) with role support
     */
    private function parseImportFile($file)
    {
        $extension = $file->getClientOriginalExtension();
        $data = [];

        if (in_array($extension, ['csv', 'txt'])) {
            $handle = fopen($file->getPathname(), 'r');
            $header = fgetcsv($handle);

            // Normalize headers
            $header = array_map(function($h) {
                return strtolower(str_replace([' ', '-'], '_', trim($h)));
            }, $header);

            while (($row = fgetcsv($handle)) !== false) {
                if (count($row) === count($header)) {
                    $data[] = array_combine($header, $row);
                }
            }
            fclose($handle);
        } elseif ($extension === 'xlsx') {
            throw new \Exception('Excel files not yet supported. Please use CSV format.');
        }

        return $data;
    }

    /**
     * Validate import data with role and permission support
     */
    private function validateImportData($data)
    {
        $errors = [];
        $warnings = [];
        $validStatuses = ['active', 'inactive'];
        $existingEmails = User::pluck('email')->toArray();
        $existingRoles = Role::pluck('name', 'slug')->toArray();
        $existingPermissions = Permission::pluck('slug')->toArray();

        foreach ($data as $index => $row) {
            $rowNumber = $index + 2;
            $rowErrors = [];

            // Required fields validation
            if (empty(trim($row['name'] ?? ''))) {
                $rowErrors[] = "Name is required";
            }

            if (empty(trim($row['email'] ?? ''))) {
                $rowErrors[] = "Email is required";
            } elseif (!filter_var(trim($row['email']), FILTER_VALIDATE_EMAIL)) {
                $rowErrors[] = "Invalid email format";
            } elseif (in_array(trim($row['email']), $existingEmails)) {
                $warnings[] = "Row {$rowNumber}: Email already exists - " . trim($row['email']);
            }

            // Role validation
            if (!empty(trim($row['role_name'] ?? ''))) {
                $roleName = trim($row['role_name']);
                $roleExists = Role::where('name', $roleName)->orWhere('slug', Str::slug($roleName))->exists();
                if (!$roleExists) {
                    $rowErrors[] = "Role '{$roleName}' does not exist";
                }
            }

            // Permissions validation
            if (!empty(trim($row['permissions'] ?? ''))) {
                $permissions = array_map('trim', explode(',', $row['permissions']));
                foreach ($permissions as $permission) {
                    if (!in_array($permission, $existingPermissions)) {
                        $rowErrors[] = "Permission '{$permission}' does not exist";
                    }
                }
            }

            // Status validation
            if (empty(trim($row['status'] ?? ''))) {
                $rowErrors[] = "Status is required";
            } elseif (!in_array(strtolower(trim($row['status'])), $validStatuses)) {
                $rowErrors[] = "Invalid status. Must be one of: " . implode(', ', $validStatuses);
            }

            // Password validation
            if (empty(trim($row['password'] ?? ''))) {
                $rowErrors[] = "Password is required";
            } elseif (strlen(trim($row['password'])) < 8) {
                $rowErrors[] = "Password must be at least 8 characters";
            }

            // Company ID validation
            if (!empty(trim($row['company_id'] ?? ''))) {
                $companyId = trim($row['company_id']);
                if (!is_numeric($companyId) || !Company::find($companyId)) {
                    $rowErrors[] = "Invalid company ID: {$companyId}";
                }
            }

            if (!empty($rowErrors)) {
                $errors["Row {$rowNumber}"] = $rowErrors;
            }
        }

        return [
            'errors' => $errors,
            'warnings' => $warnings,
            'valid_count' => count($data) - count($errors)
        ];
    }

    /**
     * Import users from validated data with role and permission support
     */
    private function importUsers($data, $options = [])
    {
        $created = 0;
        $updated = 0;
        $skipped = 0;
        $skipDuplicates = $options['skip_duplicates'] ?? false;
        $updateExisting = $options['update_existing'] ?? false;
        $defaultCompanyId = $options['default_company_id'] ?? 1;

        foreach ($data as $row) {
            $email = trim($row['email']);
            $existingUser = User::where('email', $email)->first();

            $userData = [
                'name' => trim($row['name']),
                'phone' => trim($row['phone'] ?? ''),
                'status' => strtolower(trim($row['status'])),
            ];

            if (!empty(trim($row['password']))) {
                $userData['password'] = Hash::make(trim($row['password']));
            }

            if ($existingUser) {
                if ($updateExisting) {
                    $existingUser->update($userData);
                    $this->assignRoleAndPermissions($existingUser, $row, $defaultCompanyId);
                    $updated++;
                } else {
                    $skipped++;
                }
            } else {
                $userData['email'] = $email;
                $user = User::create($userData);
                $this->assignRoleAndPermissions($user, $row, $defaultCompanyId);
                $created++;
            }
        }

        return [
            'created' => $created,
            'updated' => $updated,
            'skipped' => $skipped
        ];
    }

    /**
     * Assign role and permissions to user
     */
    private function assignRoleAndPermissions($user, $row, $companyId)
    {
        // Assign role if specified
        if (!empty(trim($row['role_name'] ?? ''))) {
            $roleName = trim($row['role_name']);
            $role = Role::where('name', $roleName)->orWhere('slug', Str::slug($roleName))->first();

            if ($role) {
                // Create or update company_users record
                $user->companies()->syncWithoutDetaching([
                    $companyId => [
                        'role_id' => $role->id,
                        'status' => $user->status,
                        'is_default' => true
                    ]
                ]);
            }
        }

        // Assign individual permissions if specified
        if (!empty(trim($row['permissions'] ?? ''))) {
            $permissions = array_map('trim', explode(',', $row['permissions']));
            $permissionIds = Permission::whereIn('slug', $permissions)->pluck('id')->toArray();

            if (!empty($permissionIds)) {
                $syncData = [];
                foreach ($permissionIds as $permissionId) {
                    $syncData[$permissionId] = ['company_id' => $companyId];
                }
                $user->permissions()->syncWithoutDetaching($syncData);
            }
        }
    }
}
