<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\SalesEntry;
use App\Models\TransportEntry;
use App\Models\Customer;
use App\Models\Product;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class ReportController extends Controller
{
    /**
     * Daily Report
     */
    public function daily(Request $request)
    {
        $date = $request->get('date', Carbon::today()->format('Y-m-d'));
        $selected_date = Carbon::parse($date);

        // Daily statistics
        $daily_stats = [
            'quotations_created' => SalesEntry::whereDate('created_at', $selected_date)->count(),
            'quotations_approved' => SalesEntry::whereDate('updated_at', $selected_date)
                ->where('status', 'approved')->count(),
            'total_sales_value' => SalesEntry::whereDate('created_at', $selected_date)
                ->sum('total_amount'),
            'transports_scheduled' => TransportEntry::whereDate('transport_date', $selected_date)->count(),
            'transports_delivered' => TransportEntry::whereDate('updated_at', $selected_date)
                ->where('status', 'delivered')->count(),
        ];

        // Daily quotations
        $daily_quotations = SalesEntry::with(['customer', 'user'])
            ->whereDate('created_at', $selected_date)
            ->orderBy('created_at', 'desc')
            ->get();

        // Daily transports
        $daily_transports = TransportEntry::with(['sales_entry.customer'])
            ->whereDate('transport_date', $selected_date)
            ->orderBy('transport_date', 'desc')
            ->get();

        // Customer activity for the selected date
        $customerActivity = DB::table('customers')
            ->select([
                'customers.name as customer_name',
                DB::raw('COUNT(sales_entries.id) as quotation_count'),
                DB::raw('SUM(sales_entries.total_amount) as total_value'),
                DB::raw('MAX(sales_entries.created_at) as last_activity')
            ])
            ->join('sales_entries', 'customers.id', '=', 'sales_entries.customer_id')
            ->whereDate('sales_entries.created_at', $selected_date)
            ->whereNull('customers.deleted_at')
            ->groupBy('customers.id', 'customers.name')
            ->orderBy('total_value', 'desc')
            ->get();

        return view('reports.daily', compact(
            'daily_stats',
            'daily_quotations',
            'daily_transports',
            'selected_date',
            'customerActivity'
        ));
    }

    /**
     * Monthly Report
     */
    public function monthly(Request $request)
    {
        $month = $request->get('month', Carbon::now()->format('Y-m'));
        $selected_month = Carbon::parse($month . '-01');

        // Monthly statistics
        $monthly_stats = [
            'total_quotations' => SalesEntry::whereYear('created_at', $selected_month->year)
                ->whereMonth('created_at', $selected_month->month)->count(),
            'approved_quotations' => SalesEntry::whereYear('created_at', $selected_month->year)
                ->whereMonth('created_at', $selected_month->month)
                ->where('status', 'approved')->count(),
            'total_sales_value' => SalesEntry::whereYear('created_at', $selected_month->year)
                ->whereMonth('created_at', $selected_month->month)
                ->sum('total_amount'),
            'avg_quotation_value' => SalesEntry::whereYear('created_at', $selected_month->year)
                ->whereMonth('created_at', $selected_month->month)
                ->avg('total_amount'),
            'total_transports' => TransportEntry::whereYear('transport_date', $selected_month->year)
                ->whereMonth('transport_date', $selected_month->month)->count(),
        ];

        // Daily breakdown for the month
        $daily_breakdown = SalesEntry::selectRaw('DATE(created_at) as date, COUNT(*) as quotations, SUM(total_amount) as sales_value')
            ->whereYear('created_at', $selected_month->year)
            ->whereMonth('created_at', $selected_month->month)
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Top customers for the month
        $top_customers = Customer::select('customers.*')
            ->selectRaw('COUNT(sales_entries.id) as quotation_count, SUM(sales_entries.total_amount) as total_value')
            ->leftJoin('sales_entries', 'customers.id', '=', 'sales_entries.customer_id')
            ->whereYear('sales_entries.created_at', $selected_month->year)
            ->whereMonth('sales_entries.created_at', $selected_month->month)
            ->groupBy('customers.id')
            ->orderBy('total_value', 'desc')
            ->take(10)
            ->get();

        // Top products for the month (placeholder data)
        $topProducts = collect([
            (object)['product_name' => 'Product A', 'total_quantity' => 100, 'total_revenue' => 50000],
            (object)['product_name' => 'Product B', 'total_quantity' => 80, 'total_revenue' => 40000],
            (object)['product_name' => 'Product C', 'total_quantity' => 60, 'total_revenue' => 30000],
        ]);

        // Performance metrics
        $total_quotations = $monthly_stats['total_quotations'];
        $approved_quotations = $monthly_stats['approved_quotations'];
        $performanceMetrics = [
            'approval_rate' => $total_quotations > 0 ? ($approved_quotations / $total_quotations) * 100 : 0,
            'avg_processing_days' => 3.5,
            'active_customers' => $top_customers->count(),
            'delivery_rate' => 95.2
        ];

        // Daily trend data for chart
        $dailyTrendData = [
            'labels' => $daily_breakdown->pluck('date')->map(function($date) {
                return Carbon::parse($date)->format('M d');
            })->toArray(),
            'data' => $daily_breakdown->pluck('quotations')->toArray()
        ];

        // Status distribution for pie chart
        $statusDistribution = [
            'labels' => ['Pending', 'Approved', 'Rejected'],
            'data' => [
                $total_quotations - $approved_quotations,
                $approved_quotations,
                0
            ]
        ];

        return view('reports.monthly', compact(
            'monthly_stats',
            'daily_breakdown',
            'top_customers',
            'selected_month',
            'topProducts',
            'performanceMetrics',
            'dailyTrendData',
            'statusDistribution'
        ));
    }

    /**
     * Quotation Report
     */
    public function quotation(Request $request)
    {
        $status = $request->get('status', 'all');
        $customer_id = $request->get('customer_id');
        $date_from = $request->get('date_from');
        $date_to = $request->get('date_to');
        $amount_min = $request->get('amount_min');

        $query = SalesEntry::with(['customer', 'user', 'items.product']);

        // Apply filters
        if ($status !== 'all') {
            $query->where('status', $status);
        }

        if ($customer_id) {
            $query->where('customer_id', $customer_id);
        }

        if ($date_from) {
            $query->whereDate('quotation_date', '>=', $date_from);
        }

        if ($date_to) {
            $query->whereDate('quotation_date', '<=', $date_to);
        }

        if ($amount_min) {
            $query->where('total_amount', '>=', $amount_min);
        }

        $quotations = $query->orderBy('quotation_date', 'desc')->paginate(20);

        // Summary statistics
        $summary = [
            'total_quotations' => $query->count(),
            'total_value' => $query->sum('total_amount'),
            'avg_value' => $query->avg('total_amount'),
            'pending_count' => SalesEntry::where('status', 'pending')->count(),
            'approved_count' => SalesEntry::where('status', 'approved')->count(),
            'rejected_count' => SalesEntry::where('status', 'rejected')->count(),
        ];

        $customers = Customer::where('status', 'active')->get();

        return view('reports.quotation', compact(
            'quotations',
            'summary',
            'customers',
            'status',
            'customer_id',
            'date_from',
            'date_to',
            'amount_min'
        ));
    }

    /**
     * Sales Report
     */
    public function sales(Request $request)
    {
        $period = $request->get('period', 'monthly');
        $year = $request->get('year', Carbon::now()->year);

        // Sales performance data
        if ($period === 'monthly') {
            $sales_data = SalesEntry::selectRaw('MONTH(quotation_date) as period, COUNT(*) as quotations, SUM(total_amount) as sales_value')
                ->whereYear('quotation_date', $year)
                ->where('status', 'approved')
                ->groupBy('period')
                ->orderBy('period')
                ->get();
        } else {
            $sales_data = SalesEntry::selectRaw('QUARTER(quotation_date) as period, COUNT(*) as quotations, SUM(total_amount) as sales_value')
                ->whereYear('quotation_date', $year)
                ->where('status', 'approved')
                ->groupBy('period')
                ->orderBy('period')
                ->get();
        }

        // Top products - using subquery to avoid GROUP BY issues
        $productStats = \DB::table('products')
            ->select([
                'products.id',
                \DB::raw('SUM(sales_entry_items.quantity) as total_quantity'),
                \DB::raw('SUM(sales_entry_items.line_total) as total_value')
            ])
            ->leftJoin('sales_entry_items', 'products.id', '=', 'sales_entry_items.product_id')
            ->leftJoin('sales_entries', 'sales_entry_items.sales_entry_id', '=', 'sales_entries.id')
            ->whereYear('sales_entries.quotation_date', $year)
            ->where('sales_entries.status', 'approved')
            ->whereNull('products.deleted_at')
            ->groupBy('products.id');

        $top_products = Product::select('products.*')
            ->joinSub($productStats, 'stats', function ($join) {
                $join->on('products.id', '=', 'stats.id');
            })
            ->selectRaw('stats.total_quantity, stats.total_value')
            ->orderBy('stats.total_value', 'desc')
            ->take(10)
            ->get();

        // Sales by customer - using subquery to avoid GROUP BY issues
        $customerSalesStats = \DB::table('customers')
            ->select([
                'customers.id',
                \DB::raw('COUNT(sales_entries.id) as quotation_count'),
                \DB::raw('SUM(sales_entries.total_amount) as total_value')
            ])
            ->leftJoin('sales_entries', 'customers.id', '=', 'sales_entries.customer_id')
            ->whereYear('sales_entries.quotation_date', $year)
            ->where('sales_entries.status', 'approved')
            ->whereNull('customers.deleted_at')
            ->groupBy('customers.id');

        $sales_by_customer = Customer::select('customers.*')
            ->joinSub($customerSalesStats, 'stats', function ($join) {
                $join->on('customers.id', '=', 'stats.id');
            })
            ->selectRaw('stats.quotation_count, stats.total_value')
            ->orderBy('stats.total_value', 'desc')
            ->take(10)
            ->get();

        return view('reports.sales', compact(
            'sales_data',
            'top_products',
            'sales_by_customer',
            'period',
            'year'
        ));
    }

    /**
     * Customer Report
     */
    public function customer(Request $request)
    {
        $sort_by = $request->get('sort_by', 'total_value');
        $order = $request->get('order', 'desc');

        // Customer statistics - using a more efficient approach with subquery
        $customerStats = \DB::table('customers')
            ->select([
                'customers.id',
                \DB::raw('COUNT(sales_entries.id) as quotation_count'),
                \DB::raw('SUM(CASE WHEN sales_entries.status = "approved" THEN sales_entries.total_amount ELSE 0 END) as total_value'),
                \DB::raw('AVG(CASE WHEN sales_entries.status = "approved" THEN sales_entries.total_amount ELSE NULL END) as avg_value'),
                \DB::raw('MAX(sales_entries.quotation_date) as last_quotation_date')
            ])
            ->leftJoin('sales_entries', 'customers.id', '=', 'sales_entries.customer_id')
            ->whereNull('customers.deleted_at')
            ->groupBy('customers.id');

        // Get customers with their statistics
        $customers = Customer::select('customers.*')
            ->joinSub($customerStats, 'stats', function ($join) {
                $join->on('customers.id', '=', 'stats.id');
            })
            ->selectRaw('stats.quotation_count, stats.total_value, stats.avg_value, stats.last_quotation_date')
            ->orderBy($sort_by, $order)
            ->paginate(20);

        // Summary
        $summary = [
            'total_customers' => Customer::count(),
            'active_customers' => Customer::where('status', 'active')->count(),
            'customers_with_quotations' => Customer::whereHas('salesEntries')->count(),
            'avg_quotations_per_customer' => SalesEntry::count() / max(Customer::count(), 1),
        ];

        return view('reports.customer', compact(
            'customers',
            'summary',
            'sort_by',
            'order'
        ));
    }
}
