<?php

namespace App\Http\Controllers;

use App\Models\Role;
use App\Models\Permission;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class RoleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $roles = Role::with('permissions')->orderBy('name')->paginate(15);
        return view('admin.roles.index', compact('roles'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $permissions = Permission::active()->orderBy('module')->orderBy('name')->get();
        $groupedPermissions = $permissions->groupBy('module');

        return view('admin.roles.create', compact('permissions', 'groupedPermissions'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'status' => 'required|in:active,inactive',
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,id'
        ]);



        $role = Role::create([
            'name' => $request->name,
            'slug' => Str::slug($request->name),
            'description' => $request->description,
            'status' => $request->status
        ]);

        if ($request->has('permissions')) {
            $role->permissions()->sync($request->permissions);
        }

        return redirect()->route('admin.roles.index')
            ->with('success', 'Role created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Role $role)
    {
        $role->load('permissions');
        return view('admin.roles.show', compact('role'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Role $role)
    {
        // Load role with permissions
        $role->load('permissions');

        // Get all permissions grouped by module
        $permissions = Permission::active()->orderBy('module')->orderBy('name')->get();
        $groupedPermissions = $permissions->groupBy('module');

        // Get current role permissions
        $rolePermissions = $role->permissions->pluck('id')->toArray();

        return view('admin.roles.edit', compact('role', 'permissions', 'groupedPermissions', 'rolePermissions'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Role $role)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'description' => 'nullable|string',
            'status' => 'required|in:active,inactive',
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,id'
        ]);

        $role->update([
            'name' => $request->name,
            'slug' => Str::slug($request->name),
            'description' => $request->description,
            'status' => $request->status
        ]);

        if ($request->has('permissions')) {
            $role->permissions()->sync($request->permissions);
        } else {
            $role->permissions()->sync([]);
        }

        // Refresh permissions for all users with this role
        \App\Models\User::refreshPermissionsForRole($role->id);

        // Set session flag to refresh permissions on next request
        session()->flash('role_updated', true);

        return redirect()->route('admin.roles.index')
            ->with('success', 'Role updated successfully! User permissions have been refreshed.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Role $role)
    {
        // Check if role is being used
        $usersCount = $role->users()->count();

        if ($usersCount > 0) {
            return redirect()->route('admin.roles.index')
                ->with('error', 'Cannot delete role. It is assigned to ' . $usersCount . ' user(s).');
        }

        $role->delete();

        return redirect()->route('admin.roles.index')
            ->with('success', 'Role deleted successfully!');
    }

    /**
     * Toggle role status.
     */
    public function toggleStatus(Role $role)
    {
        $role->update([
            'status' => $role->status === 'active' ? 'inactive' : 'active'
        ]);

        return redirect()->route('admin.roles.index')
            ->with('success', 'Role status updated successfully!');
    }

    /**
     * Get roles for AJAX requests
     */
    public function getRoles()
    {
        $roles = Role::where('status', 'active')
                    ->orderBy('name')
                    ->get(['id', 'name', 'slug', 'description']);

        return response()->json($roles);
    }

    /**
     * Get role permissions for AJAX requests
     */
    public function getRolePermissions(Role $role)
    {
        $permissions = $role->permissions()
                           ->where('status', 'active')
                           ->get(['id', 'name', 'slug', 'module']);

        return response()->json($permissions);
    }
}
