<?php

namespace App\Exports;

use App\Models\TransportEntry;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class SimpleTransportExport implements FromCollection, WithHeadings, WithMapping
{
    protected $companyIds;
    protected $filters;

    public function __construct($companyIds = null, $filters = [])
    {
        $this->companyIds = $companyIds;
        $this->filters = $filters;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $query = TransportEntry::with(['customer', 'company', 'user']);

        // Apply company filter
        if ($this->companyIds) {
            $query->whereIn('company_id', $this->companyIds);
        }

        // Apply additional filters
        if (!empty($this->filters['status'])) {
            $query->where('status', $this->filters['status']);
        }

        if (!empty($this->filters['date_from'])) {
            $query->whereDate('transport_date', '>=', $this->filters['date_from']);
        }

        if (!empty($this->filters['date_to'])) {
            $query->whereDate('transport_date', '<=', $this->filters['date_to']);
        }

        return $query->orderBy('transport_date', 'desc')->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'Sales Entry',
            'Transport Date',
            'Customer Name',
            'Customer Phone',
            'Company',
            'Vehicle',
            'Driver Details',
            'Delivery Date',
            'Status',
            'Created At',
        ];
    }

    /**
     * @param mixed $transportEntry
     * @return array
     */
    public function map($transportEntry): array
    {
        return [
            $transportEntry->sales_entry ?? 'N/A',
            $transportEntry->transport_date ? $transportEntry->transport_date->format('d-m-Y') : 'N/A',
            optional($transportEntry->customer)->name ?? 'N/A',
            optional($transportEntry->customer)->phone ?? 'N/A',
            optional($transportEntry->company)->name ?? 'N/A',
            $transportEntry->vehicle ?? 'N/A',
            $transportEntry->driver_details ?? 'N/A',
            $transportEntry->delivery_date ? $transportEntry->delivery_date->format('d-m-Y') : 'N/A',
            ucfirst($transportEntry->status ?? 'pending'),
            $transportEntry->created_at ? $transportEntry->created_at->format('d-m-Y H:i') : 'N/A',
        ];
    }
}
