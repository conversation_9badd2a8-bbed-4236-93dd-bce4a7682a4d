<?php

namespace App\Exports;

use App\Models\SalesEntry;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class SalesExport implements FromCollection, WithHeadings, WithMapping, WithStyles, WithColumnWidths, WithTitle
{
    protected $companyIds;
    protected $filters;

    public function __construct($companyIds = null, $filters = [])
    {
        $this->companyIds = $companyIds;
        $this->filters = $filters;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $query = SalesEntry::with(['customer', 'company', 'items.product']);

        // Apply company filter
        if ($this->companyIds) {
            $query->whereIn('company_id', $this->companyIds);
        }

        // Apply additional filters
        if (!empty($this->filters['status'])) {
            $query->where('status', $this->filters['status']);
        }

        if (!empty($this->filters['customer_id'])) {
            $query->where('customer_id', $this->filters['customer_id']);
        }

        if (!empty($this->filters['date_from'])) {
            $query->whereDate('quotation_date', '>=', $this->filters['date_from']);
        }

        if (!empty($this->filters['date_to'])) {
            $query->whereDate('quotation_date', '<=', $this->filters['date_to']);
        }

        return $query->orderBy('quotation_date', 'desc')->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'Quotation No',
            'Date',
            'Customer Name',
            'Customer Phone',
            'Customer Email',
            'Company',
            'Status',
            'Items Count',
            'Subtotal',
            'CGST',
            'SGST',
            'IGST',
            'Insurance',
            'Freight Advance',
            'TCS Amount',
            'Grand Total',
            'Valid Until',
            'Created By',
            'Created At',
        ];
    }

    /**
     * @param mixed $salesEntry
     * @return array
     */
    public function map($salesEntry): array
    {
        return [
            $salesEntry->quotation_number ?? 'N/A',
            $salesEntry->quotation_date ? $salesEntry->quotation_date->format('d-m-Y') : 'N/A',
            optional($salesEntry->customer)->name ?? 'N/A',
            optional($salesEntry->customer)->phone ?? 'N/A',
            optional($salesEntry->customer)->email ?? 'N/A',
            optional($salesEntry->company)->name ?? 'N/A',
            ucfirst($salesEntry->status ?? 'pending'),
            $salesEntry->items ? $salesEntry->items->count() : 0,
            number_format($salesEntry->subtotal ?? 0, 2),
            number_format($salesEntry->total_cgst ?? 0, 2),
            number_format($salesEntry->total_sgst ?? 0, 2),
            number_format($salesEntry->total_igst ?? 0, 2),
            number_format($salesEntry->insurance ?? 0, 2),
            number_format($salesEntry->frt_advance ?? 0, 2),
            number_format($salesEntry->tcs_amount ?? 0, 2),
            number_format($salesEntry->calculated_net_amount ?? $salesEntry->grand_total ?? $salesEntry->total_amount ?? 0, 2),
            $salesEntry->valid_until ? $salesEntry->valid_until->format('d-m-Y') : 'N/A',
            optional($salesEntry->user)->name ?? 'System',
            $salesEntry->created_at ? $salesEntry->created_at->format('d-m-Y H:i') : 'N/A',
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as header
            1 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF'],
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '4e73df'],
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => '000000'],
                    ],
                ],
            ],
            // Style all data rows
            'A2:S1000' => [
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => 'CCCCCC'],
                    ],
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_LEFT,
                ],
            ],
        ];
    }

    /**
     * @return array
     */
    public function columnWidths(): array
    {
        return [
            'A' => 15, // Quotation No
            'B' => 12, // Date
            'C' => 25, // Customer Name
            'D' => 15, // Customer Phone
            'E' => 25, // Customer Email
            'F' => 20, // Company
            'G' => 12, // Status
            'H' => 10, // Items Count
            'I' => 12, // Subtotal
            'J' => 10, // CGST
            'K' => 10, // SGST
            'L' => 10, // IGST
            'M' => 12, // Insurance
            'N' => 15, // Freight Advance
            'O' => 12, // TCS Amount
            'P' => 15, // Grand Total
            'Q' => 12, // Valid Until
            'R' => 15, // Created By
            'S' => 18, // Created At
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'Sales Entries';
    }
}
