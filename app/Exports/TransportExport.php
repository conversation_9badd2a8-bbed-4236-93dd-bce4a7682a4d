<?php

namespace App\Exports;

use App\Models\TransportEntry;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class TransportExport implements FromCollection, WithHeadings, WithMapping, WithStyles, WithColumnWidths, WithTitle
{
    protected $companyIds;
    protected $filters;

    public function __construct($companyIds = null, $filters = [])
    {
        $this->companyIds = $companyIds;
        $this->filters = $filters;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $query = TransportEntry::with(['salesEntry.customer', 'company', 'user']);

        // Apply company filter
        if ($this->companyIds) {
            $query->whereIn('company_id', $this->companyIds);
        }

        // Apply additional filters
        if (!empty($this->filters['status'])) {
            $query->where('status', $this->filters['status']);
        }

        if (!empty($this->filters['date_from'])) {
            $query->whereDate('transport_date', '>=', $this->filters['date_from']);
        }

        if (!empty($this->filters['date_to'])) {
            $query->whereDate('transport_date', '<=', $this->filters['date_to']);
        }

        return $query->orderBy('transport_date', 'desc')->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'Transport ID',
            'Sales Entry',
            'Transport Date',
            'Customer Name',
            'From Location',
            'To Location',
            'Vehicle Number',
            'Driver Name',
            'Driver Phone',
            'Company',
            'Status',
            'Gross Weight',
            'Tare Weight',
            'Net Weight',
            'Transport Cost',
            'Advance Amount',
            'Balance Amount',
            'Estimated Delivery',
            'Actual Delivery',
            'Distance (KM)',
            'Fuel Cost',
            'Toll Cost',
            'Other Expenses',
            'Created By',
            'Created At',
        ];
    }

    /**
     * @param mixed $transportEntry
     * @return array
     */
    public function map($transportEntry): array
    {
        return [
            $transportEntry->id,
            $transportEntry->salesEntry->quotation_number ?? 'N/A',
            $transportEntry->transport_date ? $transportEntry->transport_date->format('d-m-Y') : 'N/A',
            $transportEntry->salesEntry->customer->name ?? 'N/A',
            $transportEntry->from_location ?? 'N/A',
            $transportEntry->to_location ?? 'N/A',
            $transportEntry->vehicle_number ?? 'N/A',
            $transportEntry->driver_name ?? 'N/A',
            $transportEntry->driver_phone ?? 'N/A',
            $transportEntry->company->name ?? 'N/A',
            ucfirst($transportEntry->status ?? 'scheduled'),
            number_format($transportEntry->gross_weight ?? 0, 2) . ' KG',
            number_format($transportEntry->tare_weight ?? 0, 2) . ' KG',
            number_format($transportEntry->net_weight ?? 0, 2) . ' KG',
            number_format($transportEntry->transport_cost ?? 0, 2),
            number_format($transportEntry->advance_amount ?? 0, 2),
            number_format($transportEntry->balance_amount ?? 0, 2),
            $transportEntry->estimated_delivery ? $transportEntry->estimated_delivery->format('d-m-Y') : 'N/A',
            $transportEntry->actual_delivery ? $transportEntry->actual_delivery->format('d-m-Y') : 'N/A',
            $transportEntry->distance_km ?? 'N/A',
            number_format($transportEntry->fuel_cost ?? 0, 2),
            number_format($transportEntry->toll_cost ?? 0, 2),
            number_format($transportEntry->other_expenses ?? 0, 2),
            $transportEntry->user->name ?? 'System',
            $transportEntry->created_at->format('d-m-Y H:i'),
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as header
            1 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF'],
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '1cc88a'],
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => '000000'],
                    ],
                ],
            ],
            // Style all data rows
            'A2:Y1000' => [
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => 'CCCCCC'],
                    ],
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_LEFT,
                ],
            ],
        ];
    }

    /**
     * @return array
     */
    public function columnWidths(): array
    {
        return [
            'A' => 12, // Transport ID
            'B' => 15, // Sales Entry
            'C' => 12, // Transport Date
            'D' => 25, // Customer Name
            'E' => 20, // From Location
            'F' => 20, // To Location
            'G' => 15, // Vehicle Number
            'H' => 15, // Driver Name
            'I' => 15, // Driver Phone
            'J' => 20, // Company
            'K' => 12, // Status
            'L' => 12, // Gross Weight
            'M' => 12, // Tare Weight
            'N' => 12, // Net Weight
            'O' => 15, // Transport Cost
            'P' => 15, // Advance Amount
            'Q' => 15, // Balance Amount
            'R' => 15, // Estimated Delivery
            'S' => 15, // Actual Delivery
            'T' => 12, // Distance
            'U' => 12, // Fuel Cost
            'V' => 12, // Toll Cost
            'W' => 15, // Other Expenses
            'X' => 15, // Created By
            'Y' => 18, // Created At
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'Transport Entries';
    }
}
