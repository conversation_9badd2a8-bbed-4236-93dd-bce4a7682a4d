<?php

namespace App\Exports;

use App\Models\SalesEntry;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class SimpleSalesExport implements FromCollection, WithHeadings, WithMapping
{
    protected $companyIds;
    protected $filters;

    public function __construct($companyIds = null, $filters = [])
    {
        $this->companyIds = $companyIds;
        $this->filters = $filters;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $query = SalesEntry::with(['customer', 'company', 'items.product', 'user']);

        // Apply company filter
        if ($this->companyIds) {
            $query->whereIn('company_id', $this->companyIds);
        }

        // Apply additional filters
        if (!empty($this->filters['status'])) {
            $query->where('status', $this->filters['status']);
        }

        if (!empty($this->filters['customer_id'])) {
            $query->where('customer_id', $this->filters['customer_id']);
        }

        if (!empty($this->filters['date_from'])) {
            $query->whereDate('quotation_date', '>=', $this->filters['date_from']);
        }

        if (!empty($this->filters['date_to'])) {
            $query->whereDate('quotation_date', '<=', $this->filters['date_to']);
        }

        return $query->orderBy('quotation_date', 'desc')->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'Quotation No',
            'Date',
            'Customer Name',
            'Customer Phone',
            'Company',
            'Status',
            'Items Count',
            'Subtotal',
            'Grand Total',
            'Created At',
        ];
    }

    /**
     * @param mixed $salesEntry
     * @return array
     */
    public function map($salesEntry): array
    {
        return [
            $salesEntry->quotation_number ?? 'N/A',
            $salesEntry->quotation_date ? $salesEntry->quotation_date->format('d-m-Y') : 'N/A',
            optional($salesEntry->customer)->name ?? 'N/A',
            optional($salesEntry->customer)->phone ?? 'N/A',
            optional($salesEntry->company)->name ?? 'N/A',
            ucfirst($salesEntry->status ?? 'pending'),
            $salesEntry->items ? $salesEntry->items->count() : 0,
            number_format($salesEntry->subtotal ?? 0, 2),
            number_format($salesEntry->calculated_net_amount ?? $salesEntry->grand_total ?? $salesEntry->total_amount ?? 0, 2),
            $salesEntry->created_at ? $salesEntry->created_at->format('d-m-Y H:i') : 'N/A',
        ];
    }
}
