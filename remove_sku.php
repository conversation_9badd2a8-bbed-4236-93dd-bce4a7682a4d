<?php
/**
 * SKU Removal Script
 * This script will remove the SKU field from the products table and all related code
 */

echo "🗑️  Starting SKU Removal Process...\n";
echo "===================================\n\n";

// Change to Laravel directory if needed
$laravelPath = __DIR__;
if (!file_exists($laravelPath . '/artisan')) {
    echo "❌ Error: artisan file not found. Make sure you're in the Laravel root directory.\n";
    exit(1);
}

echo "📋 What will be removed:\n";
echo "• SKU column from products table\n";
echo "• SKU unique constraint and index\n";
echo "• SKU validation rules from controllers\n";
echo "• SKU fields from all forms and views\n";
echo "• SKU generation logic from import commands\n\n";

// Ask for confirmation
echo "⚠️  WARNING: This will permanently remove the SKU field from your database!\n";
echo "Make sure you have a backup before proceeding.\n\n";

$confirmation = readline("Are you sure you want to continue? (yes/no): ");

if (strtolower($confirmation) !== 'yes') {
    echo "❌ Operation cancelled.\n";
    exit(0);
}

echo "\n🚀 Running migration to remove SKU column...\n";

// Run the migration
$command = 'php artisan migrate';
echo "Running: {$command}\n\n";

$output = [];
$returnCode = 0;
exec($command . ' 2>&1', $output, $returnCode);

// Display output
foreach ($output as $line) {
    echo $line . "\n";
}

echo "\n===================================\n";
if ($returnCode === 0) {
    echo "✅ SKU removal completed successfully!\n\n";
    
    echo "📝 Summary of changes made:\n";
    echo "✅ Removed SKU column from products table\n";
    echo "✅ Removed SKU from Product model fillable array\n";
    echo "✅ Removed SKU validation from ProductController\n";
    echo "✅ Removed SKU validation from MasterController\n";
    echo "✅ Removed SKU column from product index view\n";
    echo "✅ Removed SKU field from create/edit forms\n";
    echo "✅ Removed SKU from product show view\n";
    echo "✅ Removed SKU from category show view\n";
    echo "✅ Removed SKU generation from import commands\n";
    echo "✅ Updated database seeders\n\n";
    
    echo "🎯 Next steps:\n";
    echo "1. Test your product CRUD operations\n";
    echo "2. Run your import commands to verify they work\n";
    echo "3. Check all product-related pages in your application\n";
    echo "4. Update any custom code that might reference SKU\n\n";
    
    echo "🎉 Your application is now SKU-free!\n";
} else {
    echo "❌ Migration failed with return code: {$returnCode}\n";
    echo "Please check the error messages above and fix any issues.\n";
    exit(1);
}

echo "\nDone! 🎯\n";
?>
