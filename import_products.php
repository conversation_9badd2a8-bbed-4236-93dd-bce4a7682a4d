<?php
/**
 * Quick Product Import Script
 * Run this file to import all steel products
 */

echo "🚀 Starting Product Import...\n";
echo "================================\n\n";

// Change to Laravel directory if needed
$laravelPath = __DIR__;
if (!file_exists($laravelPath . '/artisan')) {
    echo "❌ Error: artisan file not found. Make sure you're in the Laravel root directory.\n";
    exit(1);
}

// Run the import command
$command = 'php artisan products:import --force';
echo "Running: {$command}\n\n";

// Execute the command
$output = [];
$returnCode = 0;
exec($command . ' 2>&1', $output, $returnCode);

// Display output
foreach ($output as $line) {
    echo $line . "\n";
}

echo "\n================================\n";
if ($returnCode === 0) {
    echo "✅ Import completed successfully!\n";
    echo "🎉 Check your products page to see all imported items.\n";
} else {
    echo "❌ Import failed with return code: {$returnCode}\n";
    echo "Please check the error messages above.\n";
}

echo "\n📋 What was imported:\n";
echo "• Steel Sheets (15 products)\n";
echo "• GI Pipes (34 products)\n";
echo "• MS Angles (97 products)\n";
echo "• MS Beams (20 products)\n";
echo "• MS Binding Wire (2 products)\n";
echo "• MS Channels (21 products)\n";
echo "• MS Chequered Plates (7 products)\n";
echo "• MS Flats (158 products)\n";
echo "• MS Gate Channels (3 products)\n";
echo "• MS H Beams (10 products)\n";
echo "• MS Pipes - All Sizes (329 products)\n";
echo "• MS Plates (24 products)\n";
echo "• MS Round Bars (30 products)\n";
echo "• MS Square Bars (21 products)\n";
echo "• TMT Bars & TMT Prime Bars (18 products)\n";
echo "• UB Sections (2 products)\n";
echo "• Wire Rod (1 product)\n";
echo "• Total: 792 products\n\n";

echo "🔧 Next steps:\n";
echo "1. Go to your admin panel\n";
echo "2. Navigate to Products section\n";
echo "3. Update prices and stock quantities as needed\n";
echo "4. Set up proper categories if required\n\n";

echo "Done! 🎯\n";
?>
